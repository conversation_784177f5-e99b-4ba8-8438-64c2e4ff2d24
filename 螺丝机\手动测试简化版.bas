'===============================================================================
'                        手动测试简化版 - 不依赖输入电平
'===============================================================================

'变量定义
DIM original_zset

PRINT "==============================================================================="
PRINT "                        两轴插补手动测试程序"
PRINT "==============================================================================="
PRINT "程序功能：手动执行各种插补测试，不依赖输入电平"
PRINT "使用方法：在命令行直接调用测试函数"
PRINT "==============================================================================="

'系统初始化
PRINT "正在初始化系统..."

'设置VP_SPEED显示单轴速度
original_zset = SYSTEM_ZSET
SYSTEM_ZSET = original_zset AND (NOT 1)
PRINT "设置VP_SPEED显示单轴速度"

'设置基础轴组
BASE(0, 1)
PRINT "设置轴组：BASE(0, 1)"

'清零坐标
DPOS = 0, 0
PRINT "坐标清零：DPOS = 0, 0"

'设置脉冲当量
UNITS = 100, 100
PRINT "脉冲当量：UNITS = 100, 100"

'设置运动参数
SPEED = 100, 100
ACCEL = 1000, 1000
DECEL = 1000, 1000
PRINT "运动参数：SPEED=100, ACCEL=1000, DECEL=1000"

'设置平滑参数
SRAMP = 50, 50
VP_MODE = 7, 7
PRINT "平滑参数：SRAMP=50, VP_MODE=7"

PRINT "系统初始化完成！"
PRINT ""

'设置示波器
TRIGGER
PRINT "示波器已触发，监控信号："
PRINT "VP_SPEED(0) - X轴单轴速度"
PRINT "VP_SPEED(1) - Y轴单轴速度"
PRINT ""

PRINT "==============================================================================="
PRINT "                        可用的测试命令"
PRINT "==============================================================================="
PRINT "直接在命令行输入以下命令执行测试："
PRINT ""
PRINT "基础测试："
PRINT "CALL Test1    ' 两轴直线插补测试"
PRINT "CALL Test2    ' 两轴圆弧插补测试"
PRINT ""
PRINT "连续插补对比："
PRINT "CALL Test3    ' 非连续插补测试（MERGE=OFF）"
PRINT "CALL Test4    ' 连续插补测试（MERGE=ON）"
PRINT ""
PRINT "前瞻功能测试："
PRINT "CALL Test5    ' 前瞻拐角减速测试"
PRINT "CALL Test6    ' 自动倒角测试"
PRINT "CALL Test7    ' 组合前瞻测试"
PRINT ""
PRINT "综合测试："
PRINT "CALL Test8    ' 复杂轨迹测试"
PRINT "CALL TestAll  ' 运行所有测试"
PRINT ""
PRINT "系统功能："
PRINT "CALL ShowStatus    ' 显示系统状态"
PRINT "CALL ResetSystem   ' 重置系统"
PRINT "==============================================================================="

'等待用户命令
PRINT "程序就绪，请输入测试命令..."
PRINT ""

'简单的状态监控循环
WHILE 1
    DELAY 1000
    '可以在这里添加状态监控代码
WEND

END

'================ 测试函数定义 ================

'测试1：两轴直线插补
SUB Test1
    PRINT "==============================================================================="
    PRINT "                        测试1：两轴直线插补"
    PRINT "==============================================================================="
    PRINT "从A点(0,0)运动到B点(100,100)"
    PRINT "理论计算：合成距离141.42，各轴速度70.71"
    
    DPOS = 0, 0
    FORCE_SPEED = 100
    TRIGGER
    PRINT "开始执行直线插补：MOVE(100, 100)"
    MOVE(100, 100)
    WAIT IDLE(0)
    WAIT IDLE(1)
    PRINT "直线插补完成，当前位置：(", DPOS(0), ", ", DPOS(1), ")"
    PRINT "测试1完成"
    PRINT ""
END SUB

'测试2：两轴圆弧插补
SUB Test2
    PRINT "==============================================================================="
    PRINT "                        测试2：两轴圆弧插补"
    PRINT "==============================================================================="
    PRINT "三点圆弧插补，逆时针方向"
    
    MOVEABS(100, 100)
    WAIT IDLE(0)
    WAIT IDLE(1)
    FORCE_SPEED = 80
    TRIGGER
    PRINT "开始执行圆弧插补：MOVECIRC2ABS(50, 150, 0, 100)"
    MOVECIRC2ABS(50, 150, 0, 100)
    WAIT IDLE(0)
    WAIT IDLE(1)
    PRINT "圆弧插补完成，当前位置：(", DPOS(0), ", ", DPOS(1), ")"
    PRINT "测试2完成"
    PRINT ""
END SUB

'测试3：非连续插补
SUB Test3
    PRINT "==============================================================================="
    PRINT "                        测试3：非连续插补（MERGE=OFF）"
    PRINT "==============================================================================="
    PRINT "四段直线插补组成矩形，观察停顿"
    
    MOVEABS(0, 0)
    WAIT IDLE(0)
    WAIT IDLE(1)
    MERGE = OFF
    CORNER_MODE = 0
    FORCE_SPEED = 100
    TRIGGER
    
    PRINT "第一段：(0,0) → (100,0)"
    MOVE(100, 0)
    WAIT IDLE(0)
    WAIT IDLE(1)
    
    PRINT "第二段：(100,0) → (100,100)"
    MOVE(0, 100)
    WAIT IDLE(0)
    WAIT IDLE(1)
    
    PRINT "第三段：(100,100) → (0,100)"
    MOVE(-100, 0)
    WAIT IDLE(0)
    WAIT IDLE(1)
    
    PRINT "第四段：(0,100) → (0,0)"
    MOVE(0, -100)
    WAIT IDLE(0)
    WAIT IDLE(1)
    
    PRINT "非连续插补完成，观察：每段之间有停顿"
    PRINT "测试3完成"
    PRINT ""
END SUB

'测试4：连续插补
SUB Test4
    PRINT "==============================================================================="
    PRINT "                        测试4：连续插补（MERGE=ON）"
    PRINT "==============================================================================="
    PRINT "四段直线插补组成矩形，连续执行"
    
    MOVEABS(0, 0)
    WAIT IDLE(0)
    WAIT IDLE(1)
    MERGE = ON
    CORNER_MODE = 0
    FORCE_SPEED = 100
    TRIGGER
    
    PRINT "连续发送四段插补指令："
    MOVE(100, 0)
    MOVE(0, 100)
    MOVE(-100, 0)
    MOVE(0, -100)
    
    WAIT IDLE(0)
    WAIT IDLE(1)
    MERGE = OFF
    
    PRINT "连续插补完成，观察：段间连续但拐角有冲击"
    PRINT "测试4完成"
    PRINT ""
END SUB

'测试5：前瞻拐角减速
SUB Test5
    PRINT "==============================================================================="
    PRINT "                        测试5：前瞻拐角减速（CORNER_MODE=2）"
    PRINT "==============================================================================="
    PRINT "拐角处按比例减速"
    
    MOVEABS(0, 0)
    WAIT IDLE(0)
    WAIT IDLE(1)
    MERGE = ON
    CORNER_MODE = 2
    DECEL_ANGLE = 30 * (PI/180)
    STOP_ANGLE = 90 * (PI/180)
    FORCE_SPEED = 100
    TRIGGER
    
    MOVE(100, 0)
    MOVE(0, 100)
    MOVE(-100, 0)
    MOVE(0, -100)
    
    WAIT IDLE(0)
    WAIT IDLE(1)
    MERGE = OFF
    CORNER_MODE = 0
    
    PRINT "前瞻拐角减速完成，观察：拐角处减速但不停"
    PRINT "测试5完成"
    PRINT ""
END SUB

'测试6：自动倒角
SUB Test6
    PRINT "==============================================================================="
    PRINT "                        测试6：自动倒角（CORNER_MODE=32）"
    PRINT "==============================================================================="
    PRINT "拐角处轨迹倒角，保持高速"
    
    MOVEABS(0, 0)
    WAIT IDLE(0)
    WAIT IDLE(1)
    MERGE = ON
    CORNER_MODE = 32
    ZSMOOTH = 10
    FORCE_SPEED = 100
    TRIGGER
    
    MOVE(100, 0)
    MOVE(0, 100)
    MOVE(-100, 0)
    MOVE(0, -100)
    
    WAIT IDLE(0)
    WAIT IDLE(1)
    MERGE = OFF
    CORNER_MODE = 0
    
    PRINT "自动倒角完成，观察：拐角处圆弧倒角"
    PRINT "测试6完成"
    PRINT ""
END SUB

'显示系统状态
SUB ShowStatus
    PRINT "==============================================================================="
    PRINT "                        系统状态信息"
    PRINT "==============================================================================="
    PRINT "控制器型号：", ?*model
    PRINT "固件版本：", ?*version
    PRINT "当前位置：(", DPOS(0), ", ", DPOS(1), ")"
    PRINT "轴状态：AXIS(0)=", AXIS(0), " AXIS(1)=", AXIS(1)
    PRINT "输入状态：IN0=", IN(0), " IN1=", IN(1), " IN2=", IN(2), " IN3=", IN(3)
    PRINT "输出状态：OUT0=", OUT(0), " OUT1=", OUT(1), " OUT2=", OUT(2), " OUT3=", OUT(3)
    PRINT "SYSTEM_ZSET=", SYSTEM_ZSET
    PRINT "==============================================================================="
END SUB

'重置系统
SUB ResetSystem
    PRINT "正在重置系统..."
    DPOS = 0, 0
    MERGE = OFF
    CORNER_MODE = 0
    FORCE_SPEED = 100
    PRINT "系统重置完成"
END SUB
