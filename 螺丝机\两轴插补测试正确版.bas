'===============================================================================
'                        两轴插补测试程序 - 正确的RTBasic版本
'                        基于官方例程结构编写，支持输入电平触发
'===============================================================================

'================ 全局变量定义 ================
DIM test_mode                           ' 测试模式选择
DIM original_zset                       ' 保存原始SYSTEM_ZSET设置
DIM last_input_state(8)                 ' 记录上次输入状态
DIM input_pin_base                      ' 输入引脚起始编号
DIM i                                   ' 循环变量

'================ 主程序（任务0）================
'设置输入引脚起始编号
input_pin_base = 0

'系统初始化
PRINT "==============================================================================="
PRINT "                        两轴插补测试程序初始化"
PRINT "==============================================================================="

'设置VP_SPEED显示单轴速度
original_zset = SYSTEM_ZSET
SYSTEM_ZSET = original_zset AND (NOT 1)  ' 清除bit0，VP_SPEED使用单轴速度
PRINT "设置VP_SPEED显示单轴速度，便于监控各轴分速度"

'设置基础轴组：X轴(0)和Y轴(1)
BASE(0, 1)                              ' X轴为主轴，Y轴为从轴
PRINT "设置轴组：BASE(0, 1) - X轴主轴，Y轴从轴"

'清零坐标
DPOS = 0, 0                             ' 坐标清零
PRINT "坐标清零：DPOS = 0, 0"

'设置脉冲当量
UNITS = 100, 100                        ' 脉冲当量100
PRINT "脉冲当量：UNITS = 100, 100"

'设置运动参数
SPEED = 100, 100                        ' 主轴速度100units/s
ACCEL = 1000, 1000                      ' 加速度1000units/s²
DECEL = 1000, 1000                      ' 减速度1000units/s²
PRINT "运动参数：SPEED=100, ACCEL=1000, DECEL=1000"

'设置平滑参数
SRAMP = 50, 50                          ' S曲线时间50ms
VP_MODE = 7, 7                          ' SS曲线，最平滑
PRINT "平滑参数：SRAMP=50, VP_MODE=7"

'初始化输入状态
FOR i = 0 TO 7
    last_input_state(i) = IN(input_pin_base + i)
NEXT i

'显示输入引脚分配
PRINT "==============================================================================="
PRINT "                        输入引脚分配"
PRINT "==============================================================================="
PRINT "IN", input_pin_base + 0, " - 两轴直线插补测试"
PRINT "IN", input_pin_base + 1, " - 两轴圆弧插补测试"
PRINT "IN", input_pin_base + 2, " - 非连续插补测试"
PRINT "IN", input_pin_base + 3, " - 连续插补测试"
PRINT "IN", input_pin_base + 4, " - 前瞻拐角减速测试"
PRINT "IN", input_pin_base + 5, " - 自动倒角测试"
PRINT "IN", input_pin_base + 6, " - 组合前瞻测试"
PRINT "IN", input_pin_base + 7, " - 复杂轨迹测试"
PRINT "==============================================================================="
PRINT "程序进入输入电平监控模式，等待输入信号触发测试..."
PRINT ""

'主循环 - 持续监控输入电平
WHILE 1
    '检测输入上升沿
    FOR i = 0 TO 7
        IF (IN(input_pin_base + i) = 1) AND (last_input_state(i) = 0) THEN
            PRINT "检测到IN", input_pin_base + i, "上升沿，启动测试", i + 1
            
            '自动设置示波器
            TRIGGER
            PRINT "示波器监控设置："
            PRINT "VP_SPEED(0) - X轴单轴速度（蓝色，刻度100，偏移0）"
            PRINT "VP_SPEED(1) - Y轴单轴速度（红色，刻度100，偏移-60）"
            PRINT ""
            
            '执行对应测试
            IF i = 0 THEN
                '测试1：两轴直线插补
                PRINT "==============================================================================="
                PRINT "                        测试1：两轴直线插补"
                PRINT "==============================================================================="
                PRINT "从A点(0,0)运动到B点(100,100)"
                PRINT "插补合成运动距离：√(100²+100²) = 141.42 units"
                PRINT "X轴实际速度：100×100/141.42 = 70.71 units/s"
                PRINT "Y轴实际速度：100×100/141.42 = 70.71 units/s"
                
                DPOS = 0, 0
                FORCE_SPEED = 100
                PRINT "开始执行直线插补：MOVE(100, 100)"
                MOVE(100, 100)
                WAIT IDLE(0)
                WAIT IDLE(1)
                PRINT "直线插补完成，当前位置：(", DPOS(0), ", ", DPOS(1), ")"
                
            ELSEIF i = 1 THEN
                '测试2：两轴圆弧插补
                PRINT "==============================================================================="
                PRINT "                        测试2：两轴圆弧插补"
                PRINT "==============================================================================="
                PRINT "在XY平面第一象限走一段逆时针圆弧"
                
                MOVEABS(100, 100)
                WAIT IDLE(0)
                WAIT IDLE(1)
                FORCE_SPEED = 80
                PRINT "开始执行圆弧插补：MOVECIRC2ABS(50, 150, 0, 100)"
                MOVECIRC2ABS(50, 150, 0, 100)
                WAIT IDLE(0)
                WAIT IDLE(1)
                PRINT "圆弧插补完成，当前位置：(", DPOS(0), ", ", DPOS(1), ")"
                
            ELSEIF i = 2 THEN
                '测试3：非连续插补
                PRINT "==============================================================================="
                PRINT "                        测试3：非连续插补（MERGE=OFF）"
                PRINT "==============================================================================="
                PRINT "执行四段直线插补组成矩形轨迹，观察每段之间的停顿"
                
                MOVEABS(0, 0)
                WAIT IDLE(0)
                WAIT IDLE(1)
                MERGE = OFF
                CORNER_MODE = 0
                FORCE_SPEED = 100
                
                PRINT "第一段：(0,0) → (100,0)"
                MOVE(100, 0)
                WAIT IDLE(0)
                WAIT IDLE(1)
                
                PRINT "第二段：(100,0) → (100,100)"
                MOVE(0, 100)
                WAIT IDLE(0)
                WAIT IDLE(1)
                
                PRINT "第三段：(100,100) → (0,100)"
                MOVE(-100, 0)
                WAIT IDLE(0)
                WAIT IDLE(1)
                
                PRINT "第四段：(0,100) → (0,0)"
                MOVE(0, -100)
                WAIT IDLE(0)
                WAIT IDLE(1)
                
                PRINT "非连续插补完成，观察：每段之间都有明显停顿"
                
            ELSEIF i = 3 THEN
                '测试4：连续插补
                PRINT "==============================================================================="
                PRINT "                        测试4：连续插补（MERGE=ON）"
                PRINT "==============================================================================="
                PRINT "执行四段直线插补组成矩形轨迹，观察连续性"
                
                MOVEABS(0, 0)
                WAIT IDLE(0)
                WAIT IDLE(1)
                MERGE = ON
                CORNER_MODE = 0
                FORCE_SPEED = 100
                
                PRINT "连续发送四段插补指令："
                MOVE(100, 0)
                MOVE(0, 100)
                MOVE(-100, 0)
                MOVE(0, -100)
                
                WAIT IDLE(0)
                WAIT IDLE(1)
                MERGE = OFF
                
                PRINT "连续插补完成，观察：各段之间连续，但拐角处仍有冲击"
                
            ELSEIF i = 4 THEN
                '测试5：前瞻拐角减速
                PRINT "==============================================================================="
                PRINT "                        测试5：前瞻拐角减速（CORNER_MODE=2）"
                PRINT "==============================================================================="
                PRINT "开启连续插补+自动拐角减速，拐角处按比例减速"
                
                MOVEABS(0, 0)
                WAIT IDLE(0)
                WAIT IDLE(1)
                MERGE = ON
                CORNER_MODE = 2
                DECEL_ANGLE = 30 * (PI/180)
                STOP_ANGLE = 90 * (PI/180)
                FORCE_SPEED = 100
                
                MOVE(100, 0)
                MOVE(0, 100)
                MOVE(-100, 0)
                MOVE(0, -100)
                
                WAIT IDLE(0)
                WAIT IDLE(1)
                MERGE = OFF
                CORNER_MODE = 0
                
                PRINT "前瞻拐角减速完成，观察：拐角处按比例减速"
                
            ELSEIF i = 5 THEN
                '测试6：自动倒角
                PRINT "==============================================================================="
                PRINT "                        测试6：自动倒角（CORNER_MODE=32）"
                PRINT "==============================================================================="
                PRINT "改变运动轨迹，拐角处自动倒角，不降低速度"
                
                MOVEABS(0, 0)
                WAIT IDLE(0)
                WAIT IDLE(1)
                MERGE = ON
                CORNER_MODE = 32
                ZSMOOTH = 10
                FORCE_SPEED = 100
                
                MOVE(100, 0)
                MOVE(0, 100)
                MOVE(-100, 0)
                MOVE(0, -100)
                
                WAIT IDLE(0)
                WAIT IDLE(1)
                MERGE = OFF
                CORNER_MODE = 0
                
                PRINT "自动倒角完成，观察：拐角处有圆弧倒角，轨迹平滑"
                
            ELSEIF i = 6 THEN
                '测试7：组合前瞻
                PRINT "==============================================================================="
                PRINT "                        测试7：组合前瞻（CORNER_MODE=2+32）"
                PRINT "==============================================================================="
                PRINT "同时使用拐角减速和自动倒角"
                
                MOVEABS(0, 0)
                WAIT IDLE(0)
                WAIT IDLE(1)
                MERGE = ON
                CORNER_MODE = 2 + 32
                DECEL_ANGLE = 25 * (PI/180)
                STOP_ANGLE = 80 * (PI/180)
                ZSMOOTH = 8
                FORCE_SPEED = 100
                
                MOVE(100, 0)
                MOVE(0, 100)
                MOVE(-100, 0)
                MOVE(0, -100)
                
                WAIT IDLE(0)
                WAIT IDLE(1)
                MERGE = OFF
                CORNER_MODE = 0
                
                PRINT "组合前瞻完成，观察：拐角处既有倒角又有减速"
                
            ELSEIF i = 7 THEN
                '测试8：复杂轨迹
                PRINT "==============================================================================="
                PRINT "                        测试8：复杂轨迹连续插补"
                PRINT "==============================================================================="
                PRINT "直线插补+圆弧插补的组合轨迹"
                
                MOVEABS(0, 0)
                WAIT IDLE(0)
                WAIT IDLE(1)
                MERGE = ON
                CORNER_MODE = 32
                ZSMOOTH = 5
                FORCE_SPEED = 80
                
                PRINT "第1段：直线 (0,0) → (50,0)"
                MOVE(50, 0)
                PRINT "第2段：圆弧 (50,0) → (75,25) → (100,0)"
                MOVECIRC2ABS(75, 25, 100, 0)
                PRINT "第3段：直线 (100,0) → (100,50)"
                MOVE(0, 50)
                PRINT "第4段：圆弧 (100,50) → (75,75) → (50,50)"
                MOVECIRC2ABS(75, 75, 50, 50)
                PRINT "第5段：直线 (50,50) → (0,50)"
                MOVE(-50, 0)
                PRINT "第6段：直线 (0,50) → (0,0)"
                MOVE(0, -50)
                
                WAIT IDLE(0)
                WAIT IDLE(1)
                MERGE = OFF
                CORNER_MODE = 0
                
                PRINT "复杂轨迹完成，观察：直线和圆弧之间完全连续"
            ENDIF
            
            PRINT "测试", i + 1, "执行完成"
            PRINT ""
        ENDIF
        
        last_input_state(i) = IN(input_pin_base + i)
    NEXT i
    
    DELAY 50                            ' 延时50ms，避免过于频繁检测
WEND

END
