'=============================================================================
' Zbasic 回原点演示程序 - 基于正运动官方例程
' 深圳市正运动技术有限公司
' 演示各种回零方式和回零状态检测
'=============================================================================

'================ 全局变量定义 ================
GLOBAL gv_AxisMax                    '轴数量
GLOBAL ga_AxisNum(3)                 '轴号定义
GLOBAL gv_Status                     '系统状态：0-停止，1-运行中，2-已完成
GLOBAL ga_StatusAxis(3)              '各轴状态：0-未回零，1-回零中，2-已回零
GLOBAL ga_HomeMode(3)                '回零方式
GLOBAL ga_HomePri(3)                 '回零优先级
GLOBAL ga_HomeEnable(3)              '回零使能
GLOBAL ga_Warning(20)                '警告信息

'常量定义
CONST MAX_AXES = 3       '最大轴数
CONST HOME_SPEED = 100   '回零速度
CONST CREEP_SPEED = 20   '爬行速度

'================ 主程序 ================
MAIN_INIT()     '系统初始化子程序，只执行一次

PRINT "=== 正运动回原点演示程序 ==="
PRINT "基于官方例程架构，支持多种回零模式"

WHILE 1         '主循环，程序的"心脏"
    MAIN_SCAN() '主程序扫描子程序
WEND

'================ 初始化子程序 ================
GLOBAL SUB MAIN_INIT()
    '--- 变量初始化 ---
    gv_AxisMax = 3
    ga_AxisNum(0) = 0                    'X轴（0轴）
    ga_AxisNum(1) = 1                    'Y轴（1轴）
    ga_AxisNum(2) = 2                    'Z轴（2轴）
    gv_Status = 0

    '初始化轴状态数组
    FOR i = 0 TO 2
        ga_StatusAxis(i) = 0
        ga_HomeMode(i) = 0               '默认回零模式0（正向找原点）
        ga_HomePri(i) = 0                '默认优先级0
        ga_HomeEnable(i) = 0             '默认使能回零
    NEXT

    ga_Warning = ""

    '--- 轴参数初始化 ---
    CALL AxisInit()

    PRINT "系统初始化完成"
    PRINT "轴数量：", gv_AxisMax
    PRINT "回零速度：", HOME_SPEED
    PRINT "爬行速度：", CREEP_SPEED
END SUB

'================ 主程序扫描子程序 ================
GLOBAL SUB MAIN_SCAN()
    '--- 扫描输入信号事件 ---
    IF SCAN_EVENT(IN(0)) > 0 THEN       '单轴回零
        CALL deal_home_axis(0)
    ENDIF

    IF SCAN_EVENT(IN(1)) > 0 THEN       '多轴顺序回零
        CALL deal_home()
    ENDIF

    IF SCAN_EVENT(IN(2)) > 0 THEN       '多轴同时回零
        CALL deal_home_simultaneous()
    ENDIF

    IF SCAN_EVENT(IN(3)) > 0 THEN       '状态检查
        CALL CheckHomeStatus()
    ENDIF

    '紧急停止
    IF IN(7) = ON THEN
        CALL deal_stop()
    ENDIF

    '状态指示灯
    IF gv_Status = 1 THEN               '回零中
        OP(0, TICKS AND 1)              '红灯闪烁
        OP(1, OFF)
    ELSEIF gv_Status = 2 THEN           '回零完成
        OP(0, OFF)
        OP(1, ON)                       '绿灯亮
    ELSE                                '待机
        OP(0, OFF)
        OP(1, OFF)
    ENDIF

    DELAY(50)
END SUB

'================ 轴参数初始化 ================
GLOBAL SUB AxisInit()
    BASE(ga_AxisNum(0), ga_AxisNum(1), ga_AxisNum(2))
    ATYPE = 1, 1, 1                      '脉冲轴
    UNITS = 1000, 1000, 1000             '脉冲当量
    ACCEL = 1000, 1000, 1000             '加速度
    DECEL = 1000, 1000, 1000             '减速度
    SPEED = HOME_SPEED, HOME_SPEED, HOME_SPEED    '回零速度
    CREEP = CREEP_SPEED, CREEP_SPEED, CREEP_SPEED '爬行速度
    HOMEWAIT = 20, 20, 20                '反找等待时间(ms)

    '原点输入设置
    DATUM_IN = 8, 9, 10                  '原点输入：IN8, IN9, IN10
    INVERT_IN(8, ON)                     '反转原点信号
    INVERT_IN(9, ON)
    INVERT_IN(10, ON)

    '限位输入设置（可选）
    FWD_IN = 11, 12, 13                  '正向限位
    REV_IN = 14, 15, 16                  '负向限位
    INVERT_IN(11, ON)
    INVERT_IN(12, ON)
    INVERT_IN(13, ON)
    INVERT_IN(14, ON)
    INVERT_IN(15, ON)
    INVERT_IN(16, ON)

    '关闭所有输出
    FOR i = 0 TO 7
        OP(i, OFF)
    NEXT
END SUB

'================ 停止按钮处理 ================
GLOBAL SUB deal_stop()
    STOPTASK 2
    RAPIDSTOP(2)
    gv_Status = 0

    FOR j = 0 TO gv_AxisMax - 1
        IF ga_StatusAxis(j) = 1 THEN
            ga_StatusAxis(j) = 0
        ENDIF
    NEXT

    PRINT "紧急停止！所有轴停止运动"
END SUB

'================ 单轴回零处理 ================
GLOBAL SUB deal_home_axis(num)
    IF gv_Status <> 1 THEN              '判断系统状态不在回零中
        STOPTASK 2
        RUNTASK 2, task_home_axis(num)
    ELSEIF gv_Status = 1 THEN
        PRINT "系统忙，无法回零"
        ga_Warning = "系统忙，无法回零"
    ENDIF
END SUB

GLOBAL SUB task_home_axis(num)
    PRINT "=== 单轴回零：轴", num, " ==="

    gv_Status = 1                        '状态切换成回零中
    ga_StatusAxis(ga_AxisNum(num)) = 1

    IF ga_HomeEnable(ga_AxisNum(num)) = 0 THEN
        BASE(ga_AxisNum(num))

        '根据回零模式执行不同的回零指令
        IF ga_HomeMode(num) = 0 THEN     '正向找原点
            DATUM(3)
        ELSEIF ga_HomeMode(num) = 1 THEN '负向找原点
            DATUM(4)
        ELSEIF ga_HomeMode(num) = 2 THEN '正向找原点+限位
            DATUM(13)
        ELSEIF ga_HomeMode(num) = 3 THEN '负向找原点+限位
            DATUM(14)
        ELSE
            DATUM(3)                     '默认使用模式3
        ENDIF
    ELSE
        gv_Status = 0
        ga_StatusAxis(ga_AxisNum(num)) = 0
        RETURN
    ENDIF

    '等待回零完成
    WAIT UNTIL IDLE(ga_AxisNum(num)) = -1
    DELAY(10)

    '检查回零结果
    IF AXISSTATUS(ga_AxisNum(num)) = 0 THEN
        DPOS(ga_AxisNum(num)) = 0        '清零位置
        MPOS(ga_AxisNum(num)) = 0
        ga_StatusAxis(ga_AxisNum(num)) = 2
        PRINT "轴", num, "回零成功！位置：", DPOS(ga_AxisNum(num))
    ELSE
        ga_StatusAxis(ga_AxisNum(num)) = 0
        PRINT "轴", num, "回零失败！状态：", HEX(AXISSTATUS(ga_AxisNum(num)))
        CALL AnalyzeAxisError(ga_AxisNum(num), AXISSTATUS(ga_AxisNum(num)))
    ENDIF

    gv_Status = 0                        '状态切换成待机
END SUB

'================ 多轴顺序回零处理 ================
GLOBAL SUB deal_home()
    IF gv_Status <> 1 THEN              '判断系统状态不在回零中
        STOPTASK 2
        RUNTASK 2, task_home()
    ELSEIF gv_Status = 1 THEN
        PRINT "系统忙，无法回零"
        ga_Warning = "系统忙，无法回零"
    ENDIF
END SUB

GLOBAL SUB task_home()
    PRINT "=== 多轴顺序回零演示 ==="

    LOCAL flag_i, flag_j, flag_k, flag_l '定义判断回零优先级
    LOCAL flag_mode                      '回零模式
    flag_k = 0
    gv_Status = 1                        '状态切换成回零中

    '设置所有轴状态为回零中
    FOR i = 0 TO gv_AxisMax - 1
        IF ga_HomeEnable(i) = 0 THEN
            ga_StatusAxis(i) = 1
        ENDIF
    NEXT

    '按优先级顺序回零
    FOR flag_i = 0 TO 2                  '优先级0-2
        FOR flag_j = 0 TO gv_AxisMax - 1
            IF ga_HomeEnable(flag_j) = 0 THEN    '检查是否启用回零
                IF ga_HomePri(flag_j) = flag_i THEN  '当前轴的回零优先级与当前优先级对应
                    BASE(flag_j)

                    '判断回零模式
                    IF ga_HomeMode(flag_j) = 0 THEN
                        flag_mode = 3
                    ELSEIF ga_HomeMode(flag_j) = 1 THEN
                        flag_mode = 4
                    ELSEIF ga_HomeMode(flag_j) = 2 THEN
                        flag_mode = 13
                    ELSEIF ga_HomeMode(flag_j) = 3 THEN
                        flag_mode = 14
                    ELSE
                        flag_mode = 3    '默认模式
                    ENDIF

                    DATUM(flag_mode)
                    flag_k = SET_BIT(flag_j, flag_k)  '设置完成标志位
                    PRINT "轴", flag_j, "开始回零，模式：", flag_mode
                ENDIF
            ENDIF
        NEXT

        '等待当前优先级的轴回零完成
        WAIT UNTIL IDLE(0) = -1 AND IDLE(1) = -1 AND IDLE(2) = -1

        '检查回零结果
        FOR flag_l = 0 TO gv_AxisMax - 1
            IF READ_BIT2(flag_l, flag_k) THEN
                IF AXISSTATUS(flag_l) = 0 THEN
                    ga_StatusAxis(flag_l) = 2
                    DPOS(flag_l) = 0
                    MPOS(flag_l) = 0
                    PRINT "轴", flag_l, "回零成功"
                ELSE
                    ga_StatusAxis(flag_l) = 0
                    PRINT "轴", flag_l, "回零失败，状态：", HEX(AXISSTATUS(flag_l))
                ENDIF
            ENDIF
        NEXT
    NEXT

    gv_Status = 2                        '状态切换成已完成
    PRINT "多轴顺序回零完成！"
END SUB

'================ 多轴同时回零处理 ================
GLOBAL SUB deal_home_simultaneous()
    IF gv_Status <> 1 THEN              '判断系统状态不在回零中
        STOPTASK 2
        RUNTASK 2, task_home_simultaneous()
    ELSEIF gv_Status = 1 THEN
        PRINT "系统忙，无法回零"
        ga_Warning = "系统忙，无法回零"
    ENDIF
END SUB

GLOBAL SUB task_home_simultaneous()
    PRINT "=== 多轴同时回零演示 ==="

    gv_Status = 1                        '状态切换成回零中

    '同时启动所有轴回零
    FOR i = 0 TO gv_AxisMax - 1
        IF ga_HomeEnable(i) = 0 THEN
            ga_StatusAxis(i) = 1
            BASE(i)

            '根据模式执行回零
            IF ga_HomeMode(i) = 0 THEN
                DATUM(3)
            ELSEIF ga_HomeMode(i) = 1 THEN
                DATUM(4)
            ELSEIF ga_HomeMode(i) = 2 THEN
                DATUM(13)
            ELSEIF ga_HomeMode(i) = 3 THEN
                DATUM(14)
            ELSE
                DATUM(3)                 '默认模式
            ENDIF

            PRINT "轴", i, "开始回零..."
        ENDIF
    NEXT

    '等待所有轴回零完成
    WAIT UNTIL IDLE(0) = -1 AND IDLE(1) = -1 AND IDLE(2) = -1

    '检查所有轴的回零结果
    LOCAL home_success
    home_success = 1

    FOR i = 0 TO gv_AxisMax - 1
        IF ga_HomeEnable(i) = 0 THEN
            IF AXISSTATUS(i) = 0 THEN
                ga_StatusAxis(i) = 2
                DPOS(i) = 0
                MPOS(i) = 0
                PRINT "轴", i, "回零成功，位置：", DPOS(i)
            ELSE
                ga_StatusAxis(i) = 0
                PRINT "轴", i, "回零失败，状态：", HEX(AXISSTATUS(i))
                home_success = 0
            ENDIF
        ENDIF
    NEXT

    IF home_success = 1 THEN
        gv_Status = 2                    '所有轴回零成功
        PRINT "所有轴同时回零完成！"
    ELSE
        gv_Status = 0                    '有轴回零失败
        PRINT "部分轴回零失败！"
    ENDIF
END SUB

'================ 回零状态检查 ================
GLOBAL SUB CheckHomeStatus()
    PRINT "=== 回零状态检查 ==="

    FOR i = 0 TO gv_AxisMax - 1
        PRINT "轴", i, "状态检查："
        PRINT "  位置：", DPOS(i)
        PRINT "  编码器位置：", MPOS(i)
        PRINT "  轴状态：", HEX(AXISSTATUS(i))
        PRINT "  运动状态：", IIF(IDLE(i) = -1, "停止", "运动中")
        PRINT "  回零状态：", ga_StatusAxis(i), "(0-未回零,1-回零中,2-已回零)"

        '检查是否在原点附近
        IF ABS(DPOS(i)) < 1 THEN
            PRINT "  原点状态：在原点"
        ELSE
            PRINT "  原点状态：偏离原点", DPOS(i), "单位"
        ENDIF

        '检查原点输入信号
        LOCAL home_input
        home_input = IN(8 + i)
        PRINT "  原点信号：", IIF(home_input = ON, "有效", "无效")

        PRINT ""
    NEXT

    '显示系统状态
    PRINT "系统状态：", gv_Status, "(0-停止,1-运行中,2-已完成)"
    IF gv_Status = 0 THEN
        PRINT "状态说明：待机"
    ELSEIF gv_Status = 1 THEN
        PRINT "状态说明：回零中"
    ELSEIF gv_Status = 2 THEN
        PRINT "状态说明：回零完成"
    ENDIF
END SUB

'================ 轴错误分析子程序 ================
GLOBAL SUB AnalyzeAxisError(axis_num, status_value)
    PRINT "轴", axis_num, "错误分析："

    IF status_value AND 1 THEN
        PRINT "  - 轴错误标志置位"
    ENDIF

    IF status_value AND 2 THEN
        PRINT "  - 碰到正向硬限位"
    ENDIF

    IF status_value AND 4 THEN
        PRINT "  - 碰到负向硬限位"
    ENDIF

    IF status_value AND 8 THEN
        PRINT "  - 急停信号有效"
    ENDIF

    IF status_value AND 16 THEN
        PRINT "  - 驱动器报警"
    ENDIF

    '检查原点信号状态
    LOCAL home_signal
    home_signal = IN(8 + axis_num)
    PRINT "  - 原点信号状态：", IIF(home_signal = ON, "有效", "无效")

    '建议解决方案
    PRINT "建议检查："
    PRINT "  1. 原点开关连接和信号"
    PRINT "  2. 限位开关状态"
    PRINT "  3. 驱动器使能和报警"
    PRINT "  4. 轴运动方向设置"
END SUB

'================ 回零成功判断说明 ================
GLOBAL SUB ShowHomeSuccessCheck()
    PRINT "=== 回零成功判断方法 ==="
    PRINT "1. 基础判断：AXISSTATUS = 0 (无错误)"
    PRINT "2. 运动判断：IDLE = -1 (轴已停止)"
    PRINT "3. 位置判断：ABS(DPOS) < 允许误差"
    PRINT "4. 信号判断：原点信号状态检查"
    PRINT ""
    PRINT "标准代码："
    PRINT "DATUM(3)"
    PRINT "WAIT IDLE"
    PRINT "IF AXISSTATUS(axis) = 0 THEN"
    PRINT "    PRINT \"回零成功\""
    PRINT "    DPOS(axis) = 0"
    PRINT "ELSE"
    PRINT "    PRINT \"回零失败\""
    PRINT "ENDIF"
END SUB
