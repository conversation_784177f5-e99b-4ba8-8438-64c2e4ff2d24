'=============================================================================
' AB信号回零示例程序
' 适用于不带Z信号的编码器回零
' 基于正运动官方手册和最佳实践
'=============================================================================

' 全局变量
GLOBAL total_axes        ' 轴数量
GLOBAL ab_home_status(3) ' 各轴AB回零状态：0-未回零,1-回零中,2-已回零,3-失败

' 主程序
total_axes = 3
CALL InitABHomeSystem()

PRINT "=== AB信号回零程序 ==="
PRINT "适用于不带Z信号的编码器"
PRINT ""
PRINT "操作说明："
PRINT "IN0 - 单轴AB回零（轴0）"
PRINT "IN1 - 多轴AB回零"
PRINT "IN2 - 状态检查"
PRINT "IN3 - 信号测试"
PRINT "IN4 - 重复性测试"

WHILE 1
    ' 单轴AB回零
    IF SCAN_EVENT(IN(0)) > 0 THEN
        CALL SingleABHome(0)
    ENDIF
    
    ' 多轴AB回零
    IF SCAN_EVENT(IN(1)) > 0 THEN
        CALL MultiABHome()
    ENDIF
    
    ' 状态检查
    IF SCAN_EVENT(IN(2)) > 0 THEN
        CALL CheckABHomeStatus()
    ENDIF
    
    ' 信号测试
    IF SCAN_EVENT(IN(3)) > 0 THEN
        CALL TestABSignals()
    ENDIF
    
    ' 重复性测试
    IF SCAN_EVENT(IN(4)) > 0 THEN
        CALL ABRepeatabilityTest(0, 5)
    ENDIF
    
    DELAY(50)
WEND
END

' AB信号回零系统初始化
GLOBAL SUB InitABHomeSystem()
    PRINT "初始化AB信号回零系统..."
    
    FOR i = 0 TO total_axes - 1
        BASE(i)
        ATYPE = 4                ' 脉冲方向输出+正交编码器输入
        UNITS = 1000             ' 脉冲当量
        SPEED = 200              ' 回零速度
        CREEP = 20               ' 爬行速度
        ACCEL = 1000             ' 加速度
        DECEL = 1000             ' 减速度
        HOMEWAIT = 20            ' 反找等待时间
        
        ' 信号映射
        DATUM_IN = 8 + i         ' 原点开关：IN8, IN9, IN10
        FWD_IN = 11 + i          ' 正限位：IN11, IN12, IN13
        REV_IN = 14 + i          ' 负限位：IN14, IN15, IN16
        
        ' 信号反转（ZMC系列OFF有效）
        INVERT_IN(8 + i, ON)
        INVERT_IN(11 + i, ON)
        INVERT_IN(14 + i, ON)
        
        ' 编码器设置
        ENCODER_RATIO(1, 1)      ' 1:1比例，根据需要调整
        
        ab_home_status(i) = 0    ' 初始化为未回零
    NEXT
    
    PRINT "AB信号回零系统初始化完成"
    PRINT "ATYPE设置：4（脉冲+正交编码器）"
    PRINT "编码器比例：1:1"
END SUB

' 单轴AB回零
GLOBAL SUB SingleABHome(axis_num)
    PRINT "=== 开始轴", axis_num, "AB信号回零 ==="
    
    BASE(axis_num)
    ab_home_status(axis_num) = 1  ' 设置为回零中
    
    ' 显示回零前状态
    PRINT "回零前状态："
    PRINT "  DPOS位置：", DPOS(axis_num)
    PRINT "  MPOS位置：", MPOS(axis_num)
    PRINT "  编码器状态：", HEX(ENCODER_STATUS(axis_num))
    PRINT "  原点信号：", IN(8 + axis_num)
    
    ' 检查ATYPE设置
    IF ATYPE(axis_num) <> 4 AND ATYPE(axis_num) <> 5 THEN
        PRINT "警告：ATYPE设置可能不正确，当前值：", ATYPE(axis_num)
        PRINT "建议：ATYPE=4（正交编码器）或ATYPE=5（脉冲方向编码器）"
    ENDIF
    
    ' 执行AB信号回零（使用模式3，适合AB信号）
    PRINT "执行回零指令 DATUM(3)..."
    DATUM(3)
    WAIT UNTIL IDLE(axis_num) = -1
    DELAY(10)                    ' 状态稳定延时
    
    ' 检查回零结果
    IF AXISSTATUS(axis_num) = 0 THEN
        ' 回零成功处理
        DPOS(axis_num) = 0       ' 清零脉冲位置
        MPOS(axis_num) = 0       ' 清零编码器位置
        ab_home_status(axis_num) = 2  ' 设置为已回零
        
        PRINT "✓ 轴", axis_num, "AB信号回零成功！"
        PRINT "回零后状态："
        PRINT "  DPOS位置：", DPOS(axis_num)
        PRINT "  MPOS位置：", MPOS(axis_num)
        PRINT "  编码器状态：", HEX(ENCODER_STATUS(axis_num))
    ELSE
        ' 回零失败处理
        ab_home_status(axis_num) = 3  ' 设置为回零失败
        PRINT "✗ 轴", axis_num, "AB信号回零失败！"
        PRINT "错误状态：", HEX(AXISSTATUS(axis_num))
        CALL AnalyzeABHomeError(axis_num)
    ENDIF
    
    PRINT "=== 轴", axis_num, "AB回零完成 ==="
END SUB

' 多轴AB回零
GLOBAL SUB MultiABHome()
    PRINT "=== 开始多轴AB信号回零 ==="
    
    FOR i = 0 TO total_axes - 1
        CALL SingleABHome(i)
        IF ab_home_status(i) <> 2 THEN
            PRINT "多轴AB回零失败，停止在轴", i
            RETURN
        ENDIF
        DELAY(500)  ' 轴间延时
    NEXT
    
    PRINT "✓ 所有轴AB信号回零成功！"
END SUB

' AB回零状态检查
GLOBAL SUB CheckABHomeStatus()
    PRINT "=== AB信号回零状态检查 ==="
    
    FOR i = 0 TO total_axes - 1
        PRINT "轴", i, "详细状态："
        PRINT "  回零状态：", ab_home_status(i), "(0-未回零,1-回零中,2-已回零,3-失败)"
        PRINT "  ATYPE设置：", ATYPE(i)
        PRINT "  DPOS位置：", DPOS(i)
        PRINT "  MPOS位置：", MPOS(i)
        PRINT "  轴状态：", HEX(AXISSTATUS(i))
        PRINT "  编码器状态：", HEX(ENCODER_STATUS(i))
        PRINT "  编码器原始值：", ENCODER(i)
        PRINT "  原点信号：", IN(8 + i)
        
        ' 运动状态
        IF IDLE(i) = -1 THEN
            PRINT "  运动状态：停止"
        ELSE
            PRINT "  运动状态：运动中"
        ENDIF
        
        PRINT ""
    NEXT
END SUB

' AB信号测试
GLOBAL SUB TestABSignals()
    PRINT "=== AB信号动态测试 ==="
    PRINT "测试时间：10秒，请手动转动编码器"
    
    FOR test_time = 1 TO 100  ' 10秒测试
        PRINT "时间:", test_time * 0.1, "s"
        
        FOR i = 0 TO total_axes - 1
            DIM encoder_value, mpos_value, encoder_stat
            encoder_value = ENCODER(i)
            mpos_value = MPOS(i)
            encoder_stat = ENCODER_STATUS(i)

            PRINT "轴", i, ": ENCODER=", encoder_value, " MPOS=", mpos_value, " STATUS=", HEX(encoder_stat)
        NEXT
        
        PRINT "---"
        DELAY(100)
    NEXT
    
    PRINT "AB信号测试完成"
END SUB

' AB回零重复性测试
GLOBAL SUB ABRepeatabilityTest(axis_num, test_count)
    PRINT "=== 轴", axis_num, "AB回零重复性测试 ==="
    PRINT "测试次数：", test_count
    
    DIM positions(10)
    DIM max_deviation, min_pos, max_pos
    
    FOR i = 1 TO test_count
        PRINT "第", i, "次回零测试"
        
        ' 先移动到随机位置
        BASE(axis_num)
        MOVE(50 + i * 10)
        WAIT IDLE
        PRINT "移动到位置：", DPOS(axis_num)
        
        ' 执行AB回零
        DATUM(3)
        WAIT UNTIL IDLE(axis_num) = -1
        DELAY(10)
        
        ' 检查回零结果
        IF AXISSTATUS(axis_num) = 0 THEN
            DPOS(axis_num) = 0
            MPOS(axis_num) = 0
            positions(i) = MPOS(axis_num)
            PRINT "回零位置：", positions(i)
        ELSE
            PRINT "第", i, "次回零失败"
            RETURN
        ENDIF
        
        DELAY(1000)
    NEXT
    
    ' 计算重复性
    min_pos = positions(1)
    max_pos = positions(1)
    
    FOR i = 2 TO test_count
        IF positions(i) < min_pos THEN min_pos = positions(i)
        IF positions(i) > max_pos THEN max_pos = positions(i)
    NEXT
    
    max_deviation = max_pos - min_pos
    
    PRINT "=== 重复性测试结果 ==="
    PRINT "最大偏差：", max_deviation
    PRINT "最小位置：", min_pos
    PRINT "最大位置：", max_pos
    
    IF max_deviation < 2 THEN
        PRINT "✓ AB回零重复性良好（偏差<2个脉冲）"
    ELSEIF max_deviation < 5 THEN
        PRINT "⚠ AB回零重复性一般（偏差<5个脉冲）"
    ELSE
        PRINT "✗ AB回零重复性需要改善（偏差≥5个脉冲）"
        PRINT "建议："
        PRINT "  1. 降低CREEP速度"
        PRINT "  2. 增加HOMEWAIT时间"
        PRINT "  3. 检查编码器信号质量"
    ENDIF
END SUB

' AB回零错误分析
GLOBAL SUB AnalyzeABHomeError(axis_num)
    DIM status_value, encoder_stat
    status_value = AXISSTATUS(axis_num)
    encoder_stat = ENCODER_STATUS(axis_num)
    
    PRINT "轴", axis_num, "AB回零错误详细分析："
    
    ' 轴状态分析
    IF status_value AND 1 THEN
        PRINT "  ✗ 轴错误标志置位"
    ENDIF
    
    IF status_value AND 2 THEN
        PRINT "  ✗ 碰到正向硬限位"
    ENDIF
    
    IF status_value AND 4 THEN
        PRINT "  ✗ 碰到负向硬限位"
    ENDIF
    
    IF status_value AND 8 THEN
        PRINT "  ✗ 急停信号有效"
    ENDIF
    
    IF status_value AND 16 THEN
        PRINT "  ✗ 驱动器报警"
    ENDIF
    
    ' 编码器信号分析
    PRINT "编码器AB信号诊断："
    PRINT "  ATYPE设置：", ATYPE(axis_num)
    PRINT "  编码器状态：", HEX(encoder_stat)
    PRINT "  编码器原始值：", ENCODER(axis_num)
    PRINT "  MPOS位置：", MPOS(axis_num)
    PRINT "  编码器比例：1:1（默认设置）"

    ' 原点信号分析
    DIM home_signal
    home_signal = IN(8 + axis_num)
    PRINT "  原点信号状态：", home_signal
    
    PRINT "AB信号回零故障排除建议："
    PRINT "  1. 检查编码器AB信号连接是否牢固"
    PRINT "  2. 检查编码器供电电压（5V或24V）"
    PRINT "  3. 检查原点开关连接和信号"
    PRINT "  4. 确认ATYPE=4（正交编码器）或ATYPE=5（脉冲方向编码器）"
    PRINT "  5. 检查ENCODER_RATIO设置是否合理"
    PRINT "  6. 使用屏蔽电缆，屏蔽层接地"
    PRINT "  7. 检查编码器是否损坏"
END SUB
