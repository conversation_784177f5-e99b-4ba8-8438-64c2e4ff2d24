'=============================================================================
' 螺丝机参数配置文件
' 用于设置螺丝位置、运动参数等
' 配合螺丝机控制程序使用
'=============================================================================

'注意：请先运行螺丝机控制程序.bas，再运行本配置程序

'参数配置主程序
PRINT "=== 螺丝机参数配置 ==="
PRINT "正在加载配置..."

'调用配置函数
CALL ConfigureSystem()
CALL ConfigureScrewPositions()
CALL ConfigureMotionParameters()
CALL SaveConfiguration()

PRINT "配置完成！"
CALL ShowHelp()

END

'================ 系统配置 ================
GLOBAL SUB ConfigureSystem()
    PRINT "配置系统参数..."
    
    '设置螺丝数量
    CALL SetScrewCount(1, 8)        ' 左侧8个螺丝
    CALL SetScrewCount(2, 8)        ' 右侧8个螺丝
    
    '设置吸螺丝位置
    CALL SetPickPosition(50, 150, 5)
    
    '设置电批参数
    gv_ScrewDriver_Addr = 10        ' 电批Modbus地址
    
    PRINT "系统参数配置完成"
END SUB

'================ 螺丝位置配置 ================
GLOBAL SUB ConfigureScrewPositions()
    PRINT "配置螺丝位置..."
    
    '左侧螺丝位置配置(2x4阵列)
    DIM left_start_x, left_start_y, left_spacing_x, left_spacing_y
    left_start_x = 100              ' 起始X位置
    left_start_y = 80               ' 起始Y位置  
    left_spacing_x = 50             ' X方向间距
    left_spacing_y = 40             ' Y方向间距
    
    DIM screw_index
    screw_index = 1
    
    FOR row = 0 TO 1                ' 2行
        FOR col = 0 TO 3            ' 4列
            DIM pos_x, pos_y, pos_z
            pos_x = left_start_x + col * left_spacing_x
            pos_y = left_start_y + row * left_spacing_y
            pos_z = 0                ' 螺丝孔表面高度
            
            CALL SetScrewPosition(1, screw_index, pos_x, pos_y, pos_z)
            screw_index = screw_index + 1
        NEXT
    NEXT
    
    '右侧螺丝位置配置(2x4阵列)
    DIM right_start_x, right_start_y, right_spacing_x, right_spacing_y
    right_start_x = 100             ' 起始X位置
    right_start_y = 220             ' 起始Y位置(右侧)
    right_spacing_x = 50            ' X方向间距
    right_spacing_y = 40            ' Y方向间距
    
    screw_index = 1
    
    FOR row = 0 TO 1                ' 2行
        FOR col = 0 TO 3            ' 4列
            DIM pos_x, pos_y, pos_z
            pos_x = right_start_x + col * right_spacing_x
            pos_y = right_start_y + row * right_spacing_y
            pos_z = 0                ' 螺丝孔表面高度
            
            CALL SetScrewPosition(2, screw_index, pos_x, pos_y, pos_z)
            screw_index = screw_index + 1
        NEXT
    NEXT
    
    PRINT "螺丝位置配置完成"
END SUB

'================ 运动参数配置 ================
GLOBAL SUB ConfigureMotionParameters()
    PRINT "配置运动参数..."
    
    '重新配置轴参数以适应实际应用
    BASE(0, 1, 2, 3)
    
    '速度参数调整
    SPEED = 150, 150, 150, 80       ' 提高XY速度，Z轴保持较慢
    ACCEL = 1500, 1500, 1500, 800   ' 提高加速度
    DECEL = 1500, 1500, 1500, 800   ' 提高减速度
    
    '插补参数优化
    MERGE = ON                      ' 连续插补
    CORNER_MODE = 2 + 8             ' 拐角减速 + 曲率限速
    DECEL_ANGLE = 30 * (PI/180)     ' 开始减速角度30度
    STOP_ANGLE = 60 * (PI/180)      ' 停止减速角度60度
    ZSMOOTH = 5                     ' 倒角半径5mm
    
    '安全参数
    FOR i = 0 TO 3
        FASTDEC(i) = ACCEL(i) * 2   ' 快速减速度为加速度的2倍
    NEXT
    
    PRINT "运动参数配置完成"
END SUB

'================ 保存配置到FLASH ================
GLOBAL SUB SaveConfiguration()
    PRINT "保存配置到FLASH..."
    
    '保存系统参数到FLASH扇区0
    FLASH_WRITE 0, gv_ScrewCount_Left, gv_ScrewCount_Right
    FLASH_WRITE 0, gv_PickPos_X, gv_PickPos_Y, gv_PickPos_Z
    FLASH_WRITE 0, gv_ScrewDriver_Addr
    
    '保存左侧螺丝位置到FLASH扇区1
    FLASH_WRITE 1, TABLE(0, 24)    ' 保存8个螺丝的位置数据(8*3=24个数据)
    
    '保存右侧螺丝位置到FLASH扇区2  
    FLASH_WRITE 2, TABLE(100, 24)  ' 保存8个螺丝的位置数据
    
    PRINT "配置保存完成"
END SUB

'================ 从FLASH加载配置 ================
GLOBAL SUB LoadConfiguration()
    PRINT "从FLASH加载配置..."
    
    '加载系统参数
    FLASH_READ 0, gv_ScrewCount_Left, gv_ScrewCount_Right
    FLASH_READ 0, gv_PickPos_X, gv_PickPos_Y, gv_PickPos_Z
    FLASH_READ 0, gv_ScrewDriver_Addr
    
    '加载左侧螺丝位置
    FLASH_READ 1, TABLE(0, 24)
    
    '加载右侧螺丝位置
    FLASH_READ 2, TABLE(100, 24)
    
    PRINT "配置加载完成"
    PRINT "左侧螺丝数量：", gv_ScrewCount_Left
    PRINT "右侧螺丝数量：", gv_ScrewCount_Right
    PRINT "吸螺丝位置：X=", gv_PickPos_X, " Y=", gv_PickPos_Y, " Z=", gv_PickPos_Z
END SUB

'================ 显示所有螺丝位置 ================
GLOBAL SUB ShowAllScrewPositions()
    PRINT "=== 左侧螺丝位置 ==="
    FOR i = 1 TO gv_ScrewCount_Left
        CALL GetScrewPosition(1, i)
    NEXT
    
    PRINT "=== 右侧螺丝位置 ==="
    FOR i = 1 TO gv_ScrewCount_Right
        CALL GetScrewPosition(2, i)
    NEXT
END SUB

'================ 单个螺丝测试 ================
GLOBAL SUB TestSingleScrew(side, screw_num)
    IF CheckAllAxisHomed() = 0 THEN
        PRINT "错误：轴未回零，无法测试"
        RETURN
    ENDIF
    
    PRINT "测试单个螺丝：侧边=", side, " 编号=", screw_num
    
    '获取螺丝位置
    DIM base_addr, index, test_x, test_y, test_z
    
    IF side = 1 THEN
        base_addr = gv_ScrewPos_Left_Start
        IF screw_num > gv_ScrewCount_Left THEN
            PRINT "错误：螺丝编号超出范围"
            RETURN
        ENDIF
    ELSEIF side = 2 THEN
        base_addr = gv_ScrewPos_Right_Start
        IF screw_num > gv_ScrewCount_Right THEN
            PRINT "错误：螺丝编号超出范围"
            RETURN
        ENDIF
    ELSE
        PRINT "错误：无效的侧边参数"
        RETURN
    ENDIF
    
    index = base_addr + (screw_num - 1) * 3
    test_x = TABLE(index)
    test_y = TABLE(index + 1)
    test_z = TABLE(index + 2)
    
    '确定使用的Y轴
    DIM y_axis
    IF side = 1 THEN
        y_axis = 1                  ' 左侧使用Y1轴
    ELSE
        y_axis = 2                  ' 右侧使用Y2轴
    ENDIF
    
    '执行测试
    gv_SystemStatus = 1
    gv_CurrentScrew = screw_num
    
    CALL ScrewProcess(test_x, test_y, test_z, y_axis)
    
    gv_SystemStatus = 0
    gv_CurrentScrew = 0
    
    PRINT "单个螺丝测试完成"
END SUB

'================ 批量位置设置 ================
GLOBAL SUB SetScrewArray(side, start_x, start_y, start_z, rows, cols, spacing_x, spacing_y)
    '批量设置螺丝位置为矩形阵列
    'side: 1-左侧, 2-右侧
    'start_x, start_y, start_z: 起始位置
    'rows, cols: 行数和列数
    'spacing_x, spacing_y: X和Y方向间距
    
    PRINT "设置螺丝阵列：侧边=", side, " 行数=", rows, " 列数=", cols
    
    DIM screw_idx
    screw_idx = 1
    
    FOR row = 0 TO rows - 1
        FOR col = 0 TO cols - 1
            DIM pos_x, pos_y, pos_z
            pos_x = start_x + col * spacing_x
            pos_y = start_y + row * spacing_y
            pos_z = start_z
            
            CALL SetScrewPosition(side, screw_idx, pos_x, pos_y, pos_z)
            screw_idx = screw_idx + 1

            '检查是否超出数量限制
            IF (side = 1 AND screw_idx > gv_ScrewCount_Left) OR (side = 2 AND screw_idx > gv_ScrewCount_Right) THEN
                PRINT "警告：螺丝数量超出设定值"
                RETURN
            ENDIF
        NEXT
    NEXT
    
    PRINT "螺丝阵列设置完成，共设置", screw_idx - 1, "个螺丝"
END SUB

'================ 配置验证 ================
GLOBAL SUB ValidateConfiguration()
    PRINT "=== 配置验证 ==="
    
    '检查螺丝数量
    IF gv_ScrewCount_Left <= 0 OR gv_ScrewCount_Left > 33 THEN
        PRINT "错误：左侧螺丝数量无效"
    ELSE
        PRINT "✓ 左侧螺丝数量：", gv_ScrewCount_Left
    ENDIF
    
    IF gv_ScrewCount_Right <= 0 OR gv_ScrewCount_Right > 33 THEN
        PRINT "错误：右侧螺丝数量无效"
    ELSE
        PRINT "✓ 右侧螺丝数量：", gv_ScrewCount_Right
    ENDIF
    
    '检查吸螺丝位置
    PRINT "✓ 吸螺丝位置：X=", gv_PickPos_X, " Y=", gv_PickPos_Y, " Z=", gv_PickPos_Z
    
    '检查电批地址
    IF gv_ScrewDriver_Addr <= 0 OR gv_ScrewDriver_Addr > 247 THEN
        PRINT "错误：电批地址无效"
    ELSE
        PRINT "✓ 电批地址：", gv_ScrewDriver_Addr
    ENDIF
    
    '检查螺丝位置数据
    DIM valid_positions
    valid_positions = 1
    
    FOR i = 1 TO gv_ScrewCount_Left
        DIM index
        index = gv_ScrewPos_Left_Start + (i - 1) * 3
        IF TABLE(index) = 0 AND TABLE(index + 1) = 0 AND TABLE(index + 2) = 0 THEN
            PRINT "警告：左侧螺丝", i, "位置未设置"
            valid_positions = 0
        ENDIF
    NEXT
    
    FOR i = 1 TO gv_ScrewCount_Right
        DIM index
        index = gv_ScrewPos_Right_Start + (i - 1) * 3
        IF TABLE(index) = 0 AND TABLE(index + 1) = 0 AND TABLE(index + 2) = 0 THEN
            PRINT "警告：右侧螺丝", i, "位置未设置"
            valid_positions = 0
        ENDIF
    NEXT
    
    IF valid_positions = 1 THEN
        PRINT "✓ 所有螺丝位置已设置"
    ENDIF
    
    PRINT "配置验证完成"
END SUB
