===============================================================================
                        螺丝机参数配置说明 - 高速高精度版本
===============================================================================

【最新参数配置】

=== 螺丝数量配置 ===
左侧螺丝数量：8个（2x4阵列，默认值，最多64个）
右侧螺丝数量：8个（2x4阵列，默认值，最多64个）
总螺丝数量：16个（最多128个）

螺丝阵列布局：
```
左侧阵列（2行4列）：
第一行：螺丝1  螺丝2  螺丝3  螺丝4
       X=100  X=150  X=200  X=250
       Y=80   Y=80   Y=80   Y=80
       Z=30   Z=30   Z=30   Z=30   (打螺丝深度)

第二行：螺丝5  螺丝6  螺丝7  螺丝8
       X=100  X=150  X=200  X=250
       Y=120  Y=120  Y=120  Y=120
       Z=30   Z=30   Z=30   Z=30   (打螺丝深度)

右侧阵列（2行4列）：
第一行：螺丝1  螺丝2  螺丝3  螺丝4
       X=100  X=150  X=200  X=250
       Y=220  Y=220  Y=220  Y=220
       Z=30   Z=30   Z=30   Z=30   (打螺丝深度)

第二行：螺丝5  螺丝6  螺丝7  螺丝8
       X=100  X=150  X=200  X=250
       Y=260  Y=260  Y=260  Y=260
       Z=30   Z=30   Z=30   Z=30   (打螺丝深度)

扩展支持：
- 最多64个螺丝/侧：2行32列布局
- X方向可扩展到1650mm（32列×50mm间距）
- Y方向跨度：40mm（行间距）
- 数据存储：左侧0-191，右侧200-391
- 动态设置：CALL SetScrewCount(left_count, right_count)
```

=== 运动参数配置 ===
运动速度：1000mm/s (1m/s)
- X轴：1000mm/s
- Y1轴：1000mm/s  
- Y2轴：1000mm/s
- Z轴：500mm/s（较慢，保证精度）

加速度：1000mm/s² (1m/s²)
- 所有轴：1000mm/s²

减速度：1000mm/s² (1m/s²)
- 所有轴：1000mm/s²

轨迹类型：S型曲线 + 统一取螺丝位置 + 标准三段轨迹
- S型曲线：SRAMP = 100（100ms平滑时间）
- 统一取螺丝位置：所有螺丝都从(50,150,10)开始
- 标准三段轨迹：抬Z→圆弧→Z下降，强制使用
- 真圆弧插补：MOVECIRC2ABS三点画弧，一段完整圆弧
- 安全高度：取螺丝8mm，工件25mm（必须>0）
- 圆弧最高点：固定在Z=20mm
- 打螺丝优化：Z轴不动，只控制电批
- 组合效果：统一起点 + 标准轨迹 + 简化打螺丝 = 极致精度

=== 轴编号配置 ===
四轴配置：X轴(0), Y1轴(1), Y2轴(2), Z轴(3)
- X轴：编号0，水平移动，控制螺丝机头左右位置
- Y1轴：编号1，左侧滑台，10mm螺距丝杆
- Y2轴：编号2，右侧滑台，10mm螺距丝杆
- Z轴：编号3，垂直移动，控制螺丝机头上下位置

启动时显示轴编号对应关系，便于调试和维护

脉冲当量：
- X轴：1000脉冲/mm
- Y1轴：100脉冲/mm（10mm螺距）
- Y2轴：100脉冲/mm（10mm螺距）
- Z轴：1000脉冲/mm

=== 双Y轴滑轨位置 ===
左侧Y1轴：
- 用户位置：300mm（方便放置工件）
- 工作位置：80mm（第一个螺丝Y坐标）

右侧Y2轴：
- 用户位置：300mm（方便放置工件）
- 工作位置：220mm（第一个螺丝Y坐标）

=== 系统初始位置 ===
回零完成后自动移动到初始工作位置：
- X轴：50mm（吸螺丝位置）
- Y1轴：300mm（左侧用户位置）
- Y2轴：300mm（右侧用户位置）
- Z轴：8mm（吸螺丝安全高度）

初始位置优势：
- 系统启动后即可开始作业
- 所有轴都在安全位置
- 用户可以方便地放置工件
- 减少首次移动时间

=== 统一取螺丝位置 ===
所有螺丝都从同一位置开始：
- X坐标：50mm
- Y坐标：150mm
- Z坐标：10mm（往下为正）
- 特点：不管左侧右侧，不管哪个孔，都从这里取螺丝

=== Z轴高度设置（绝对值） ===
关键高度参数（所有都是绝对坐标值）：
- 打螺丝工作高度：30mm（绝对值，三段插补的最终目标位置）
- 取螺丝位置安全高度：15mm（绝对值，必须>0）
- 工件位置安全高度：25mm（绝对值，必须>0）
- 圆弧最高点：5mm（绝对值，最高点，必须小于所有安全高度）
- Z轴坐标系：往下为正方向

正确高度关系：5mm < 15mm < 25mm < 30mm
即：arc_top_height < pick_safe_height < work_safe_height < screw_work_height

=== 标准三段轨迹（强制模式，绝对值）===
唯一轨迹模式（所有高度都是绝对坐标值）：
- 第一段：抬Z到起点安全高度（绝对值）
- 第二段：安全高度间真圆弧插补（MOVECIRC2ABS，绝对值）
- 第三段：Z下降到目标工作位置（绝对值）
- 打螺丝：Z轴不动，只控制电批

绝对值优势：
- 简化计算，无需相对值转换
- 提高可靠性，避免计算错误
- 便于维护，参数设置直观

【参数优势】

=== 高速运动 ===
✅ 运动速度提升到1m/s，比原来的80mm/s快12.5倍
✅ 加速度1m/s²，快速启停
✅ S型曲线，时间维度平滑，减少振动
✅ 三段连续轨迹，空间维度连续，无停顿
✅ Y轴独立移动，XZ三段连续，运动更高效

=== 高精度控制 ===
✅ S型曲线减少时间维度的冲击和振动
✅ 三段连续轨迹减少空间维度的停顿冲击
✅ Y轴独立移动，避免多轴耦合误差
✅ 智能安全高度，确保轨迹一致性
✅ 连续插补技术，提高运动精度和效率

=== 大批量生产 ===
✅ 螺丝数量从4个增加到8个，产能翻倍
✅ 4x2阵列布局，更适合长条形工件
✅ 左右两侧各8个，总共16个螺丝
✅ 支持扩展到64个螺丝/侧，最多128个螺丝
✅ 适合从小批量到超大批量生产需求

【性能提升对比】

=== 速度提升 ===
原参数：80mm/s → 新参数：1000mm/s
提升倍数：12.5倍
单次移动时间大幅缩短

=== 产能提升 ===
原螺丝数：4个/侧 → 新螺丝数：8个/侧
产能提升：100%（翻倍）
总螺丝数：从8个增加到16个

=== 轨迹优化 ===
原轨迹：梯形轨迹 → 新轨迹：SS型轨迹
优势：超平滑，最大程度减少振动和抖动，提高精度

【时间估算】

=== 单个螺丝作业时间 ===
基于1m/s运动速度估算：
1. 移动到吸螺丝位置：~0.2秒
2. 吸取螺丝：1秒（固定）
3. 移动到螺丝孔位：~0.3秒
4. 电批锁紧：2秒（固定）
5. 移动回吸螺丝位置：~0.3秒
6. 螺丝间延时：0.5秒

单个螺丝总时间：约4.3秒

=== 完整作业时间 ===
左侧8个螺丝：8 × 4.3 = 34.4秒
右侧8个螺丝：8 × 4.3 = 34.4秒
双侧并行：最长34.4秒（如果交替进行）

相比原来4个螺丝×5.5秒=22秒
虽然螺丝数量翻倍，但由于速度提升，时间增加有限

【参数调整方法】

=== 螺丝数量调整 ===
修改全局变量：
```basic
left_screw_num = 8      ' 左侧螺丝数量
right_screw_num = 8     ' 右侧螺丝数量
```

=== 运动速度调整 ===
修改SPEED参数：
```basic
SPEED = 1000, 1000, 1000, 500   ' X,Y1,Y2,Z轴速度(mm/s)
```

=== 加减速调整 ===
修改ACCEL和DECEL参数：
```basic
ACCEL = 1000, 1000, 1000, 1000  ' 加速度(mm/s²)
DECEL = 1000, 1000, 1000, 1000  ' 减速度(mm/s²)
```

=== S型曲线调整 ===
修改SRAMP参数：
```basic
SRAMP = 100                 ' S型曲线平滑时间100ms（0-250ms范围）
' 推荐值：
' SRAMP = 50   较快，适合高速运动
' SRAMP = 100  较平滑，适合精密定位
' SRAMP = 150  很平滑，适合重载或精密场合
```

SS型轨迹优势：
- 加加速参数随加减速阶段变化
- 速度曲线比S型更平滑
- 加减速开始和结束阶段更柔和
- 最大程度减少轴的抖动

=== 螺丝位置调整 ===
修改TABLE数组：
```basic
' 左侧螺丝位置（索引0-23，8个螺丝×3坐标）
TABLE(0) = 100      ' 螺丝1 X坐标
TABLE(1) = 80       ' 螺丝1 Y坐标
TABLE(2) = 0        ' 螺丝1 Z坐标
...

' 右侧螺丝位置（索引100-123，8个螺丝×3坐标）
TABLE(100) = 100    ' 螺丝1 X坐标
TABLE(101) = 220    ' 螺丝1 Y坐标
TABLE(102) = 0      ' 螺丝1 Z坐标
...
```

【注意事项】

=== 安全考虑 ===
1. 高速运动需要确保机械结构足够刚性
2. 检查限位开关和急停功能
3. 逐步提升速度，先测试低速再提升
4. 确保电机功率足够支持高加速度

=== 调试建议 ===
1. 先使用测试版验证逻辑正确性
2. 实际硬件测试时先降低速度
3. 逐步提升到目标速度
4. 观察运动平滑性和定位精度

=== 优化空间 ===
1. 可根据实际需求调整螺丝阵列布局
2. 可优化运动路径减少空行程
3. 可调整S型轨迹参数优化平滑性
4. 可根据螺丝类型调整锁紧时间

【版本兼容性】
- 螺丝机多线程简化版.bas：已更新所有参数
- 螺丝机测试版.bas：已更新螺丝数量
- 所有子任务文件：无需修改
- 使用说明文档：保持兼容

===============================================================================
