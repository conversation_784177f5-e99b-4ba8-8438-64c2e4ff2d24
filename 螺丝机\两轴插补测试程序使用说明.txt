===============================================================================
                        两轴插补测试程序使用说明
                        根据官方手册5.2插补运动章节编写
===============================================================================

【程序概述】

本测试程序根据正运动控制器官方手册5.2插补运动章节编写，包含8个测试项目，
全面演示两轴直线插补、圆弧插补和连续插补的各种应用场景。

程序特点：
✅ 完全按照官方手册理论编写
✅ 包含详细的理论计算和说明
✅ 自动设置VP_SPEED显示单轴速度
✅ 提供示波器监控指导
✅ 交互式菜单操作
✅ 8个渐进式测试项目

【测试项目详解】

=== 测试1：两轴直线插补 ===
```
理论基础：官方手册5.2.4节 - 二轴直线插补
测试内容：从A点(0,0)运动到B点(100,100)
理论计算：
- 插补合成运动距离：√(100²+100²) = 141.42 units
- X轴实际速度：100×100/141.42 = 70.71 units/s
- Y轴实际速度：100×100/141.42 = 70.71 units/s

观察要点：
✅ VP_SPEED(0)和VP_SPEED(1)应该相等（45度直线）
✅ 两轴同时启动，同时到达终点
✅ 速度曲线应该是对称的钟形曲线
```

=== 测试2：两轴圆弧插补 ===
```
理论基础：官方手册5.2.2节 - 圆弧插补原理
测试内容：三点圆弧插补，逆时针圆弧
轨迹：从点(100,100)经过中间点(50,150)到终点(0,100)

观察要点：
✅ VP_SPEED(0)和VP_SPEED(1)按圆弧轨迹变化
✅ 速度曲线平滑，无突变
✅ 轨迹应该是平滑的圆弧
```

=== 测试3：非连续插补（MERGE=OFF）===
```
理论基础：官方手册连续插补章节 - 不开启MERGE的情况
测试内容：四段直线插补组成矩形轨迹
设置：MERGE=OFF, CORNER_MODE=0

观察要点：
❌ 每段之间都有明显停顿
❌ 速度曲线有断点，每段都从0重新加速
❌ 加工效率低下
❌ 机械冲击大
```

=== 测试4：连续插补（MERGE=ON）===
```
理论基础：官方手册连续插补章节 - 开启MERGE的情况
测试内容：四段直线插补组成矩形轨迹
设置：MERGE=ON, CORNER_MODE=0

观察要点：
✅ 各段之间连续，无停顿
✅ 速度曲线连续
❌ 拐角处仍有冲击（90度直角）
```

=== 测试5：前瞻拐角减速（CORNER_MODE=2）===
```
理论基础：官方手册5.3节 - 前瞻预处理
测试内容：自动拐角减速
设置：MERGE=ON, CORNER_MODE=2
参数：DECEL_ANGLE=30度, STOP_ANGLE=90度

观察要点：
✅ 拐角处按比例减速
✅ 既保证连续性又减少冲击
✅ 速度曲线在拐角处有坡谷
✅ 不改变运动轨迹
```

=== 测试6：自动倒角（CORNER_MODE=32）===
```
理论基础：官方手册5.3节 - CORNER_MODE=32自动倒角
测试内容：拐角处自动倒角
设置：MERGE=ON, CORNER_MODE=32, ZSMOOTH=10

观察要点：
✅ 拐角处有圆弧倒角
✅ 轨迹平滑，速度保持高速
✅ 改变了运动轨迹
✅ 适合对速度要求高的场合
```

=== 测试7：组合前瞻（CORNER_MODE=2+32）===
```
理论基础：官方手册 - CORNER_MODE组合使用
测试内容：同时使用拐角减速和自动倒角
设置：MERGE=ON, CORNER_MODE=34（2+32）

观察要点：
✅ 拐角处既有倒角又有减速
✅ 平衡了速度和精度
✅ 综合效果最佳
✅ 适合大多数应用场合
```

=== 测试8：复杂轨迹连续插补 ===
```
理论基础：直线插补+圆弧插补的组合应用
测试内容：6段轨迹（直线+圆弧+直线+圆弧+直线+直线）
设置：MERGE=ON, CORNER_MODE=32, ZSMOOTH=5

观察要点：
✅ 直线和圆弧之间完全连续
✅ 整体轨迹平滑
✅ 复杂路径的连续性验证
✅ 实际应用场景模拟
```

【使用方法】

=== 运行步骤 ===
```
1. 加载程序：
   将"两轴插补测试程序.bas"加载到正运动控制器

2. 连接示波器：
   - 连接VP_SPEED(0)和VP_SPEED(1)信号
   - 设置合适的时间刻度和电压刻度

3. 运行程序：
   执行程序，会自动显示菜单

4. 选择测试：
   输入1-8选择单个测试，或输入9运行所有测试

5. 观察结果：
   通过示波器观察速度曲线变化
```

=== 示波器设置建议 ===
```
推荐监控信号：
VP_SPEED(0)  - X轴单轴速度（蓝色，刻度100，偏移0）
VP_SPEED(1)  - Y轴单轴速度（红色，刻度100，偏移-60）
MSPEED(0)    - X轴分速度（绿色，刻度100，偏移-120）
MSPEED(1)    - Y轴分速度（黄色，刻度100，偏移-180）

时间刻度：根据测试内容调整（建议2-10秒）
触发方式：自动触发（程序中已设置TRIGGER）
```

【观察要点】

=== 直线插补的特征 ===
```
正常现象：
✅ 两轴速度按比例变化
✅ 45度直线时两轴速度相等
✅ 速度曲线对称

异常现象：
❌ 两轴速度不协调
❌ 速度曲线不对称
❌ 有明显的速度突变
```

=== 圆弧插补的特征 ===
```
正常现象：
✅ 两轴速度按圆弧轨迹变化
✅ 速度曲线平滑连续
✅ 合成速度基本恒定

异常现象：
❌ 速度曲线有尖峰
❌ 轨迹不平滑
❌ 有明显的方向突变
```

=== 连续插补的特征 ===
```
成功标志：
✅ 段间无速度断点
✅ 整体曲线连续
✅ 无中间停顿

失败标志：
❌ 段间速度降到0
❌ 有明显的停顿
❌ 速度曲线有断点
```

=== 前瞻功能的特征 ===
```
拐角减速（CORNER_MODE=2）：
✅ 拐角处速度下降但不为0
✅ 轨迹不变，仅速度变化
✅ 速度曲线有坡谷

自动倒角（CORNER_MODE=32）：
✅ 拐角处轨迹变为圆弧
✅ 速度保持较高水平
✅ 轨迹平滑过渡

组合前瞻（CORNER_MODE=2+32）：
✅ 既有轨迹倒角又有速度控制
✅ 综合效果最佳
✅ 适合大多数应用
```

【故障排除】

=== 常见问题及解决方法 ===
```
问题1：连续插补不生效，段间仍有停顿
原因：MERGE设置无效或有其他干扰指令
解决：检查MERGE=ON设置，确认无WAIT IDLE干扰

问题2：速度曲线不平滑，有突变
原因：轴参数设置不当或机械刚性不足
解决：调整ACCEL/DECEL参数，检查机械系统

问题3：圆弧插补轨迹不准确
原因：三点设置不当或插补精度问题
解决：检查三点坐标，确认圆弧参数合理

问题4：前瞻功能无效
原因：CORNER_MODE设置错误或参数不匹配
解决：确认CORNER_MODE设置，检查相关参数

问题5：VP_SPEED显示异常
原因：SYSTEM_ZSET设置问题
解决：程序会自动设置，如有问题请重新运行
```

【理论验证】

=== 速度计算验证 ===
```
直线插补速度计算：
设主轴速度为S，运动距离为(ΔX, ΔY)
合成距离：L = √(ΔX² + ΔY²)
X轴实际速度：Sx = S × ΔX / L
Y轴实际速度：Sy = S × ΔY / L

验证方法：
通过示波器测量实际速度，与理论计算对比
```

=== 连续插补验证 ===
```
连续插补成功的判断标准：
1. 速度曲线无断点
2. 段间无停顿时间
3. 整体运动时间最短
4. 机械冲击最小

测量方法：
使用示波器测量整个运动过程的速度曲线
```

【总结】

本测试程序提供了完整的两轴插补测试方案：
✅ **理论基础扎实**：完全按照官方手册编写
✅ **测试全面**：涵盖所有主要插补功能
✅ **操作简便**：交互式菜单，易于使用
✅ **监控完善**：自动设置示波器监控
✅ **说明详细**：每个测试都有理论说明
✅ **故障排除**：提供常见问题解决方法

通过这8个测试项目，可以全面了解和验证：
- 两轴直线插补的原理和应用
- 两轴圆弧插补的特性和效果
- 连续插补的实现方法和优势
- 前瞻预处理的各种模式和效果
- 复杂轨迹的连续插补应用

建议按顺序执行所有测试，通过示波器观察速度曲线的变化，
深入理解插补运动的原理和正运动控制器的强大功能。

===============================================================================
