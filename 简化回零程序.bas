'=============================================================================
' 简化回零程序 - 避免函数名冲突
' 基于正运动官方手册和最佳实践
'=============================================================================

'全局变量定义
DIM sys_status           '系统状态：0-停止，1-运行中，2-已完成
DIM axis_status(3)       '各轴状态：0-未回零，1-回零中，2-已回零，3-失败
DIM axis_count           '轴数量

'常量定义
CONST HOME_SPEED = 200   '回零速度
CONST CREEP_SPEED = 20   '爬行速度

'=============================================================================
' 主程序
'=============================================================================
PRINT "=== 简化回零程序 ==="
PRINT "避免函数名冲突，功能完整"
PRINT ""
PRINT "操作说明："
PRINT "IN0 - 单轴回零（轴0）"
PRINT "IN1 - 多轴顺序回零"
PRINT "IN2 - 多轴同时回零"
PRINT "IN3 - 状态检查"
PRINT "IN7 - 紧急停止"

'初始化
CALL init_system()

'主循环
WHILE 1
    '扫描输入信号
    IF SCAN_EVENT(IN(0)) > 0 THEN       '单轴回零
        CALL single_axis_home(0)
    ENDIF
    
    IF SCAN_EVENT(IN(1)) > 0 THEN       '多轴顺序回零
        CALL multi_axis_home()
    ENDIF
    
    IF SCAN_EVENT(IN(2)) > 0 THEN       '多轴同时回零
        CALL simul_axis_home()
    ENDIF
    
    IF SCAN_EVENT(IN(3)) > 0 THEN       '状态检查
        CALL check_status()
    ENDIF
    
    '紧急停止
    IF IN(7) = ON THEN
        CALL emergency_stop()
    ENDIF
    
    '状态指示灯
    IF sys_status = 1 THEN              '回零中
        OP(0, TICKS AND 1)              '红灯闪烁
        OP(1, OFF)
    ELSEIF sys_status = 2 THEN          '回零完成
        OP(0, OFF)
        OP(1, ON)                       '绿灯亮
    ELSE                                '待机
        OP(0, OFF)
        OP(1, OFF)
    ENDIF
    
    DELAY(50)
WEND
END

'=============================================================================
' 系统初始化
'=============================================================================
SUB init_system()
    PRINT "初始化系统参数..."
    
    axis_count = 3
    sys_status = 0
    
    '初始化轴状态
    FOR i = 0 TO axis_count - 1
        axis_status(i) = 0
    NEXT
    
    '轴参数设置
    BASE(0, 1, 2)
    ATYPE = 1, 1, 1                      '脉冲轴
    UNITS = 100, 100, 100                '脉冲当量
    ACCEL = 1000, 1000, 1000             '加速度
    DECEL = 1000, 1000, 1000             '减速度
    SPEED = HOME_SPEED, HOME_SPEED, HOME_SPEED    '回零速度
    CREEP = CREEP_SPEED, CREEP_SPEED, CREEP_SPEED '爬行速度
    HOMEWAIT = 20, 20, 20                '反找等待时间(ms)
    
    '原点输入设置
    DATUM_IN = 8, 9, 10                  '原点输入：IN8, IN9, IN10
    INVERT_IN(8, ON)                     '反转原点信号（ZMC系列OFF有效）
    INVERT_IN(9, ON)
    INVERT_IN(10, ON)
    
    '限位输入设置
    FWD_IN = 11, 12, 13                  '正向限位
    REV_IN = 14, 15, 16                  '负向限位
    INVERT_IN(11, ON)
    INVERT_IN(12, ON)
    INVERT_IN(13, ON)
    INVERT_IN(14, ON)
    INVERT_IN(15, ON)
    INVERT_IN(16, ON)
    
    '关闭所有输出
    FOR i = 0 TO 7
        OP(i, OFF)
    NEXT
    
    PRINT "系统初始化完成"
END SUB

'=============================================================================
' 单轴回零
'=============================================================================
SUB single_axis_home(axis_num)
    IF sys_status = 1 THEN
        PRINT "系统忙，无法回零"
        RETURN
    ENDIF
    
    PRINT "=== 单轴回零：轴", axis_num, " ==="
    
    sys_status = 1                       '设置为回零中
    axis_status(axis_num) = 1
    
    BASE(axis_num)
    
    '执行回零
    DATUM(3)                             '使用模式3（正向找原点）
    
    '等待回零完成
    WAIT UNTIL IDLE(axis_num) = -1
    DELAY(10)                            '状态稳定延时
    
    '检查回零结果
    IF AXISSTATUS(axis_num) = 0 THEN
        '回零成功处理
        DPOS(axis_num) = 0               '清零位置
        MPOS(axis_num) = 0               '清零编码器位置
        axis_status(axis_num) = 2        '设置为已回零
        PRINT "轴", axis_num, "回零成功！位置：", DPOS(axis_num)
    ELSE
        '回零失败处理
        axis_status(axis_num) = 3        '设置为回零失败
        PRINT "轴", axis_num, "回零失败！状态：", HEX(AXISSTATUS(axis_num))
        CALL analyze_error(axis_num, AXISSTATUS(axis_num))
    ENDIF
    
    sys_status = 0                       '状态切换成待机
END SUB

'=============================================================================
' 多轴顺序回零
'=============================================================================
SUB multi_axis_home()
    IF sys_status = 1 THEN
        PRINT "系统忙，无法回零"
        RETURN
    ENDIF
    
    PRINT "=== 多轴顺序回零 ==="
    
    sys_status = 1                       '状态切换成回零中
    
    '设置所有轴状态为回零中
    FOR i = 0 TO axis_count - 1
        axis_status(i) = 1
    NEXT
    
    '按轴号顺序回零
    FOR i = 0 TO axis_count - 1
        BASE(i)
        PRINT "开始轴", i, "回零..."
        
        '执行回零
        DATUM(3)                         '使用模式3
        
        '等待当前轴回零完成
        WAIT UNTIL IDLE(i) = -1
        DELAY(10)
        
        '检查回零结果
        IF AXISSTATUS(i) = 0 THEN
            axis_status(i) = 2
            DPOS(i) = 0
            MPOS(i) = 0
            PRINT "轴", i, "回零成功"
        ELSE
            axis_status(i) = 3
            PRINT "轴", i, "回零失败，状态：", HEX(AXISSTATUS(i))
            '如果有轴失败，停止后续回零
            sys_status = 0
            PRINT "回零过程中断，轴", i, "失败"
            RETURN
        ENDIF
        
        DELAY(200)                       '轴间延时
    NEXT
    
    sys_status = 2                       '状态切换成已完成
    PRINT "多轴顺序回零完成！"
END SUB

'=============================================================================
' 多轴同时回零
'=============================================================================
SUB simul_axis_home()
    IF sys_status = 1 THEN
        PRINT "系统忙，无法回零"
        RETURN
    ENDIF
    
    PRINT "=== 多轴同时回零 ==="
    
    sys_status = 1                       '状态切换成回零中
    
    '同时启动所有轴回零
    FOR i = 0 TO axis_count - 1
        axis_status(i) = 1
        BASE(i)
        DATUM(3)                         '启动回零
        PRINT "轴", i, "开始回零..."
    NEXT
    
    '等待所有轴回零完成
    WAIT UNTIL IDLE(0) = -1 AND IDLE(1) = -1 AND IDLE(2) = -1
    DELAY(10)
    
    '检查所有轴的回零结果
    DIM home_ok
    home_ok = 1
    
    FOR i = 0 TO axis_count - 1
        IF AXISSTATUS(i) = 0 THEN
            axis_status(i) = 2
            DPOS(i) = 0
            MPOS(i) = 0
            PRINT "轴", i, "回零成功，位置：", DPOS(i)
        ELSE
            axis_status(i) = 3
            PRINT "轴", i, "回零失败，状态：", HEX(AXISSTATUS(i))
            home_ok = 0
        ENDIF
    NEXT
    
    IF home_ok = 1 THEN
        sys_status = 2                   '所有轴回零成功
        PRINT "所有轴同时回零完成！"
    ELSE
        sys_status = 0                   '有轴回零失败
        PRINT "部分轴回零失败！"
    ENDIF
END SUB

'=============================================================================
' 紧急停止
'=============================================================================
SUB emergency_stop()
    RAPIDSTOP(2)
    sys_status = 0
    
    FOR j = 0 TO axis_count - 1
        IF axis_status(j) = 1 THEN
            axis_status(j) = 0
        ENDIF
    NEXT
    
    PRINT "紧急停止！所有轴停止运动"
END SUB

'=============================================================================
' 状态检查
'=============================================================================
SUB check_status()
    PRINT "=== 回零状态检查 ==="
    
    FOR i = 0 TO axis_count - 1
        PRINT "轴", i, "状态检查："
        PRINT "  位置：", DPOS(i)
        PRINT "  编码器位置：", MPOS(i)
        PRINT "  轴状态：", HEX(AXISSTATUS(i))
        PRINT "  回零状态：", axis_status(i), "(0-未回零,1-回零中,2-已回零,3-失败)"
        
        IF IDLE(i) = -1 THEN
            PRINT "  运动状态：停止"
        ELSE
            PRINT "  运动状态：运动中"
        ENDIF
        
        '检查是否在原点附近
        IF ABS(DPOS(i)) < 1 THEN
            PRINT "  原点状态：在原点"
        ELSE
            PRINT "  原点状态：偏离原点", DPOS(i), "单位"
        ENDIF
        
        '检查原点输入信号
        DIM home_input
        home_input = IN(8 + i)
        IF home_input = ON THEN
            PRINT "  原点信号：有效"
        ELSE
            PRINT "  原点信号：无效"
        ENDIF
        
        PRINT ""
    NEXT
    
    '显示系统状态
    PRINT "系统状态：", sys_status, "(0-停止,1-运行中,2-已完成)"
    IF sys_status = 0 THEN
        PRINT "状态说明：待机"
    ELSEIF sys_status = 1 THEN
        PRINT "状态说明：回零中"
    ELSEIF sys_status = 2 THEN
        PRINT "状态说明：回零完成"
    ENDIF
END SUB

'=============================================================================
' 错误分析
'=============================================================================
SUB analyze_error(axis_num, status_value)
    PRINT "轴", axis_num, "错误分析："
    
    IF status_value AND 1 THEN
        PRINT "  - 轴错误标志置位"
    ENDIF
    
    IF status_value AND 2 THEN
        PRINT "  - 碰到正向硬限位"
    ENDIF
    
    IF status_value AND 4 THEN
        PRINT "  - 碰到负向硬限位"
    ENDIF
    
    IF status_value AND 8 THEN
        PRINT "  - 急停信号有效"
    ENDIF
    
    IF status_value AND 16 THEN
        PRINT "  - 驱动器报警"
    ENDIF
    
    '检查原点信号状态
    DIM home_signal
    home_signal = IN(8 + axis_num)
    IF home_signal = ON THEN
        PRINT "  - 原点信号状态：有效"
    ELSE
        PRINT "  - 原点信号状态：无效"
    ENDIF
    
    '建议解决方案
    PRINT "建议检查："
    PRINT "  1. 原点开关连接和信号"
    PRINT "  2. 限位开关状态"
    PRINT "  3. 驱动器使能和报警"
    PRINT "  4. 轴运动方向设置"
END SUB
