[Controller]
TypeName=
IfUpload=0
UserVersion=1.0
Files=6
Breaks=0
Watches=6
[FileList]
File1=TaskMain.bas
FileType1=1
AutoRun1=0
File2=TaskHands.bas
FileType2=1
AutoRun2=-1
File3=TaskAutoRun.bas
FileType3=1
AutoRun3=-1
File4=SubDoc.bas
FileType4=1
AutoRun4=-1
File5=Watchdog.plc
FileType5=4
AutoRun5=-1
File6=Hmi1.hmi
FileType6=5
AutoRun6=5
[OpenList]
OpenFile1=TaskMain.bas
OpenLine1=36
OpenFile2=TaskHands.bas
OpenLine2=36
OpenFile3=TaskAutoRun.bas
OpenLine3=15
OpenFile4=SubDoc.bas
OpenLine4=95
OpenFile5=Watchdog.plc
OpenLine5=6
OpenFile6=Hmi1.hmi
OpenLine6=0
[WatchList]
Watch1=gStatus
Watch2=modbus_bit(3)
Watch3=modbus_bit(4)
Watch4=lFolAxisAccPls
Watch5=vr(35)
Watch6=lcurVr
