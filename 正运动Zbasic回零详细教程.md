# 正运动Zbasic回零详细教程

## 📋 目录
1. [回零基础概念](#回零基础概念)
2. [硬件连接要求](#硬件连接要求)
3. [回零模式详解](#回零模式详解)
4. [参数配置说明](#参数配置说明)
5. [编程实现步骤](#编程实现步骤)
6. [完整示例代码](#完整示例代码)
7. [故障排除指南](#故障排除指南)
8. [最佳实践建议](#最佳实践建议)

---

## 🎯 回零基础概念

### 什么是回零？
回零（原点回归）是指让运动轴回到预设的参考坐标系原点位置的过程。在高精度自动化设备中，所有运动都以原点为参考，因此回零是运动控制的基础。

### 为什么需要回零？
- **建立坐标系**：确定运动的起始参考点
- **消除累积误差**：清除断电或异常导致的位置偏差
- **保证精度**：为后续精确运动奠定基础
- **安全要求**：确保设备在已知位置启动

### 回零的基本原理
1. **快速寻找**：以较高速度（SPEED）向原点方向运动
2. **碰到原点开关**：检测到原点信号后开始减速
3. **精确定位**：以较低速度（CREEP）进行精确定位
4. **位置清零**：将当前位置设为坐标原点（DPOS=0）

---

## 🔌 硬件连接要求

### 必需的硬件信号

#### 1. 原点开关（Home Switch）
- **作用**：指示原点位置的传感器
- **连接**：通过 `DATUM_IN` 指令映射到输入口
- **类型**：通常使用接近开关、光电开关或微动开关

#### 2. 限位开关（Limit Switch）
- **正向限位**：通过 `FWD_IN` 指令映射
- **负向限位**：通过 `REV_IN` 指令映射
- **作用**：防止轴运动超出安全范围

#### 3. 信号电平说明
- **ZMC系列控制器**：OFF状态有效（常闭触点）
- **ECI系列控制器**：ON状态有效（常开触点）
- **信号反转**：使用 `INVERT_IN` 指令调整信号极性

### 典型硬件连接图
```
控制器输入端口    |    传感器类型    |    连接说明
IN8              |    X轴原点开关   |    DATUM_IN = 8
IN9              |    Y轴原点开关   |    DATUM_IN = 9
IN10             |    Z轴原点开关   |    DATUM_IN = 10
IN11             |    X轴正限位     |    FWD_IN = 11
IN12             |    X轴负限位     |    REV_IN = 12
```

---

## 📊 回零模式详解

### 常用回零模式对比表

| 模式 | 描述 | 运动方向 | 适用场景 |
|------|------|----------|----------|
| **模式1** | Z信号回零 | 正向 | 带编码器Z信号的伺服系统 |
| **模式2** | Z信号回零 | 负向 | 带编码器Z信号的伺服系统 |
| **模式3** | 原点开关回零 | 正向 | **最常用**，适合大多数应用 |
| **模式4** | 原点开关回零 | 负向 | 原点在负方向的应用 |
| **模式5** | 原点+Z信号 | 正向 | 高精度定位要求 |
| **模式6** | 原点+Z信号 | 负向 | 高精度定位要求 |
| **模式8** | 简单原点回零 | 正向 | 简单应用，精度要求不高 |
| **模式9** | 简单原点回零 | 负向 | 简单应用，精度要求不高 |
| **模式13** | 限位反找+原点 | 正向 | 原点在限位中间 |
| **模式21** | EtherCAT驱动器回零 | - | 总线伺服驱动器 |

### 模式3详细说明（推荐）
```
运动过程：
1. 轴以SPEED速度正向运动
2. 碰到原点开关后开始减速停止
3. 以CREEP速度反向运动
4. 离开原点开关后立即停止
5. DPOS位置清零，回零完成
```

### 模式13详细说明（限位反找）
```
运动过程：
1. 轴以SPEED速度正向运动
2. 如果先碰到限位开关，不停止，反向运动
3. 碰到原点开关后以CREEP速度反向
4. 离开原点开关后停止
5. DPOS位置清零，回零完成
```

---

## ⚙️ 参数配置说明

### 基本轴参数
```basic
BASE(0)                    ' 选择轴号
ATYPE = 1                  ' 轴类型：1=脉冲轴
UNITS = 100                ' 脉冲当量：每单位脉冲数
SPEED = 200                ' 回零速度（units/s）
CREEP = 20                 ' 爬行速度（units/s）
ACCEL = 1000               ' 加速度（units/s²）
DECEL = 1000               ' 减速度（units/s²）
HOMEWAIT = 20              ' 反找等待时间（ms）
```

### 信号映射配置
```basic
' 原点信号映射
DATUM_IN = 8               ' 原点开关连接到IN8
INVERT_IN(8, ON)           ' 信号反转（ZMC系列）

' 限位信号映射
FWD_IN = 11                ' 正向限位连接到IN11
REV_IN = 12                ' 负向限位连接到IN12
INVERT_IN(11, ON)          ' 限位信号反转
INVERT_IN(12, ON)
```

### 参数选择建议
- **SPEED**：根据机械结构选择，一般50-500 units/s
- **CREEP**：SPEED的1/5到1/10，保证精度
- **HOMEWAIT**：脉冲轴建议20ms，总线轴可更短
- **信号反转**：根据传感器类型和控制器型号确定

---

## 💻 编程实现步骤

### 步骤1：系统初始化
```basic
GLOBAL SUB SystemInit()
    ' 轴参数设置
    BASE(0)
    ATYPE = 1
    UNITS = 100
    SPEED = 200
    CREEP = 20
    ACCEL = 1000
    DECEL = 1000
    HOMEWAIT = 20
    
    ' 信号映射
    DATUM_IN = 8
    FWD_IN = 11
    REV_IN = 12
    
    ' 信号反转
    INVERT_IN(8, ON)
    INVERT_IN(11, ON)
    INVERT_IN(12, ON)
    
    PRINT "轴参数初始化完成"
END SUB
```

### 步骤2：执行回零
```basic
GLOBAL SUB ExecuteHoming(axis_num, home_mode)
    PRINT "开始轴", axis_num, "回零，模式：", home_mode
    
    BASE(axis_num)
    
    ' 清除轴状态
    AXIS_STOPREASON = 0
    
    ' 执行回零指令
    DATUM(home_mode)
    
    ' 等待回零完成
    WAIT UNTIL IDLE(axis_num) = -1
    DELAY(10)  ' 状态稳定延时
    
    ' 检查回零结果
    IF AXISSTATUS(axis_num) = 0 THEN
        ' 回零成功处理
        DPOS(axis_num) = 0      ' 清零位置
        MPOS(axis_num) = 0      ' 清零编码器位置
        PRINT "轴", axis_num, "回零成功！"
        RETURN 1  ' 返回成功标志
    ELSE
        ' 回零失败处理
        PRINT "轴", axis_num, "回零失败！"
        PRINT "错误状态：", HEX(AXISSTATUS(axis_num))
        CALL AnalyzeError(axis_num)
        RETURN 0  ' 返回失败标志
    ENDIF
END SUB
```

### 步骤3：多轴回零
```basic
GLOBAL SUB MultiAxisHoming()
    LOCAL home_success
    home_success = 1
    
    ' 按顺序回零各轴
    FOR i = 0 TO 2
        IF ExecuteHoming(i, 3) = 0 THEN
            home_success = 0
            PRINT "多轴回零失败，停止在轴", i
            EXIT  ' 退出循环
        ENDIF
        DELAY(500)  ' 轴间延时
    NEXT
    
    IF home_success = 1 THEN
        PRINT "所有轴回零成功！"
    ELSE
        PRINT "多轴回零失败！"
    ENDIF
    
    RETURN home_success
END SUB
```

---

## 📝 完整示例代码

### 基础回零示例
```basic
'=============================================================================
' 基础回零示例程序
'=============================================================================

' 全局变量
GLOBAL axis_count
GLOBAL home_status(3)  ' 各轴回零状态

' 主程序
axis_count = 3
CALL InitSystem()

PRINT "按IN0开始单轴回零，按IN1开始多轴回零"

WHILE 1
    ' 单轴回零
    IF SCAN_EVENT(IN(0)) > 0 THEN
        CALL SingleAxisHome(0)
    ENDIF
    
    ' 多轴回零
    IF SCAN_EVENT(IN(1)) > 0 THEN
        CALL MultiAxisHome()
    ENDIF
    
    ' 状态显示
    IF SCAN_EVENT(IN(2)) > 0 THEN
        CALL ShowStatus()
    ENDIF
    
    DELAY(50)
WEND
END

' 系统初始化
GLOBAL SUB InitSystem()
    FOR i = 0 TO axis_count - 1
        BASE(i)
        ATYPE = 1
        UNITS = 100
        SPEED = 200
        CREEP = 20
        ACCEL = 1000
        DECEL = 1000
        HOMEWAIT = 20
        
        ' 信号映射
        DATUM_IN = 8 + i
        FWD_IN = 11 + i
        REV_IN = 14 + i
        
        ' 信号反转
        INVERT_IN(8 + i, ON)
        INVERT_IN(11 + i, ON)
        INVERT_IN(14 + i, ON)
        
        home_status(i) = 0  ' 初始化为未回零
    NEXT
    
    PRINT "系统初始化完成"
END SUB

' 单轴回零
GLOBAL SUB SingleAxisHome(axis_num)
    PRINT "开始轴", axis_num, "回零..."
    
    BASE(axis_num)
    home_status(axis_num) = 1  ' 设置为回零中
    
    ' 执行回零
    DATUM(3)
    WAIT UNTIL IDLE(axis_num) = -1
    DELAY(10)
    
    ' 检查结果
    IF AXISSTATUS(axis_num) = 0 THEN
        DPOS(axis_num) = 0
        MPOS(axis_num) = 0
        home_status(axis_num) = 2  ' 设置为已回零
        PRINT "轴", axis_num, "回零成功！位置：", DPOS(axis_num)
    ELSE
        home_status(axis_num) = 3  ' 设置为回零失败
        PRINT "轴", axis_num, "回零失败！状态：", HEX(AXISSTATUS(axis_num))
        CALL AnalyzeError(axis_num)
    ENDIF
END SUB

' 多轴回零
GLOBAL SUB MultiAxisHome()
    PRINT "开始多轴顺序回零..."
    
    FOR i = 0 TO axis_count - 1
        CALL SingleAxisHome(i)
        IF home_status(i) <> 2 THEN
            PRINT "多轴回零失败，停止在轴", i
            RETURN
        ENDIF
        DELAY(500)
    NEXT
    
    PRINT "所有轴回零成功！"
END SUB

' 状态显示
GLOBAL SUB ShowStatus()
    PRINT "=== 回零状态 ==="
    FOR i = 0 TO axis_count - 1
        PRINT "轴", i, ":"
        PRINT "  状态：", home_status(i), "(0-未回零,1-回零中,2-已回零,3-失败)"
        PRINT "  位置：", DPOS(i)
        PRINT "  轴状态：", HEX(AXISSTATUS(i))
        PRINT "  原点信号：", IN(8 + i)
    NEXT
END SUB

' 错误分析
GLOBAL SUB AnalyzeError(axis_num)
    LOCAL status_value
    status_value = AXISSTATUS(axis_num)
    
    PRINT "轴", axis_num, "错误分析："
    
    IF status_value AND 1 THEN
        PRINT "  - 轴错误标志"
    ENDIF
    
    IF status_value AND 2 THEN
        PRINT "  - 正向硬限位"
    ENDIF
    
    IF status_value AND 4 THEN
        PRINT "  - 负向硬限位"
    ENDIF
    
    IF status_value AND 8 THEN
        PRINT "  - 急停信号"
    ENDIF
    
    IF status_value AND 16 THEN
        PRINT "  - 驱动器报警"
    ENDIF
    
    PRINT "建议检查："
    PRINT "  1. 原点开关连接和信号"
    PRINT "  2. 限位开关状态"
    PRINT "  3. 驱动器使能状态"
    PRINT "  4. 机械结构是否卡死"
END SUB
```

---

## 🔧 故障排除指南

### 常见问题及解决方案

#### 1. 回零失败：AXISSTATUS ≠ 0

**问题现象**：回零指令执行后，AXISSTATUS返回非零值

**状态码对照表**：
| 状态码 | 含义 | 解决方案 |
|--------|------|----------|
| 0x01 | 轴错误标志 | 检查驱动器使能和连接 |
| 0x02 | 正向硬限位 | 检查正限位开关状态 |
| 0x04 | 负向硬限位 | 检查负限位开关状态 |
| 0x08 | 急停信号 | 检查急停开关状态 |
| 0x10 | 驱动器报警 | 检查驱动器报警信号 |

**调试代码**：
```basic
SUB DiagnoseHomingError(axis_num)
    LOCAL status_code
    status_code = AXISSTATUS(axis_num)

    PRINT "轴", axis_num, "回零诊断："
    PRINT "状态码：0x", HEX(status_code)

    IF status_code AND 1 THEN
        PRINT "错误：轴错误标志置位"
        PRINT "解决：检查AXIS_ENABLE和驱动器连接"
    ENDIF

    IF status_code AND 2 THEN
        PRINT "错误：碰到正向限位"
        PRINT "解决：手动移动轴离开限位或使用模式13"
    ENDIF

    IF status_code AND 4 THEN
        PRINT "错误：碰到负向限位"
        PRINT "解决：手动移动轴离开限位或使用模式14"
    ENDIF

    PRINT "原点信号状态：", IN(DATUM_IN(axis_num))
    PRINT "正限位信号：", IN(FWD_IN(axis_num))
    PRINT "负限位信号：", IN(REV_IN(axis_num))
END SUB
```

#### 2. 找不到原点信号

**检查步骤**：
```basic
SUB CheckHomeSignal(axis_num)
    LOCAL home_input
    home_input = DATUM_IN(axis_num)

    PRINT "原点信号检查："
    PRINT "映射输入口：IN", home_input
    PRINT "当前信号状态：", IN(home_input)

    ' 手动测试原点开关
    PRINT "请手动触发原点开关，观察信号变化..."
    FOR i = 1 TO 20
        PRINT "IN", home_input, "=", IN(home_input)
        DELAY(200)
    NEXT
END SUB
```

#### 3. 回零精度优化

```basic
SUB OptimizeHomingAccuracy(axis_num)
    BASE(axis_num)

    ' 降低爬行速度提高精度
    CREEP = 5  ' 原来20，现在5

    ' 增加反找等待时间
    HOMEWAIT = 50  ' 原来20，现在50

    ' 使用更精确的回零模式
    ' DATUM(5)  ' 使用Z信号回零（需要编码器支持）

    PRINT "已优化轴", axis_num, "回零精度参数"
END SUB
```

---

## 🏆 最佳实践建议

### 1. 标准回零流程

```basic
GLOBAL SUB StandardHomingProcedure()
    PRINT "=== 标准回零流程 ==="

    ' 步骤1：系统检查
    IF SystemCheck() = 0 THEN
        PRINT "系统检查失败，停止回零"
        RETURN
    ENDIF

    ' 步骤2：执行回零
    FOR i = 0 TO axis_count - 1
        CALL SingleAxisHome(i)
        IF home_status(i) <> 2 THEN
            PRINT "轴", i, "回零失败，停止流程"
            RETURN
        ENDIF
        DELAY(500)
    NEXT

    ' 步骤3：结果验证
    CALL VerifyHomingResult()
    PRINT "标准回零流程完成"
END SUB

GLOBAL SUB SystemCheck()
    ' 检查急停状态
    IF IN(7) = ON THEN
        PRINT "错误：急停信号有效"
        RETURN 0
    ENDIF

    ' 检查驱动器使能
    FOR i = 0 TO axis_count - 1
        IF AXIS_ENABLE(i) = 0 THEN
            PRINT "警告：轴", i, "未使能"
        ENDIF
    NEXT

    PRINT "系统检查通过"
    RETURN 1
END SUB
```

### 2. 回零成功判断标准

```basic
GLOBAL SUB VerifyHomingResult()
    PRINT "=== 回零结果验证 ==="

    FOR i = 0 TO axis_count - 1
        PRINT "轴", i, "验证："

        ' 检查1：轴状态正常
        IF AXISSTATUS(i) = 0 THEN
            PRINT "  ✓ 轴状态正常"
        ELSE
            PRINT "  ✗ 轴状态异常：", HEX(AXISSTATUS(i))
        ENDIF

        ' 检查2：位置已清零
        IF ABS(DPOS(i)) < 0.1 THEN
            PRINT "  ✓ 位置已清零：", DPOS(i)
        ELSE
            PRINT "  ⚠ 位置偏差：", DPOS(i)
        ENDIF

        ' 检查3：轴已停止
        IF IDLE(i) = -1 THEN
            PRINT "  ✓ 轴已停止"
        ELSE
            PRINT "  ✗ 轴仍在运动"
        ENDIF

        PRINT ""
    NEXT
END SUB
```

### 3. 安全保护措施

```basic
GLOBAL SUB HomingSafetyProtection()
    ' 设置软限位保护
    FOR i = 0 TO axis_count - 1
        BASE(i)
        FS_LIMIT = 500   ' 正向软限位
        RS_LIMIT = -500  ' 负向软限位
    NEXT

    ' 设置急停保护
    AXISEMG_IN = 7  ' 急停信号映射到IN7

    ' 设置驱动器报警保护
    FOR i = 0 TO axis_count - 1
        ALM_IN(i) = 20 + i  ' 驱动器报警信号
        INVERT_IN(20 + i, ON)
    NEXT

    PRINT "安全保护设置完成"
END SUB
```

---

## 📚 总结

### 回零成功的三个关键判断条件

1. **`AXISSTATUS(axis) = 0`** - 轴状态无错误（最重要）
2. **`IDLE(axis) = -1`** - 轴已停止运动
3. **`ABS(DPOS(axis)) < 允许误差`** - 位置精度满足要求

### 标准回零代码模板

```basic
' 推荐的回零实现模板
GLOBAL SUB RecommendedHoming(axis_num)
    PRINT "开始轴", axis_num, "回零"

    BASE(axis_num)

    ' 1. 执行回零指令
    DATUM(3)  ' 使用模式3（最常用）

    ' 2. 等待回零完成
    WAIT UNTIL IDLE(axis_num) = -1
    DELAY(10)  ' 状态稳定延时

    ' 3. 检查回零结果
    IF AXISSTATUS(axis_num) = 0 THEN
        ' 4. 回零成功处理
        DPOS(axis_num) = 0      ' 清零脉冲位置
        MPOS(axis_num) = 0      ' 清零编码器位置
        PRINT "轴", axis_num, "回零成功！"
        RETURN 1
    ELSE
        ' 5. 回零失败处理
        PRINT "轴", axis_num, "回零失败！状态：", HEX(AXISSTATUS(axis_num))
        RETURN 0
    ENDIF
END SUB
```

### 开发建议

1. **从简单开始**：先实现单轴回零，再扩展到多轴
2. **充分测试**：在不同位置多次测试回零的重复性
3. **完善错误处理**：为各种异常情况提供明确的错误信息
4. **参数优化**：根据实际机械特性调整速度参数
5. **安全第一**：始终考虑安全保护措施

通过遵循本教程，您可以实现稳定、可靠、高精度的回零功能！
