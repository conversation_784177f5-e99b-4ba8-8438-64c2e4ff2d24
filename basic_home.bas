'=============================================================================
' 基础回零示例程序
'=============================================================================

' 全局变量
GLOBAL total_axes        ' 改名避免与AXIS_COUNT冲突
GLOBAL home_status(3)    ' 各轴回零状态

' 主程序
total_axes = 3
CALL InitSystem()

PRINT "按IN0开始单轴回零，按IN1开始多轴回零"

WHILE 1
    ' 单轴回零
    IF SCAN_EVENT(IN(0)) > 0 THEN
        CALL SingleAxisHome(0)
    ENDIF
    
    ' 多轴回零
    IF SCAN_EVENT(IN(1)) > 0 THEN
        CALL MultiAxisHome()
    ENDIF
    
    ' 状态显示
    IF SCAN_EVENT(IN(2)) > 0 THEN
        CALL ShowStatus()
    ENDIF
    
    DELAY(50)
WEND
END

' 系统初始化
GLOBAL SUB InitSystem()
    FOR i = 0 TO total_axes - 1
        BASE(i)
        ATYPE = 1
        UNITS = 100
        SPEED = 200
        CREEP = 20
        ACCEL = 1000
        DECEL = 1000
        HOMEWAIT = 20
        
        ' 信号映射
        DATUM_IN = 8 + i
        FWD_IN = 11 + i
        REV_IN = 14 + i
        
        ' 信号反转
        INVERT_IN(8 + i, ON)
        INVERT_IN(11 + i, ON)
        INVERT_IN(14 + i, ON)
        
        home_status(i) = 0  ' 初始化为未回零
    NEXT
    
    PRINT "系统初始化完成"
END SUB

' 单轴回零
GLOBAL SUB SingleAxisHome(axis_num)
    PRINT "开始轴", axis_num, "回零..."
    
    BASE(axis_num)
    home_status(axis_num) = 1  ' 设置为回零中
    
    ' 执行回零
    DATUM(3)
    WAIT UNTIL IDLE(axis_num) = -1
    DELAY(10)
    
    ' 检查结果
    IF AXISSTATUS(axis_num) = 0 THEN
        DPOS(axis_num) = 0
        MPOS(axis_num) = 0
        home_status(axis_num) = 2  ' 设置为已回零
        PRINT "轴", axis_num, "回零成功！位置：", DPOS(axis_num)
    ELSE
        home_status(axis_num) = 3  ' 设置为回零失败
        PRINT "轴", axis_num, "回零失败！状态：", HEX(AXISSTATUS(axis_num))
        CALL AnalyzeError(axis_num)
    ENDIF
END SUB

' 多轴回零
GLOBAL SUB MultiAxisHome()
    PRINT "开始多轴顺序回零..."
    
    FOR i = 0 TO total_axes - 1
        CALL SingleAxisHome(i)
        IF home_status(i) <> 2 THEN
            PRINT "多轴回零失败，停止在轴", i
            RETURN
        ENDIF
        DELAY(500)
    NEXT
    
    PRINT "所有轴回零成功！"
END SUB

' 状态显示
GLOBAL SUB ShowStatus()
    PRINT "=== 回零状态 ==="
    FOR i = 0 TO total_axes - 1
        PRINT "轴", i, ":"
        PRINT "  状态：", home_status(i), "(0-未回零,1-回零中,2-已回零,3-失败)"
        PRINT "  位置：", DPOS(i)
        PRINT "  轴状态：", HEX(AXISSTATUS(i))
        PRINT "  原点信号：", IN(8 + i)
    NEXT
END SUB

' 错误分析
GLOBAL SUB AnalyzeError(axis_num)
    DIM status_value         ' 改用DIM替代LOCAL
    status_value = AXISSTATUS(axis_num)
    
    PRINT "轴", axis_num, "错误分析："
    
    IF status_value AND 1 THEN
        PRINT "  - 轴错误标志"
    ENDIF
    
    IF status_value AND 2 THEN
        PRINT "  - 正向硬限位"
    ENDIF
    
    IF status_value AND 4 THEN
        PRINT "  - 负向硬限位"
    ENDIF
    
    IF status_value AND 8 THEN
        PRINT "  - 急停信号"
    ENDIF
    
    IF status_value AND 16 THEN
        PRINT "  - 驱动器报警"
    ENDIF
    
    PRINT "建议检查："
    PRINT "  1. 原点开关连接和信号"
    PRINT "  2. 限位开关状态"
    PRINT "  3. 驱动器使能状态"
    PRINT "  4. 机械结构是否卡死"
END SUB
