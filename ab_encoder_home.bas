'=============================================================================
' AB编码器回零程序 - 避免函数名冲突版本
' 适用于不带Z信号的编码器回零
'=============================================================================

' 全局变量
GLOBAL total_axis            ' 轴数量
GLOBAL encoder_home_status(3) ' 各轴回零状态

' 主程序
total_axis = 3
CALL InitEncoderHome()

PRINT "=== AB编码器回零程序 ==="
PRINT "IN0 - 单轴回零"
PRINT "IN1 - 多轴回零"
PRINT "IN2 - 状态检查"
PRINT "IN3 - 回零验证"

WHILE 1
    ' 单轴回零
    IF SCAN_EVENT(IN(0)) > 0 THEN
        CALL DoSingleHome(0)
    ENDIF
    
    ' 多轴回零
    IF SCAN_EVENT(IN(1)) > 0 THEN
        CALL DoMultiHome()
    ENDIF
    
    ' 状态检查
    IF SCAN_EVENT(IN(2)) > 0 THEN
        CALL ShowHomeStatus()
    ENDIF
    
    ' 回零验证
    IF SCAN_EVENT(IN(3)) > 0 THEN
        CALL VerifyAllHome()
    ENDIF
    
    DELAY(50)
WEND
END

' 编码器回零系统初始化
GLOBAL SUB InitEncoderHome()
    FOR i = 0 TO total_axis - 1
        BASE(i)
        ATYPE = 4                ' 脉冲+正交编码器
        UNITS = 1000             ' 脉冲当量
        SPEED = 200              ' 回零速度
        CREEP = 20               ' 爬行速度
        ACCEL = 1000             ' 加速度
        DECEL = 1000             ' 减速度
        HOMEWAIT = 20            ' 反找等待时间
        
        ' 信号映射
        DATUM_IN = 8 + i         ' 原点开关
        FWD_IN = 11 + i          ' 正限位
        REV_IN = 14 + i          ' 负限位
        
        ' 信号反转
        INVERT_IN(8 + i, ON)
        INVERT_IN(11 + i, ON)
        INVERT_IN(14 + i, ON)
        
        ' 编码器设置
        ENCODER_RATIO(1, 1)      ' 1:1比例
        
        encoder_home_status(i) = 0    ' 初始化为未回零
    NEXT
    
    PRINT "AB编码器回零系统初始化完成"
    PRINT "ATYPE=4: 脉冲+正交编码器"
END SUB

' 单轴回零
GLOBAL SUB DoSingleHome(axis_num)
    PRINT "开始轴", axis_num, "编码器回零..."
    
    BASE(axis_num)
    encoder_home_status(axis_num) = 1  ' 设置为回零中
    
    ' 记录回零前状态
    PRINT "回零前状态："
    PRINT "  DPOS：", DPOS(axis_num)
    PRINT "  MPOS：", MPOS(axis_num)
    PRINT "  原点信号：", IN(8 + axis_num)
    
    DIM enc_stat
    enc_stat = ENCODER_STATUS(axis_num)
    PRINT "  编码器状态：", HEX(enc_stat)
    
    ' 执行AB编码器回零（使用模式3）
    PRINT "执行DATUM(3)回零..."
    DATUM(3)
    WAIT UNTIL IDLE(axis_num) = -1
    DELAY(10)
    
    ' 检查结果
    IF AXISSTATUS(axis_num) = 0 THEN
        DPOS(axis_num) = 0
        MPOS(axis_num) = 0
        encoder_home_status(axis_num) = 2  ' 设置为已回零
        PRINT "✓ 轴", axis_num, "编码器回零成功！"
        PRINT "回零后状态："
        PRINT "  DPOS：", DPOS(axis_num)
        PRINT "  MPOS：", MPOS(axis_num)
    ELSE
        encoder_home_status(axis_num) = 3  ' 设置为回零失败
        PRINT "✗ 轴", axis_num, "编码器回零失败！"
        PRINT "错误状态：", HEX(AXISSTATUS(axis_num))
        CALL AnalyzeHomeError(axis_num)
    ENDIF
END SUB

' 多轴回零
GLOBAL SUB DoMultiHome()
    PRINT "开始多轴编码器回零..."
    
    FOR i = 0 TO total_axis - 1
        CALL DoSingleHome(i)
        IF encoder_home_status(i) <> 2 THEN
            PRINT "多轴回零失败，停止在轴", i
            RETURN
        ENDIF
        DELAY(500)  ' 轴间延时
    NEXT
    
    PRINT "✓ 所有轴编码器回零成功！"
END SUB

' 回零状态检查
GLOBAL SUB ShowHomeStatus()
    PRINT "=== 编码器回零状态检查 ==="
    
    FOR i = 0 TO total_axis - 1
        PRINT "轴", i, "详细状态："
        PRINT "  回零状态：", encoder_home_status(i), "(0-未回零,1-回零中,2-已回零,3-失败)"
        PRINT "  ATYPE：", ATYPE(i)
        PRINT "  DPOS位置：", DPOS(i)
        PRINT "  MPOS位置：", MPOS(i)
        PRINT "  轴状态：", HEX(AXISSTATUS(i))
        PRINT "  编码器值：", ENCODER(i)
        
        DIM enc_stat
        enc_stat = ENCODER_STATUS(i)
        PRINT "  编码器状态：", HEX(enc_stat)
        PRINT "  原点信号：", IN(8 + i)
        
        ' 运动状态
        IF IDLE(i) = -1 THEN
            PRINT "  运动状态：停止"
        ELSE
            PRINT "  运动状态：运动中"
        ENDIF
        
        PRINT ""
    NEXT
END SUB

' 验证所有轴回零
GLOBAL SUB VerifyAllHome()
    PRINT "=== 验证所有轴回零结果 ==="
    
    FOR i = 0 TO total_axis - 1
        CALL VerifyHome(i)
        PRINT ""
    NEXT
END SUB

' 验证单轴回零
GLOBAL SUB VerifyHome(axis_num)
    PRINT "验证轴", axis_num, "回零结果："
    
    BASE(axis_num)
    
    ' 检查1：轴状态正常
    IF AXISSTATUS(axis_num) = 0 THEN
        PRINT "✓ 轴状态正常"
    ELSE
        PRINT "✗ 轴状态异常：", HEX(AXISSTATUS(axis_num))
    ENDIF
    
    ' 检查2：位置已清零
    IF ABS(DPOS(axis_num)) < 0.1 AND ABS(MPOS(axis_num)) < 0.1 THEN
        PRINT "✓ 位置已清零"
        PRINT "  DPOS：", DPOS(axis_num)
        PRINT "  MPOS：", MPOS(axis_num)
    ELSE
        PRINT "⚠ 位置偏差"
        PRINT "  DPOS：", DPOS(axis_num)
        PRINT "  MPOS：", MPOS(axis_num)
    ENDIF
    
    ' 检查3：轴已停止
    IF IDLE(axis_num) = -1 THEN
        PRINT "✓ 轴已停止"
    ELSE
        PRINT "✗ 轴仍在运动"
    ENDIF
    
    ' 检查4：编码器信号
    DIM enc_stat
    enc_stat = ENCODER_STATUS(axis_num)
    PRINT "编码器状态：", HEX(enc_stat)
    
    ' 检查5：原点信号
    DIM home_sig
    home_sig = IN(8 + axis_num)
    IF home_sig = OFF THEN
        PRINT "✓ 已离开原点信号"
    ELSE
        PRINT "ℹ 仍在原点信号上"
    ENDIF
END SUB

' 回零错误分析
GLOBAL SUB AnalyzeHomeError(axis_num)
    DIM status_val
    status_val = AXISSTATUS(axis_num)
    
    PRINT "轴", axis_num, "回零错误分析："
    
    IF status_val AND 1 THEN
        PRINT "  ✗ 轴错误标志置位"
    ENDIF
    
    IF status_val AND 2 THEN
        PRINT "  ✗ 碰到正向硬限位"
    ENDIF
    
    IF status_val AND 4 THEN
        PRINT "  ✗ 碰到负向硬限位"
    ENDIF
    
    IF status_val AND 8 THEN
        PRINT "  ✗ 急停信号有效"
    ENDIF
    
    IF status_val AND 16 THEN
        PRINT "  ✗ 驱动器报警"
    ENDIF
    
    ' 编码器信号分析
    DIM enc_stat
    enc_stat = ENCODER_STATUS(axis_num)
    PRINT "编码器诊断："
    PRINT "  ATYPE设置：", ATYPE(axis_num)
    PRINT "  编码器状态：", HEX(enc_stat)
    PRINT "  编码器值：", ENCODER(axis_num)
    PRINT "  MPOS位置：", MPOS(axis_num)
    
    ' 原点信号分析
    DIM home_sig
    home_sig = IN(8 + axis_num)
    PRINT "  原点信号：", home_sig
    
    PRINT "故障排除建议："
    PRINT "  1. 检查编码器AB信号连接"
    PRINT "  2. 检查编码器供电（5V/24V）"
    PRINT "  3. 检查原点开关连接"
    PRINT "  4. 确认ATYPE=4（正交编码器）"
    PRINT "  5. 检查信号屏蔽和接地"
END SUB

' 编码器信号测试
GLOBAL SUB TestEncoderSignal(axis_num)
    PRINT "=== 轴", axis_num, "编码器信号测试 ==="
    
    BASE(axis_num)
    
    PRINT "请手动转动编码器，观察10秒..."
    FOR i = 1 TO 100
        DIM enc_val, mpos_val, enc_stat
        enc_val = ENCODER(axis_num)
        mpos_val = MPOS(axis_num)
        enc_stat = ENCODER_STATUS(axis_num)
        
        PRINT "ENCODER:", enc_val, " MPOS:", mpos_val, " STATUS:", HEX(enc_stat)
        DELAY(100)
    NEXT
    
    PRINT "编码器信号测试完成"
END SUB
