'=============================================================================
' 螺丝机操作界面程序
' 提供简单的菜单操作界面
' 配合螺丝机控制程序使用
'=============================================================================

'注意：请先运行螺丝机控制程序.bas，再运行本界面程序

'界面主程序
CALL ShowMainMenu()

'主菜单循环
WHILE 1
    CALL ProcessMenuInput()
    DELAY(100)
WEND

END

'================ 显示主菜单 ================
GLOBAL SUB ShowMainMenu()
    PRINT ""
    PRINT "========================================"
    PRINT "        螺丝机控制系统 v1.0"
    PRINT "========================================"
    PRINT ""
    PRINT "【系统操作】"
    PRINT "1 - 系统回零"
    PRINT "2 - 显示系统状态"
    PRINT "3 - 急停恢复"
    PRINT ""
    PRINT "【作业操作】"
    PRINT "4 - 左侧打螺丝"
    PRINT "5 - 右侧打螺丝"
    PRINT "6 - 停止作业"
    PRINT ""
    PRINT "【参数设置】"
    PRINT "7 - 设置螺丝位置"
    PRINT "8 - 设置螺丝数量"
    PRINT "9 - 设置吸螺丝位置"
    PRINT ""
    PRINT "【测试功能】"
    PRINT "A - 测试吸螺丝"
    PRINT "B - 测试电批"
    PRINT "C - 测试运动"
    PRINT "D - 测试单个螺丝"
    PRINT ""
    PRINT "【配置管理】"
    PRINT "E - 保存配置"
    PRINT "F - 加载配置"
    PRINT "G - 显示所有位置"
    PRINT "H - 配置验证"
    PRINT ""
    PRINT "0 - 显示帮助"
    PRINT "========================================"
    PRINT "请输入选择："
END SUB

'================ 处理菜单输入 ================
GLOBAL SUB ProcessMenuInput()
    '这里简化处理，实际应用中可以通过串口或HMI接收命令
    '示例：通过MODBUS寄存器接收菜单选择
    
    DIM menu_choice
    menu_choice = MODBUS_REG(1000)  ' 从寄存器1000读取菜单选择
    
    IF menu_choice = 0 THEN
        RETURN                      ' 无输入
    ENDIF
    
    '处理菜单选择
    IF menu_choice = 1 THEN
        CALL MenuSystemHome()
    ELSEIF menu_choice = 2 THEN
        CALL MenuShowStatus()
    ELSEIF menu_choice = 3 THEN
        CALL MenuEmergencyReset()
    ELSEIF menu_choice = 4 THEN
        CALL MenuLeftScrew()
    ELSEIF menu_choice = 5 THEN
        CALL MenuRightScrew()
    ELSEIF menu_choice = 6 THEN
        CALL MenuStopJob()
    ELSEIF menu_choice = 7 THEN
        CALL MenuSetScrewPosition()
    ELSEIF menu_choice = 8 THEN
        CALL MenuSetScrewCount()
    ELSEIF menu_choice = 9 THEN
        CALL MenuSetPickPosition()
    ELSEIF menu_choice = 10 THEN     ' A
        CALL MenuTestPickScrew()
    ELSEIF menu_choice = 11 THEN     ' B
        CALL MenuTestScrewDriver()
    ELSEIF menu_choice = 12 THEN     ' C
        CALL MenuTestMovement()
    ELSEIF menu_choice = 13 THEN     ' D
        CALL MenuTestSingleScrew()
    ELSEIF menu_choice = 14 THEN     ' E
        CALL MenuSaveConfig()
    ELSEIF menu_choice = 15 THEN     ' F
        CALL MenuLoadConfig()
    ELSEIF menu_choice = 16 THEN     ' G
        CALL MenuShowAllPositions()
    ELSEIF menu_choice = 17 THEN     ' H
        CALL MenuValidateConfig()
    ELSEIF menu_choice = 0 THEN
        CALL ShowHelp()
    ELSE
        PRINT "无效选择，请重新输入"
    ENDIF
    
    '清除菜单选择
    MODBUS_REG(1000) = 0
    
    '显示菜单
    CALL ShowMainMenu()
END SUB

'================ 菜单功能实现 ================

GLOBAL SUB MenuSystemHome()
    PRINT "执行系统回零..."
    IF gv_SystemStatus = 0 THEN
        STOPTASK 2
        RUN "HomeTask", 2
        PRINT "回零任务已启动"
    ELSE
        PRINT "系统忙，无法回零"
    ENDIF
END SUB

GLOBAL SUB MenuShowStatus()
    CALL ShowSystemStatus()
END SUB

GLOBAL SUB MenuEmergencyReset()
    CALL EmergencyReset()
END SUB

GLOBAL SUB MenuLeftScrew()
    PRINT "开始左侧打螺丝..."
    IF gv_SystemStatus = 0 AND CheckAllAxisHomed() = 1 THEN
        STOPTASK 2
        RUN "LeftScrewTask", 2
        PRINT "左侧打螺丝任务已启动"
    ELSE
        PRINT "系统未就绪或未回零"
    ENDIF
END SUB

GLOBAL SUB MenuRightScrew()
    PRINT "开始右侧打螺丝..."
    IF gv_SystemStatus = 0 AND CheckAllAxisHomed() = 1 THEN
        STOPTASK 2
        RUN "RightScrewTask", 2
        PRINT "右侧打螺丝任务已启动"
    ELSE
        PRINT "系统未就绪或未回零"
    ENDIF
END SUB

GLOBAL SUB MenuStopJob()
    PRINT "停止当前作业..."
    RAPIDSTOP(2)
    STOPTASK 2
    gv_SystemStatus = 0
    gv_CurrentScrew = 0
    OP(0, OFF)                      ' 关闭吸螺丝
    PRINT "作业已停止"
END SUB

GLOBAL SUB MenuSetScrewPosition()
    '从MODBUS寄存器读取参数
    DIM side, screw_num, pos_x, pos_y, pos_z
    side = MODBUS_REG(1001)         ' 侧边：1-左，2-右
    screw_num = MODBUS_REG(1002)    ' 螺丝编号
    pos_x = MODBUS_IEEE(1003)       ' X位置
    pos_y = MODBUS_IEEE(1004)       ' Y位置
    pos_z = MODBUS_IEEE(1005)       ' Z位置
    
    IF side > 0 AND screw_num > 0 THEN
        CALL SetScrewPosition(side, screw_num, pos_x, pos_y, pos_z)
        '清除参数
        MODBUS_REG(1001) = 0
        MODBUS_REG(1002) = 0
        MODBUS_IEEE(1003) = 0
        MODBUS_IEEE(1004) = 0
        MODBUS_IEEE(1005) = 0
    ELSE
        PRINT "请先在HMI中设置螺丝位置参数"
    ENDIF
END SUB

GLOBAL SUB MenuSetScrewCount()
    DIM side, count
    side = MODBUS_REG(1006)         ' 侧边
    count = MODBUS_REG(1007)        ' 数量
    
    IF side > 0 AND count > 0 THEN
        CALL SetScrewCount(side, count)
        MODBUS_REG(1006) = 0
        MODBUS_REG(1007) = 0
    ELSE
        PRINT "请先在HMI中设置螺丝数量参数"
    ENDIF
END SUB

GLOBAL SUB MenuSetPickPosition()
    DIM pos_x, pos_y, pos_z
    pos_x = MODBUS_IEEE(1008)       ' X位置
    pos_y = MODBUS_IEEE(1009)       ' Y位置
    pos_z = MODBUS_IEEE(1010)       ' Z位置
    
    IF pos_x <> 0 OR pos_y <> 0 OR pos_z <> 0 THEN
        CALL SetPickPosition(pos_x, pos_y, pos_z)
        MODBUS_IEEE(1008) = 0
        MODBUS_IEEE(1009) = 0
        MODBUS_IEEE(1010) = 0
    ELSE
        PRINT "请先在HMI中设置吸螺丝位置参数"
    ENDIF
END SUB

GLOBAL SUB MenuTestPickScrew()
    CALL TestPickScrew()
END SUB

GLOBAL SUB MenuTestScrewDriver()
    CALL TestScrewDriver()
END SUB

GLOBAL SUB MenuTestMovement()
    DIM test_x, test_y, test_z
    test_x = MODBUS_IEEE(1011)      ' 测试X位置
    test_y = MODBUS_IEEE(1012)      ' 测试Y位置
    test_z = MODBUS_IEEE(1013)      ' 测试Z位置
    
    IF test_x <> 0 OR test_y <> 0 OR test_z <> 0 THEN
        CALL TestMovement(test_x, test_y, test_z)
        MODBUS_IEEE(1011) = 0
        MODBUS_IEEE(1012) = 0
        MODBUS_IEEE(1013) = 0
    ELSE
        PRINT "请先在HMI中设置测试位置参数"
    ENDIF
END SUB

GLOBAL SUB MenuTestSingleScrew()
    DIM side, screw_num
    side = MODBUS_REG(1014)         ' 侧边
    screw_num = MODBUS_REG(1015)    ' 螺丝编号
    
    IF side > 0 AND screw_num > 0 THEN
        CALL TestSingleScrew(side, screw_num)
        MODBUS_REG(1014) = 0
        MODBUS_REG(1015) = 0
    ELSE
        PRINT "请先在HMI中设置测试螺丝参数"
    ENDIF
END SUB

GLOBAL SUB MenuSaveConfig()
    CALL SaveConfiguration()
END SUB

GLOBAL SUB MenuLoadConfig()
    CALL LoadConfiguration()
END SUB

GLOBAL SUB MenuShowAllPositions()
    CALL ShowAllScrewPositions()
END SUB

GLOBAL SUB MenuValidateConfig()
    CALL ValidateConfiguration()
END SUB

'================ HMI接口说明 ================
GLOBAL SUB ShowHMIInterface()
    PRINT "=== HMI接口寄存器说明 ==="
    PRINT ""
    PRINT "【控制寄存器】"
    PRINT "MODBUS_REG(1000) - 菜单选择"
    PRINT ""
    PRINT "【参数寄存器】"
    PRINT "MODBUS_REG(1001) - 螺丝侧边(1-左,2-右)"
    PRINT "MODBUS_REG(1002) - 螺丝编号"
    PRINT "MODBUS_IEEE(1003) - 螺丝X位置"
    PRINT "MODBUS_IEEE(1004) - 螺丝Y位置"
    PRINT "MODBUS_IEEE(1005) - 螺丝Z位置"
    PRINT "MODBUS_REG(1006) - 螺丝数量侧边"
    PRINT "MODBUS_REG(1007) - 螺丝数量"
    PRINT "MODBUS_IEEE(1008) - 吸螺丝X位置"
    PRINT "MODBUS_IEEE(1009) - 吸螺丝Y位置"
    PRINT "MODBUS_IEEE(1010) - 吸螺丝Z位置"
    PRINT "MODBUS_IEEE(1011) - 测试X位置"
    PRINT "MODBUS_IEEE(1012) - 测试Y位置"
    PRINT "MODBUS_IEEE(1013) - 测试Z位置"
    PRINT "MODBUS_REG(1014) - 测试螺丝侧边"
    PRINT "MODBUS_REG(1015) - 测试螺丝编号"
    PRINT ""
    PRINT "【状态寄存器】"
    PRINT "MODBUS_REG(0) - 系统状态"
    PRINT "MODBUS_REG(1) - 左侧螺丝数量"
    PRINT "MODBUS_REG(2) - 右侧螺丝数量"
    PRINT "MODBUS_REG(3) - 当前螺丝编号"
    PRINT "MODBUS_REG(10-13) - 各轴回零状态"
    PRINT "MODBUS_IEEE(20-23) - 各轴当前位置"
END SUB

'================ 快速设置功能 ================
GLOBAL SUB QuickSetup()
    PRINT "=== 快速设置 ==="
    PRINT "正在执行快速设置..."
    
    '调用配置程序
    CALL ConfigureSystem()
    CALL ConfigureScrewPositions()
    CALL ConfigureMotionParameters()
    
    '验证配置
    CALL ValidateConfiguration()
    
    '保存配置
    CALL SaveConfiguration()
    
    PRINT "快速设置完成！"
    PRINT "建议执行系统回零后开始作业"
END SUB
