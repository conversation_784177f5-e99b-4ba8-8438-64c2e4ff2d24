===============================================================================
                        两轴插补测试正确版使用说明
                        基于RTBasic语法规范的完整版本
===============================================================================

【程序概述】

本程序是基于官方手册5.2插补运动章节编写的完整测试程序，采用正确的RTBasic语法结构，
支持输入电平触发，包含8个完整的插补测试项目。

程序特点：
✅ 完全符合RTBasic语法规范
✅ 采用线性程序结构，参考官方例程
✅ 使用官方推荐的输入检测方式（IN_SCAN + IN_EVENT）
✅ 自动设置VP_SPEED显示单轴速度
✅ 包含详细的理论计算和说明
✅ 支持输入电平自动触发测试
✅ 提供完整的示波器监控指导

【程序结构分析】

=== 核心改进点 ===
```
1. 正确的RTBasic程序结构：
   - 线性程序结构，避免复杂函数嵌套
   - 使用WHILE 1...WEND主循环保持程序运行
   - 添加END语句明确程序结构

2. 官方推荐的输入检测：
   - 使用IN_SCAN(0, 7)检测IO变动
   - 使用IN_EVENT(i) > 0检测上升沿
   - 效率高，可靠性好

3. VP_SPEED单轴显示设置：
   - SYSTEM_ZSET = SYSTEM_ZSET AND (NOT 1)
   - 在BASE()之前设置，确保生效
   - 显示各轴单轴速度，便于分析
```

=== 程序执行流程 ===
```
1. 初始化阶段：
   - 设置VP_SPEED显示单轴速度
   - 配置轴组和运动参数
   - 显示输入引脚分配

2. 主循环阶段：
   - 持续监控输入电平变化
   - 检测上升沿触发测试
   - 自动执行对应测试项目

3. 测试执行：
   - 自动触发示波器
   - 执行具体插补测试
   - 显示测试结果和分析
```

【输入引脚分配】

=== 默认分配（IN0-IN7）===
```
IN0 - 两轴直线插补测试（Test1_Linear）
功能：验证基础直线插补原理
轨迹：从(0,0)到(100,100)的45度直线
理论：合成距离141.42，各轴速度70.71
观察：VP_SPEED(0)和VP_SPEED(1)应该相等且同步

IN1 - 两轴圆弧插补测试（Test2_Circular）
功能：验证圆弧插补轨迹生成
轨迹：三点圆弧插补，逆时针方向
观察：两轴速度按圆弧轨迹协调变化，轨迹平滑

IN2 - 非连续插补测试（Test3_NonContinuous）
功能：展示MERGE=OFF时的运动特性
轨迹：矩形轨迹，四段直线
问题：每段之间有停顿，效率低，冲击大
观察：速度曲线有明显断点

IN3 - 连续插补测试（Test4_Continuous）
功能：展示MERGE=ON时的改善效果
轨迹：矩形轨迹，连续执行
改善：段间连续，无停顿
问题：拐角处仍有冲击
观察：速度曲线连续，但拐角有尖峰

IN4 - 前瞻拐角减速测试（Test5_CornerDecel）
功能：CORNER_MODE=2的拐角减速效果
特点：拐角处按角度自动减速
优势：减少冲击，保护机械
观察：拐角处速度下降但不为0

IN5 - 自动倒角测试（Test6_AutoChamfer）
功能：CORNER_MODE=32的自动倒角效果
特点：改变轨迹，拐角处插入圆弧
优势：保持高速，轨迹平滑
观察：拐角处轨迹变为圆弧，速度保持较高

IN6 - 组合前瞻测试（Test7_Combined）
功能：CORNER_MODE=2+32的组合效果
特点：既有倒角又有减速
优势：平衡速度和精度
观察：综合效果最佳，适合大多数应用

IN7 - 复杂轨迹测试（Test8_Complex）
功能：直线+圆弧的复杂轨迹连续插补
轨迹：6段混合轨迹（直线+圆弧+直线+圆弧+直线+直线）
验证：复杂路径的连续性
观察：直线和圆弧之间完全连续
```

【硬件连接要求】

=== 输入信号规格 ===
```
电压等级：24V DC（标准）或5V DC（根据控制器规格）
信号类型：数字开关信号
触发方式：上升沿触发（0V→24V）
信号宽度：建议≥100ms（避免抖动误触发）
信号频率：不建议过于频繁，间隔≥1秒
```

=== 连接方式 ===
```
标准连接：
IN0+ ← 24V信号源（测试1触发）
IN0- ← 0V (GND)
IN1+ ← 24V信号源（测试2触发）
IN1- ← 0V (GND)
...以此类推到IN7

PLC控制连接：
PLC_Y0 → IN0+, PLC_COM → IN0-
PLC_Y1 → IN1+, PLC_COM → IN1-
...以此类推

按钮控制连接：
24V+ → 按钮1 → IN0+, 24V- → IN0-
24V+ → 按钮2 → IN1+, 24V- → IN1-
...以此类推
```

【使用方法】

=== 第一次使用流程 ===
```
1. 程序加载：
   - 将"两轴插补测试正确版.bas"加载到正运动控制器
   - 程序自动启动并显示初始化信息
   - 显示输入引脚分配和监控说明

2. 硬件连接：
   - 根据引脚分配连接输入信号线
   - 确认信号电压等级匹配（24V或5V）
   - 测试信号连通性

3. 示波器设置：
   - 连接示波器到VP_SPEED(0)和VP_SPEED(1)
   - 设置合适的时间和电压刻度
   - 程序会在每次测试前自动触发示波器

4. 基础测试：
   - 给IN0施加24V信号（直线插补测试）
   - 观察程序输出信息和电机运动
   - 检查示波器速度曲线
   - 确认基本功能正常

5. 逐项测试：
   - 依次触发IN1-IN7，执行各项测试
   - 观察不同插补模式的效果差异
   - 对比分析速度曲线特性
   - 记录测试结果和现象
```

=== 日常使用流程 ===
```
1. 系统上电：
   - 控制器上电，程序自动运行
   - 显示初始化信息和引脚分配
   - 进入输入电平监控模式

2. 选择测试：
   - 根据需要给对应输入引脚施加高电平
   - 程序自动检测上升沿并执行测试
   - 观察运动过程和速度曲线变化

3. 结果分析：
   - 通过示波器分析速度特性
   - 评估插补质量和连续性
   - 记录测试数据和观察现象

4. 参数调整：
   - 根据测试结果调整运动参数
   - 优化前瞻和平滑设置
   - 重新测试验证效果
```

【示波器监控】

=== 自动监控设置 ===
```
程序特点：
- 每次测试前自动执行TRIGGER
- 自动显示监控信号说明
- VP_SPEED已设置为单轴速度显示

推荐监控信号：
VP_SPEED(0) - X轴单轴速度（可正可负）
VP_SPEED(1) - Y轴单轴速度（可正可负）
MSPEED(0)   - X轴分速度（对比验证）
MSPEED(1)   - Y轴分速度（对比验证）

显示设置：
VP_SPEED(0)：垂直刻度100，偏移0
VP_SPEED(1)：垂直刻度100，偏移-60
MSPEED(0)：垂直刻度100，偏移-120
MSPEED(1)：垂直刻度100，偏移-180
```

=== 观察要点 ===
```
直线插补（IN0）：
✅ VP_SPEED(0)和VP_SPEED(1)应该相等（45度直线）
✅ 两轴同时启动，同时停止
✅ 速度曲线对称，无突变

圆弧插补（IN1）：
✅ VP_SPEED(0)和VP_SPEED(1)按圆弧轨迹变化
✅ 速度曲线平滑连续，无尖峰
✅ 可以显示正负值，反映方向变化

连续插补对比（IN2 vs IN3）：
IN2（非连续）：段间速度降到0，有明显断点
IN3（连续）：段间无断点，整体连续

前瞻功能（IN4-IN7）：
✅ 拐角处速度平滑变化
✅ 轨迹质量改善
✅ 机械冲击减少
```

【理论验证】

=== 直线插补理论计算 ===
```
测试1（IN0）理论验证：
运动：MOVE(100, 100)
合成距离：√(100² + 100²) = 141.42 units
主轴速度：FORCE_SPEED = 100 units/s
X轴实际速度：100 × 100 / 141.42 = 70.71 units/s
Y轴实际速度：100 × 100 / 141.42 = 70.71 units/s

验证方法：
通过示波器测量VP_SPEED(0)和VP_SPEED(1)
应该都约为70.71，且相等
```

=== 连续插补效果验证 ===
```
测试3 vs 测试4对比：
测试3（MERGE=OFF）：
- 每段运动后都有WAIT IDLE
- 速度曲线有4个独立的钟形曲线
- 段间速度降到0，有停顿时间

测试4（MERGE=ON）：
- 连续发送4段MOVE指令
- 速度曲线连续，无中间断点
- 拐角处可能有速度尖峰

验证标准：
连续插补成功 = 无中间停顿 + 速度曲线连续
```

=== 前瞻功能效果验证 ===
```
测试5（CORNER_MODE=2）：
- 拐角处速度按角度比例减速
- 不改变运动轨迹
- 速度曲线在拐角处有坡谷

测试6（CORNER_MODE=32）：
- 拐角处轨迹变为圆弧
- 速度保持较高水平
- 轨迹平滑过渡

测试7（CORNER_MODE=2+32）：
- 综合两种效果
- 既有轨迹倒角又有速度控制
- 平衡速度和精度
```

【故障排除】

=== 常见问题及解决方法 ===
```
问题1：程序加载后没有响应
原因：程序结构问题或语法错误
解决：检查程序是否正确加载，查看错误信息

问题2：输入电平无响应
原因：硬件连接或信号问题
解决：
- 检查输入信号电压（24V或5V）
- 确认接线正确（+/-极性）
- 测试信号连通性
- 检查信号宽度（≥100ms）

问题3：VP_SPEED显示异常
原因：SYSTEM_ZSET设置问题
解决：程序已自动设置，如有问题请重新加载程序

问题4：运动异常或不平滑
原因：轴参数设置或机械问题
解决：
- 检查SPEED、ACCEL、DECEL参数
- 确认机械连接和润滑状态
- 调整SRAMP和VP_MODE参数

问题5：示波器无信号
原因：示波器连接或设置问题
解决：
- 确认示波器连接正确
- 检查信号线和接地
- 程序会自动执行TRIGGER
```

=== 调试建议 ===
```
1. 逐步测试：
   - 先测试IN0（最简单的直线插补）
   - 确认基本功能正常后再测试其他

2. 参数调整：
   - 根据实际机械系统调整速度和加速度
   - 优化SRAMP和VP_MODE参数
   - 调整前瞻参数（ZSMOOTH、角度等）

3. 对比分析：
   - 重点对比IN2和IN3的差异（连续插补效果）
   - 分析IN4-IN7的前瞻功能效果
   - 记录最佳参数组合

4. 性能优化：
   - 根据应用需求选择合适的前瞻模式
   - 平衡速度和精度要求
   - 考虑机械系统的承受能力
```

【程序优势】

=== 技术优势 ===
```
✅ 完全符合RTBasic语法规范
✅ 采用官方推荐的编程方式
✅ 线性程序结构，稳定可靠
✅ 自动化程度高，操作简便
✅ 理论与实践相结合
✅ 提供完整的分析工具
```

=== 学习价值 ===
```
✅ 深入理解插补运动原理
✅ 掌握连续插补的实现方法
✅ 了解前瞻预处理的各种模式
✅ 学会使用示波器分析运动质量
✅ 为实际应用提供参考方案
✅ 验证理论计算的准确性
```

【总结】

本程序是学习和验证正运动控制器插补功能的完整解决方案：
- 基于官方手册理论，确保准确性
- 采用正确的RTBasic语法，保证兼容性
- 提供8个渐进式测试，覆盖全面
- 支持输入电平自动触发，便于集成
- 包含详细的理论计算和分析指导

通过使用本程序，可以全面了解和掌握两轴插补运动的原理和应用，
为实际项目开发提供坚实的技术基础。

===============================================================================
