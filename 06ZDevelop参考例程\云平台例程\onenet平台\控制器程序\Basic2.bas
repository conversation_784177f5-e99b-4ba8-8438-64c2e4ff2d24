global number
number = 0
base(0)
atype = 1
units = 2500
speed = 50
accel = 200
dpos = 0

delay(3000)

dim curcmd
curcmd = 0

while 1
   
    if modbus_reg(400) <> 0 then
		curcmd = modbus_reg(400)
        modbus_reg(400) = 0
		if curcmd = 2 then
			stoptask 4
			runtask 4,movetest
		elseif curcmd = 1 then
        	stoptask 4
            cancel(2)
			vmove(1)
        elseif curcmd = 3 then
			speed = 6010
        elseif curcmd = 4 then
			speed = 30
        elseif curcmd = 5 then
            stoptask 4
			cancel(2)
		endif
	endif
    modbus_ieee(100) = vp_speed(0)
	'?port_status(10)
wend

global sub movetest()
	while 1
 		move(10)
		wait idle
		delay(3000)
		move(-10)
		wait idle
		delay(3000)
		number = number + 1 
		if number > 100000 then
			number = 0
		endif   
    wend
end sub 