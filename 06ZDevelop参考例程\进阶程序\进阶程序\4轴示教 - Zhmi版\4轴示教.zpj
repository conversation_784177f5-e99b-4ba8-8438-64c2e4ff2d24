[Controller]
TypeName=
IfUpload=0
UserVersion=1.0
Files=3
Breaks=0
Watches=19
[FileList]
File1=Basic1.bas
FileType1=1
AutoRun1=-1
File2=info.zlb
FileType2=2
AutoRun2=-1
File3=Hmi.hmi
FileType3=5
AutoRun3=0
[OpenList]
OpenFile1=Basic1.bas
OpenLine1=3
OpenFile2=info.zlb
OpenLine2=5
OpenFile3=Hmi.hmi
OpenLine3=0
[WatchList]
Watch1=pagenum
Watch2=fileflag
Watch3=FILEFLASHFLAG
Watch4=TOTALPANGE
Watch5=totalfilenum
Watch6=FILENUMMAX
Watch7=ONEPAGENUM
Watch8=i
Watch9=filetoflash(0)
Watch10=filename
Watch11=shownamelist(0)
Watch12=LINESPACE
Watch13=linenum
Watch14=filenum
Watch15=filelinepara(0)
Watch16=browsepage
Watch17=FILENAMELENG
Watch18=SD"+tostr(i+1)+".BIN
Watch19=ZINDEX_ARRAY(unameindex((unum-1) mod ONEPAGENUM ))
