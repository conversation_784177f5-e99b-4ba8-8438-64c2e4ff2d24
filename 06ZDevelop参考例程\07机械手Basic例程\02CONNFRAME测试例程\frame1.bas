base(0,1)
RAPIDSTOP
wait IDLE

ATYPE=1,1
units=10,10
dpos=0,0
CLUTCH_RATE=0
SPEED=100,100
accel=1000,1000
CREEP=10,10

base(2,3)
ATYPE=0,0
table(10,20,20,3600,3600)
DPOS=0,0
UNITS=1,10
speed=100,100
ACCEL=1000,1000

merge=1
CORNER_MODE=2
DECEL_ANGLE=15*(pi/180)
STOP_ANGLE=45*(pi/180)



while 1

	if SCAN_EVENT(in(0))<0 then
		base(2,3)
		CONNREFRAME(1,10,0,1)
		wait LOADED
	endif
	if SCAN_EVENT(in(0))>0 then
		BASE(0,1)
		CONNFRAME(1,10,2,3)
		wait LOADED
	endif
	
	if SCAN_EVENT(in(1))=1 then
		if MTYPE(0)=33 then
		base(2,3)
		moveabs(30,20)
		wait idle
		elseif mtype(0)<>33 then
		base(0,1)
		MOVEABS(10,10)
		endif
	endif
wend