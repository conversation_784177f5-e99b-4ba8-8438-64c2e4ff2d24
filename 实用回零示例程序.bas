'=============================================================================
' 实用Z信号回零示例程序
' 基于正运动官方手册和最佳实践
' 专门针对Z信号回零（模式2和模式6）
' 适用于ZMC系列运动控制器，要求ATYPE=4或7
'=============================================================================

'================ 全局变量定义 ================
GLOBAL gv_AxisMax                    '轴数量
GLOBAL ga_AxisNum(3)                 '轴号定义
GLOBAL gv_Status                     '系统状态：0-停止，1-运行中，2-已完成
GLOBAL ga_StatusAxis(3)              '各轴状态：0-未回零，1-回零中，2-已回零，3-失败
GLOBAL ga_HomeMode(3)                '回零方式：2-Z信号反向，6-原点+Z信号负向
GLOBAL ga_HomeEnable(3)              '回零使能：0-使能，1-禁用

'常量定义
CONST MAX_AXES = 3       '最大轴数
CONST HOME_SPEED = 200   '回零速度
CONST CREEP_SPEED = 20   '爬行速度

'================ 主程序 ================
PRACTICAL_INIT()     '系统初始化

PRINT "=== 实用Z信号回零示例程序 ==="
PRINT "基于正运动官方手册和最佳实践"
PRINT ""
PRINT "Z信号反向回零（模式2）："
PRINT "IN0 - 单轴回零（轴0）"
PRINT "IN1 - 多轴顺序回零"
PRINT "IN2 - 多轴同时回零"
PRINT ""
PRINT "原点+Z信号负向（模式6）："
PRINT "IN3 - 单轴回零（轴0）"
PRINT "IN4 - 多轴顺序回零"
PRINT "IN5 - 多轴同时回零"
PRINT ""
PRINT "系统功能："
PRINT "IN6 - 状态检查"
PRINT "IN7 - 紧急停止"

WHILE 1         '主循环
    PRACTICAL_SCAN() '主程序扫描
WEND

'================ 初始化子程序 ================
GLOBAL SUB PRACTICAL_INIT()
    '--- 变量初始化 ---
    gv_AxisMax = 3
    ga_AxisNum(0) = 0                    'X轴（0轴）
    ga_AxisNum(1) = 1                    'Y轴（1轴）
    ga_AxisNum(2) = 2                    'Z轴（2轴）
    gv_Status = 0
    
    '初始化轴状态数组
    FOR i = 0 TO 2
        ga_StatusAxis(i) = 0
        ga_HomeMode(i) = 2               '默认回零模式2（Z信号反向回零）
        ga_HomeEnable(i) = 0             '默认使能回零
    NEXT
    
    '--- 轴参数初始化 ---
    CALL PracticalAxisInit()

    PRINT "系统初始化完成"
END SUB

'================ Z信号回零轴参数初始化 ================
GLOBAL SUB PracticalAxisInit()
    BASE(ga_AxisNum(0), ga_AxisNum(1), ga_AxisNum(2))
    ATYPE = 4, 4, 4                      'Z信号回零必须：脉冲+编码器
    UNITS = 1000, 1000, 1000             '脉冲当量（建议1000以上）
    ACCEL = 1000, 1000, 1000             '加速度
    DECEL = 1000, 1000, 1000             '减速度
    SPEED = 100, 100, 100                'Z信号回零速度（不宜过快）
    CREEP = 10, 10, 10                   'Z信号寻找速度（爬行速度）
    HOMEWAIT = 20, 20, 20                '反找等待时间(ms)
    
    '原点输入设置
    DATUM_IN = 8, 9, 10                  '原点输入：IN8, IN9, IN10
    INVERT_IN(8, ON)                     '反转原点信号（ZMC系列OFF有效）
    INVERT_IN(9, ON)
    INVERT_IN(10, ON)
    
    '限位输入设置
    FWD_IN = 11, 12, 13                  '正向限位
    REV_IN = 14, 15, 16                  '负向限位
    INVERT_IN(11, ON)
    INVERT_IN(12, ON)
    INVERT_IN(13, ON)
    INVERT_IN(14, ON)
    INVERT_IN(15, ON)
    INVERT_IN(16, ON)
    
    '关闭所有输出
    FOR i = 0 TO 7
        OP(i, OFF)
    NEXT
END SUB

'================ 主程序扫描子程序 ================
GLOBAL SUB PRACTICAL_SCAN()
    '--- 扫描输入信号事件 ---
    ' Z信号反向回零（模式2）
    IF SCAN_EVENT(IN(0)) > 0 THEN       '模式2单轴回零
        CALL practical_home_axis_mode(0, 2)
    ENDIF

    IF SCAN_EVENT(IN(1)) > 0 THEN       '模式2多轴顺序回零
        CALL practical_home_seq_mode(2)
    ENDIF

    IF SCAN_EVENT(IN(2)) > 0 THEN       '模式2多轴同时回零
        CALL practical_home_sim_mode(2)
    ENDIF

    ' 原点+Z信号负向（模式6）
    IF SCAN_EVENT(IN(3)) > 0 THEN       '模式6单轴回零
        CALL practical_home_axis_mode(0, 6)
    ENDIF

    IF SCAN_EVENT(IN(4)) > 0 THEN       '模式6多轴顺序回零
        CALL practical_home_seq_mode(6)
    ENDIF

    IF SCAN_EVENT(IN(5)) > 0 THEN       '模式6多轴同时回零
        CALL practical_home_sim_mode(6)
    ENDIF

    IF SCAN_EVENT(IN(6)) > 0 THEN       '状态检查
        CALL PracticalCheckStatus()
    ENDIF

    '紧急停止
    IF IN(7) = ON THEN
        CALL practical_stop()
    ENDIF
    
    '状态指示灯
    IF gv_Status = 1 THEN               '回零中
        OP(0, TICKS AND 1)              '红灯闪烁
        OP(1, OFF)
    ELSEIF gv_Status = 2 THEN           '回零完成
        OP(0, OFF)
        OP(1, ON)                       '绿灯亮
    ELSE                                '待机
        OP(0, OFF)
        OP(1, OFF)
    ENDIF
    
    DELAY(50)
END SUB

'================ 单轴Z信号回零处理 ================
GLOBAL SUB practical_home_axis_mode(num, home_mode)
    IF gv_Status <> 1 THEN              '判断系统状态不在回零中
        CALL practical_task_axis_mode(num, home_mode)
    ELSE
        PRINT "系统忙，无法回零"
    ENDIF
END SUB



GLOBAL SUB practical_task_home_axis(num)
    PRINT "=== 单轴回零：轴", num, " ==="
    
    gv_Status = 1                        '状态切换成回零中
    ga_StatusAxis(num) = 1
    
    '执行回零前检查
    IF PracticalSystemCheck() = 0 THEN
        PRINT "系统检查失败，停止回零"
        gv_Status = 0
        ga_StatusAxis(num) = 3
        RETURN
    ENDIF
    
    BASE(num)
    
    '清除轴状态
    AXIS_STOPREASON = 0
    
    '根据回零模式执行不同的回零指令
    IF ga_HomeMode(num) = 3 THEN         '正向找原点
        DATUM(3)
    ELSEIF ga_HomeMode(num) = 4 THEN     '负向找原点
        DATUM(4)
    ELSEIF ga_HomeMode(num) = 13 THEN    '正向找原点+限位反找
        DATUM(13)
    ELSEIF ga_HomeMode(num) = 14 THEN    '负向找原点+限位反找
        DATUM(14)
    ELSE
        DATUM(3)                         '默认使用模式3
    ENDIF
    
    '等待回零完成
    WAIT UNTIL IDLE(num) = -1
    DELAY(10)  '状态稳定延时
    
    '检查回零结果
    IF AXISSTATUS(num) = 0 THEN
        '回零成功处理
        DPOS(num) = 0                    '清零位置
        MPOS(num) = 0                    '清零编码器位置
        ga_StatusAxis(num) = 2           '设置为已回零
        PRINT "轴", num, "回零成功！位置：", DPOS(num)
    ELSE
        '回零失败处理
        ga_StatusAxis(num) = 3           '设置为回零失败
        PRINT "轴", num, "回零失败！状态：", HEX(AXISSTATUS(num))
        CALL PracticalAnalyzeError(num, AXISSTATUS(num))
    ENDIF
    
    gv_Status = 0                        '状态切换成待机
END SUB

'================ 多轴顺序Z信号回零处理 ================
GLOBAL SUB practical_home_seq_mode(home_mode)
    IF gv_Status <> 1 THEN
        CALL practical_task_seq_mode(home_mode)
    ELSE
        PRINT "系统忙，无法回零"
    ENDIF
END SUB



GLOBAL SUB practical_task_sequential()
    PRINT "=== 多轴顺序回零演示 ==="
    
    gv_Status = 1                        '状态切换成回零中
    
    '设置所有轴状态为回零中
    FOR i = 0 TO gv_AxisMax - 1
        IF ga_HomeEnable(i) = 0 THEN
            ga_StatusAxis(i) = 1
        ENDIF
    NEXT
    
    '按轴号顺序回零（可根据需要调整顺序）
    FOR i = 0 TO gv_AxisMax - 1
        IF ga_HomeEnable(i) = 0 THEN
            BASE(i)
            PRINT "开始轴", i, "回零..."
            
            '根据回零模式执行（支持Z信号回零）
            IF ga_HomeMode(i) = 2 THEN
                DATUM(2)                 'Z信号反向回零
            ELSEIF ga_HomeMode(i) = 6 THEN
                DATUM(6)                 '原点+Z信号负向回零
            ELSEIF ga_HomeMode(i) = 3 THEN
                DATUM(3)
            ELSEIF ga_HomeMode(i) = 4 THEN
                DATUM(4)
            ELSEIF ga_HomeMode(i) = 13 THEN
                DATUM(13)
            ELSEIF ga_HomeMode(i) = 14 THEN
                DATUM(14)
            ELSE
                DATUM(2)                 '默认使用Z信号反向回零
            ENDIF
            
            '等待当前轴回零完成
            WAIT UNTIL IDLE(i) = -1
            DELAY(10)
            
            '检查回零结果
            IF AXISSTATUS(i) = 0 THEN
                ga_StatusAxis(i) = 2
                DPOS(i) = 0
                MPOS(i) = 0
                PRINT "轴", i, "回零成功"
            ELSE
                ga_StatusAxis(i) = 3
                PRINT "轴", i, "回零失败，状态：", HEX(AXISSTATUS(i))
                '如果有轴失败，停止后续回零
                gv_Status = 0
                PRINT "回零过程中断，轴", i, "失败"
                RETURN
            ENDIF
            
            DELAY(200)  '轴间延时
        ENDIF
    NEXT
    
    gv_Status = 2                        '状态切换成已完成
    PRINT "多轴顺序回零完成！"
END SUB

'================ 多轴同时Z信号回零处理 ================
GLOBAL SUB practical_home_sim_mode(home_mode)
    IF gv_Status <> 1 THEN
        CALL practical_task_sim_mode(home_mode)
    ELSE
        PRINT "系统忙，无法回零"
    ENDIF
END SUB



GLOBAL SUB practical_task_simul()
    PRINT "=== 多轴同时回零演示 ==="
    
    gv_Status = 1                        '状态切换成回零中
    
    '同时启动所有轴回零
    FOR i = 0 TO gv_AxisMax - 1
        IF ga_HomeEnable(i) = 0 THEN
            ga_StatusAxis(i) = 1
            BASE(i)
            
            '根据模式执行回零（支持Z信号回零）
            IF ga_HomeMode(i) = 2 THEN
                DATUM(2)                 'Z信号反向回零
            ELSEIF ga_HomeMode(i) = 6 THEN
                DATUM(6)                 '原点+Z信号负向回零
            ELSEIF ga_HomeMode(i) = 3 THEN
                DATUM(3)
            ELSEIF ga_HomeMode(i) = 4 THEN
                DATUM(4)
            ELSEIF ga_HomeMode(i) = 13 THEN
                DATUM(13)
            ELSEIF ga_HomeMode(i) = 14 THEN
                DATUM(14)
            ELSE
                DATUM(2)                 '默认使用Z信号反向回零
            ENDIF
            
            PRINT "轴", i, "开始回零..."
        ENDIF
    NEXT
    
    '等待所有轴回零完成
    WAIT UNTIL IDLE(0) = -1 AND IDLE(1) = -1 AND IDLE(2) = -1
    DELAY(10)
    
    '检查所有轴的回零结果
    DIM home_success
    home_success = 1
    
    FOR i = 0 TO gv_AxisMax - 1
        IF ga_HomeEnable(i) = 0 THEN
            IF AXISSTATUS(i) = 0 THEN
                ga_StatusAxis(i) = 2
                DPOS(i) = 0
                MPOS(i) = 0
                PRINT "轴", i, "回零成功，位置：", DPOS(i)
            ELSE
                ga_StatusAxis(i) = 3
                PRINT "轴", i, "回零失败，状态：", HEX(AXISSTATUS(i))
                home_success = 0
            ENDIF
        ENDIF
    NEXT
    
    IF home_success = 1 THEN
        gv_Status = 2                    '所有轴回零成功
        PRINT "所有轴同时回零完成！"
    ELSE
        gv_Status = 0                    '有轴回零失败
        PRINT "部分轴回零失败！"
    ENDIF
END SUB

'================ 停止按钮处理 ================
GLOBAL SUB practical_stop()
    STOPTASK 2
    RAPIDSTOP(2)
    gv_Status = 0

    FOR j = 0 TO gv_AxisMax - 1
        IF ga_StatusAxis(j) = 1 THEN
            ga_StatusAxis(j) = 0
        ENDIF
    NEXT

    PRINT "紧急停止！所有轴停止运动"
END SUB

'================ 系统检查 ================
GLOBAL SUB PracticalSystemCheck()
    PRINT "执行系统检查..."

    '检查急停状态
    IF IN(7) = ON THEN
        PRINT "错误：急停信号有效，无法回零"
        RETURN 0
    ENDIF

    '检查驱动器使能
    FOR i = 0 TO gv_AxisMax - 1
        IF AXIS_ENABLE(i) = 0 THEN
            PRINT "警告：轴", i, "未使能"
        ENDIF
    NEXT

    PRINT "系统检查通过"
    RETURN 1
END SUB

'================ 回零状态检查 ================
GLOBAL SUB PracticalCheckStatus()
    PRINT "=== 回零状态检查 ==="

    FOR i = 0 TO gv_AxisMax - 1
        PRINT "轴", i, "状态检查："
        PRINT "  位置：", DPOS(i)
        PRINT "  编码器位置：", MPOS(i)
        PRINT "  轴状态：", HEX(AXISSTATUS(i))

        IF IDLE(i) = -1 THEN
            PRINT "  运动状态：停止"
        ELSE
            PRINT "  运动状态：运动中"
        ENDIF

        PRINT "  回零状态：", ga_StatusAxis(i), "(0-未回零,1-回零中,2-已回零,3-失败)"
        PRINT "  回零模式：", ga_HomeMode(i), "(2-Z信号反向,6-原点+Z信号负向)"

        '检查ATYPE设置（Z信号回零要求）
        PRINT "  ATYPE：", ATYPE(i)
        IF ATYPE(i) = 4 OR ATYPE(i) = 7 THEN
            PRINT "  ✓ ATYPE设置正确，支持Z信号回零"
        ELSE
            PRINT "  ✗ ATYPE设置错误，无法进行Z信号回零"
        ENDIF

        '检查是否在原点附近
        IF ABS(DPOS(i)) < 1 THEN
            PRINT "  原点状态：在原点"
        ELSE
            PRINT "  原点状态：偏离原点", DPOS(i), "单位"
        ENDIF

        '检查原点输入信号（模式6需要）
        DIM home_input
        home_input = IN(8 + i)
        IF home_input = ON THEN
            PRINT "  原点信号：有效"
        ELSE
            PRINT "  原点信号：无效"
        ENDIF

        '检查编码器值（Z信号回零相关）
        PRINT "  编码器值：", ENCODER(i)

        PRINT ""
    NEXT

    '显示系统状态
    PRINT "系统状态：", gv_Status, "(0-停止,1-运行中,2-已完成)"
    IF gv_Status = 0 THEN
        PRINT "状态说明：待机"
    ELSEIF gv_Status = 1 THEN
        PRINT "状态说明：回零中"
    ELSEIF gv_Status = 2 THEN
        PRINT "状态说明：回零完成"
    ENDIF
END SUB

'================ 信号测试 ================
GLOBAL SUB PracticalTestSignals()
    PRINT "=== 原点信号测试 ==="
    PRINT "测试时间：10秒，请手动触发各轴原点开关"

    FOR test_time = 1 TO 100  '10秒测试
        PRINT "时间:", test_time * 0.1, "s"

        FOR i = 0 TO gv_AxisMax - 1
            DIM home_signal, fwd_signal, rev_signal
            home_signal = IN(8 + i)
            fwd_signal = IN(11 + i)
            rev_signal = IN(14 + i)

            PRINT "轴", i, ": 原点=", home_signal, " 正限位=", fwd_signal, " 负限位=", rev_signal
        NEXT

        PRINT "---"
        DELAY(100)
    NEXT

    PRINT "信号测试完成"
END SUB

'================ 轴错误分析子程序 ================
GLOBAL SUB PracticalAnalyzeError(axis_num, status_value)
    DIM cur_axis, cur_status
    cur_axis = axis_num
    cur_status = status_value

    PRINT "轴", cur_axis, "错误分析："

    IF cur_status AND 1 THEN
        PRINT "  - 轴错误标志置位"
    ENDIF

    IF cur_status AND 2 THEN
        PRINT "  - 碰到正向硬限位"
    ENDIF

    IF cur_status AND 4 THEN
        PRINT "  - 碰到负向硬限位"
    ENDIF

    IF cur_status AND 8 THEN
        PRINT "  - 急停信号有效"
    ENDIF

    IF cur_status AND 16 THEN
        PRINT "  - 驱动器报警"
    ENDIF

    '检查原点信号状态
    DIM home_signal
    home_signal = IN(8 + cur_axis)
    IF home_signal = ON THEN
        PRINT "  - 原点信号状态：有效"
    ELSE
        PRINT "  - 原点信号状态：无效"
    ENDIF

    '建议解决方案
    PRINT "建议检查："
    PRINT "  1. 原点开关连接和信号"
    PRINT "  2. 限位开关状态"
    PRINT "  3. 驱动器使能和报警"
    PRINT "  4. 轴运动方向设置"
    PRINT "  5. Z信号回零需要ATYPE=4或7"
    PRINT "  6. 检查编码器Z信号连接"
END SUB

'================ Z信号回零任务函数 ================

'单轴Z信号回零任务（支持不同模式）
GLOBAL SUB practical_task_axis_mode(num, home_mode)
    DIM cur_axis, mode_val
    cur_axis = num
    mode_val = home_mode

    gv_Status = 1                        '状态切换成回零中
    ga_StatusAxis(cur_axis) = 1

    IF mode_val = 2 THEN
        PRINT "开始轴", cur_axis, "Z信号反向回零（模式2）"
        PRINT "运动过程：CREEP负向→Z信号→停止"
    ELSEIF mode_val = 6 THEN
        PRINT "开始轴", cur_axis, "原点+Z信号负向回零（模式6）"
        PRINT "运动过程：SPEED负向→原点→CREEP正向→离开→CREEP正向→Z信号"
    ENDIF

    '执行回零前检查
    IF PracticalSystemCheck() = 0 THEN
        PRINT "系统检查失败，停止回零"
        gv_Status = 0
        ga_StatusAxis(cur_axis) = 3
        RETURN
    ENDIF

    BASE(cur_axis)

    '检查ATYPE设置（Z信号回零要求）
    IF ATYPE(cur_axis) <> 4 AND ATYPE(cur_axis) <> 7 THEN
        PRINT "错误：Z信号回零必须配置ATYPE=4或7"
        PRINT "当前ATYPE：", ATYPE(cur_axis)
        gv_Status = 0
        ga_StatusAxis(cur_axis) = 3
        RETURN
    ENDIF

    '清除轴错误状态（官方推荐）
    DATUM(0) AXIS(cur_axis)
    DELAY(10)

    '执行Z信号回零指令
    DATUM(mode_val)

    '等待回零完成
    WAIT UNTIL IDLE(cur_axis) = -1
    DELAY(10)  '状态稳定延时

    '检查回零结果
    IF AXISSTATUS(cur_axis) = 0 THEN
        '回零成功 - DATUM指令已自动清零DPOS并纠正MPOS
        ga_StatusAxis(cur_axis) = 2           '设置为已回零
        PRINT "轴", cur_axis, "Z信号回零成功！"
        PRINT "DPOS：", DPOS(cur_axis), "（已自动清零）"
        PRINT "MPOS：", MPOS(cur_axis), "（已自动纠正）"

        IF mode_val = 2 THEN
            PRINT "精度：±1个编码器脉冲"
        ELSEIF mode_val = 6 THEN
            PRINT "精度：±0.25个编码器脉冲（最高精度）"
        ENDIF
    ELSE
        '回零失败处理
        ga_StatusAxis(cur_axis) = 3           '设置为回零失败
        PRINT "轴", cur_axis, "Z信号回零失败！状态：", HEX(AXISSTATUS(cur_axis))
        CALL PracticalAnalyzeError(cur_axis, AXISSTATUS(cur_axis))
    ENDIF

    gv_Status = 0                        '状态切换成待机
END SUB

'多轴顺序Z信号回零任务（支持不同模式）
GLOBAL SUB practical_task_seq_mode(home_mode)
    DIM mode_val, cur_axis
    mode_val = home_mode

    gv_Status = 1                        '状态切换成回零中

    IF mode_val = 2 THEN
        PRINT "开始多轴顺序Z信号反向回零（模式2）"
    ELSEIF mode_val = 6 THEN
        PRINT "开始多轴顺序原点+Z信号负向回零（模式6）"
    ENDIF

    '执行回零前检查
    IF PracticalSystemCheck() = 0 THEN
        PRINT "系统检查失败，停止回零"
        gv_Status = 0
        RETURN
    ENDIF

    '按顺序回零每个轴
    FOR i = 0 TO gv_AxisMax - 1
        cur_axis = ga_AxisNum(i)

        PRINT "--- 开始轴", cur_axis, "Z信号回零 ---"
        ga_StatusAxis(i) = 1             '设置为回零中

        BASE(cur_axis)

        '检查ATYPE设置
        IF ATYPE(cur_axis) <> 4 AND ATYPE(cur_axis) <> 7 THEN
            PRINT "错误：轴", cur_axis, "ATYPE设置不正确，当前：", ATYPE(cur_axis)
            ga_StatusAxis(i) = 3
            gv_Status = 0
            RETURN
        ENDIF

        '清除轴错误状态
        DATUM(0) AXIS(cur_axis)
        DELAY(10)

        '执行Z信号回零
        DATUM(mode_val)

        '等待回零完成
        WAIT UNTIL IDLE(cur_axis) = -1
        DELAY(10)

        '检查回零结果
        IF AXISSTATUS(cur_axis) = 0 THEN
            ga_StatusAxis(i) = 2         '设置为已回零
            PRINT "轴", cur_axis, "Z信号回零成功"
        ELSE
            ga_StatusAxis(i) = 3         '设置为回零失败
            PRINT "轴", cur_axis, "Z信号回零失败，停止多轴回零"
            CALL PracticalAnalyzeError(cur_axis, AXISSTATUS(cur_axis))
            gv_Status = 0
            RETURN
        ENDIF

        DELAY(500)  '轴间延时
    NEXT

    PRINT "所有轴Z信号顺序回零完成"
    gv_Status = 0                        '状态切换成待机
END SUB

'多轴同时Z信号回零任务（支持不同模式）
GLOBAL SUB practical_task_sim_mode(home_mode)
    DIM mode_val, check_axis, result_axis, all_success
    mode_val = home_mode

    gv_Status = 1                        '状态切换成回零中

    IF mode_val = 2 THEN
        PRINT "开始多轴同时Z信号反向回零（模式2）"
    ELSEIF mode_val = 6 THEN
        PRINT "开始多轴同时原点+Z信号负向回零（模式6）"
    ENDIF

    '执行回零前检查
    IF PracticalSystemCheck() = 0 THEN
        PRINT "系统检查失败，停止回零"
        gv_Status = 0
        RETURN
    ENDIF

    '检查所有轴的ATYPE设置
    FOR i = 0 TO gv_AxisMax - 1
        check_axis = ga_AxisNum(i)
        BASE(check_axis)

        IF ATYPE(check_axis) <> 4 AND ATYPE(check_axis) <> 7 THEN
            PRINT "错误：轴", check_axis, "ATYPE设置不正确，当前：", ATYPE(check_axis)
            gv_Status = 0
            RETURN
        ENDIF
    NEXT

    '设置所有轴状态为回零中
    FOR i = 0 TO gv_AxisMax - 1
        ga_StatusAxis(i) = 1
    NEXT

    '清除所有轴错误状态
    FOR i = 0 TO gv_AxisMax - 1
        DATUM(0) AXIS(ga_AxisNum(i))
    NEXT
    DELAY(10)

    '同时启动所有轴Z信号回零
    FOR i = 0 TO gv_AxisMax - 1
        BASE(ga_AxisNum(i))
        PRINT "启动轴", ga_AxisNum(i), "Z信号回零（模式", mode_val, "）"
        DATUM(mode_val)
    NEXT

    PRINT "等待所有轴Z信号回零完成..."

    '等待所有轴回零完成
    WAIT UNTIL IDLE(ga_AxisNum(0)) = -1 AND IDLE(ga_AxisNum(1)) = -1 AND IDLE(ga_AxisNum(2)) = -1
    DELAY(10)  '状态稳定延时

    '检查所有轴的回零结果
    all_success = 1

    FOR i = 0 TO gv_AxisMax - 1
        result_axis = ga_AxisNum(i)
        BASE(result_axis)

        IF AXISSTATUS(result_axis) = 0 THEN
            ga_StatusAxis(i) = 2         '设置为已回零
            PRINT "轴", result_axis, "Z信号同时回零成功"
        ELSE
            ga_StatusAxis(i) = 3         '设置为回零失败
            PRINT "轴", result_axis, "Z信号同时回零失败"
            CALL PracticalAnalyzeError(result_axis, AXISSTATUS(result_axis))
            all_success = 0
        ENDIF
    NEXT

    '显示最终结果
    IF all_success = 1 THEN
        PRINT "所有轴Z信号同时回零成功！"
        IF mode_val = 2 THEN
            PRINT "精度：±1个编码器脉冲"
        ELSEIF mode_val = 6 THEN
            PRINT "精度：±0.25个编码器脉冲（最高精度）"
        ENDIF
    ELSE
        PRINT "部分轴Z信号同时回零失败！"
    ENDIF

    gv_Status = 0                        '状态切换成待机
END SUB