# 不带Z信号回零详细教程

## 📋 目录
1. [AB信号回零基础概念](#AB信号回零基础概念)
2. [硬件连接要求](#硬件连接要求)
3. [回零模式详解](#回零模式详解)
4. [参数配置说明](#参数配置说明)
5. [编程实现步骤](#编程实现步骤)
6. [完整示例代码](#完整示例代码)
7. [故障排除指南](#故障排除指南)
8. [最佳实践建议](#最佳实践建议)

---

## 🎯 AB信号回零基础概念

### 什么是AB信号回零？
AB信号回零是指使用编码器的A、B相信号（不使用Z信号）进行原点回归的方法。这种方式适用于：
- 编码器没有Z信号输出
- Z信号不稳定或损坏
- 对回零精度要求不是极高的应用
- 成本敏感的应用场合

### AB信号与Z信号的区别

| 信号类型 | 特点 | 精度 | 适用场景 |
|----------|------|------|----------|
| **AB信号** | 连续方波信号 | 中等精度 | 一般工业应用 |
| **Z信号** | 每转一个脉冲 | 高精度 | 高精度定位应用 |
| **AB+Z信号** | AB连续+Z索引 | 最高精度 | 精密加工应用 |

### 回零原理
1. **快速寻找**：轴以SPEED速度向原点开关方向运动
2. **碰到原点开关**：检测到原点信号后开始减速
3. **精确定位**：以CREEP速度进行精确定位
4. **离开原点**：反向运动直到离开原点开关
5. **位置清零**：将当前位置设为坐标原点（DPOS=0，MPOS=0）

---

## 🔌 硬件连接要求

### 必需的硬件信号

#### 1. 编码器AB信号
- **A相信号**：连接到控制器编码器输入的A相端子
- **B相信号**：连接到控制器编码器输入的B相端子
- **电源**：编码器供电（通常5V或24V）
- **屏蔽**：使用屏蔽电缆，屏蔽层接地

#### 2. 原点开关
- **作用**：指示原点位置的传感器
- **连接**：通过 `DATUM_IN` 指令映射到输入口
- **类型**：接近开关、光电开关或微动开关

#### 3. 限位开关（可选但推荐）
- **正向限位**：通过 `FWD_IN` 指令映射
- **负向限位**：通过 `REV_IN` 指令映射

### 典型硬件连接图
```
控制器端子        |    设备连接        |    说明
Axis0_A+/A-      |    编码器A相       |    差分信号连接
Axis0_B+/B-      |    编码器B相       |    差分信号连接
Axis0_5V/GND     |    编码器电源      |    5V供电
IN8              |    原点开关        |    DATUM_IN = 8
IN11             |    正限位开关      |    FWD_IN = 11
IN12             |    负限位开关      |    REV_IN = 12
```

---

## 📊 回零模式详解

### 适用于AB信号的回零模式

| 模式 | 描述 | 运动方向 | 适用场景 |
|------|------|----------|----------|
| **模式3** | 原点开关回零 | 正向 | **最常用**，适合大多数AB信号应用 |
| **模式4** | 原点开关回零 | 负向 | 原点在负方向的应用 |
| **模式8** | 简单原点回零 | 正向 | 简单应用，精度要求不高 |
| **模式9** | 简单原点回零 | 负向 | 简单应用，精度要求不高 |
| **模式13** | 限位反找+原点 | 正向 | 原点在限位中间 |
| **模式14** | 限位反找+原点 | 负向 | 原点在限位中间 |

### 模式3详细说明（推荐用于AB信号）
```
运动过程：
1. 轴以SPEED速度正向运动
2. 碰到原点开关后开始减速停止
3. 以CREEP速度反向运动
4. 离开原点开关后立即停止
5. DPOS和MPOS位置清零，回零完成
```

**注意**：与带Z信号的模式5、6不同，模式3、4不会寻找Z信号，直接以离开原点开关的位置作为原点。

---

## ⚙️ 参数配置说明

### 轴类型设置（关键）
```basic
' AB信号回零的轴类型设置
BASE(0)
ATYPE = 4                    ' 脉冲方向输出+正交编码器输入
' 或者
ATYPE = 5                    ' 脉冲方向输出+脉冲方向编码器输入
```

### 基本轴参数
```basic
BASE(0)                      ' 选择轴号
UNITS = 1000                 ' 脉冲当量：每单位脉冲数
SPEED = 200                  ' 回零速度（units/s）
CREEP = 20                   ' 爬行速度（units/s）
ACCEL = 1000                 ' 加速度（units/s²）
DECEL = 1000                 ' 减速度（units/s²）
HOMEWAIT = 20                ' 反找等待时间（ms）
```

### 信号映射配置
```basic
' 原点信号映射
DATUM_IN = 8                 ' 原点开关连接到IN8
INVERT_IN(8, ON)             ' 信号反转（ZMC系列OFF有效）

' 限位信号映射
FWD_IN = 11                  ' 正向限位连接到IN11
REV_IN = 12                  ' 负向限位连接到IN12
INVERT_IN(11, ON)            ' 限位信号反转
INVERT_IN(12, ON)
```

### 编码器参数设置
```basic
' 编码器齿轮比设置（如果需要）
ENCODER_RATIO(1, 1)          ' 1:1比例，根据实际情况调整

' 编码器滤波设置（可选）
ENCODER_FILTER = 1           ' 不滤波，如需滤波可设置0.5等
```

---

## 💻 编程实现步骤

### 步骤1：系统初始化
```basic
GLOBAL SUB InitABHomeSystem()
    PRINT "初始化AB信号回零系统..."
    
    ' 轴参数设置
    BASE(0)
    ATYPE = 4                    ' 脉冲+正交编码器
    UNITS = 1000                 ' 脉冲当量
    SPEED = 200                  ' 回零速度
    CREEP = 20                   ' 爬行速度
    ACCEL = 1000                 ' 加速度
    DECEL = 1000                 ' 减速度
    HOMEWAIT = 20                ' 反找等待时间
    
    ' 信号映射
    DATUM_IN = 8                 ' 原点开关
    FWD_IN = 11                  ' 正限位
    REV_IN = 12                  ' 负限位
    
    ' 信号反转（ZMC系列）
    INVERT_IN(8, ON)
    INVERT_IN(11, ON)
    INVERT_IN(12, ON)
    
    ' 编码器设置
    ENCODER_RATIO(1, 1)          ' 编码器比例
    
    PRINT "AB信号回零系统初始化完成"
END SUB
```

### 步骤2：执行AB信号回零
```basic
GLOBAL SUB ExecuteABHoming(axis_num)
    PRINT "开始轴", axis_num, "AB信号回零..."
    
    BASE(axis_num)
    
    ' 清除轴状态
    AXIS_STOPREASON = 0
    
    ' 记录回零前的编码器位置
    DIM encoder_before
    encoder_before = MPOS(axis_num)
    PRINT "回零前编码器位置：", encoder_before
    
    ' 执行回零指令（使用模式3，适合AB信号）
    DATUM(3)
    
    ' 等待回零完成
    WAIT UNTIL IDLE(axis_num) = -1
    DELAY(10)                    ' 状态稳定延时
    
    ' 检查回零结果
    IF AXISSTATUS(axis_num) = 0 THEN
        ' 回零成功处理
        DPOS(axis_num) = 0       ' 清零脉冲位置
        MPOS(axis_num) = 0       ' 清零编码器位置
        PRINT "轴", axis_num, "AB信号回零成功！"
        PRINT "回零后位置 - DPOS：", DPOS(axis_num), " MPOS：", MPOS(axis_num)
        RETURN 1                 ' 返回成功标志
    ELSE
        ' 回零失败处理
        PRINT "轴", axis_num, "AB信号回零失败！"
        PRINT "错误状态：", HEX(AXISSTATUS(axis_num))
        CALL AnalyzeABHomeError(axis_num)
        RETURN 0                 ' 返回失败标志
    ENDIF
END SUB
```

### 步骤3：AB信号回零验证
```basic
GLOBAL SUB VerifyABHoming(axis_num)
    PRINT "=== AB信号回零验证 ==="
    
    BASE(axis_num)
    
    ' 检查1：轴状态正常
    IF AXISSTATUS(axis_num) = 0 THEN
        PRINT "✓ 轴状态正常"
    ELSE
        PRINT "✗ 轴状态异常：", HEX(AXISSTATUS(axis_num))
    ENDIF
    
    ' 检查2：位置已清零
    IF ABS(DPOS(axis_num)) < 0.1 AND ABS(MPOS(axis_num)) < 0.1 THEN
        PRINT "✓ 位置已清零 - DPOS：", DPOS(axis_num), " MPOS：", MPOS(axis_num)
    ELSE
        PRINT "⚠ 位置偏差 - DPOS：", DPOS(axis_num), " MPOS：", MPOS(axis_num)
    ENDIF
    
    ' 检查3：轴已停止
    IF IDLE(axis_num) = -1 THEN
        PRINT "✓ 轴已停止"
    ELSE
        PRINT "✗ 轴仍在运动"
    ENDIF
    
    ' 检查4：编码器信号状态
    DIM encoder_status
    encoder_status = ENCODER_STATUS(axis_num)
    PRINT "编码器AB信号状态：", HEX(encoder_status)
    
    ' 检查5：原点信号状态
    DIM home_signal
    home_signal = IN(DATUM_IN(axis_num))
    IF home_signal = OFF THEN
        PRINT "✓ 已离开原点信号"
    ELSE
        PRINT "ℹ 仍在原点信号上"
    ENDIF
END SUB
```

---

## 📝 完整示例代码

### AB信号回零完整程序
```basic
'=============================================================================
' AB编码器回零程序
' 适用于不带Z信号的编码器回零
'=============================================================================

' 全局变量
GLOBAL total_axis            ' 轴数量
GLOBAL encoder_home_status(3) ' 各轴回零状态

' 主程序
total_axis = 3
CALL InitEncoderHome()

PRINT "=== AB编码器回零程序 ==="
PRINT "IN0 - 单轴回零（轴0）"
PRINT "IN1 - 多轴顺序回零"
PRINT "IN2 - 多轴同时回零"
PRINT "IN3 - 状态检查"
PRINT "IN4 - 回零验证"

WHILE 1
    ' 单轴回零（轴0）
    IF SCAN_EVENT(IN(0)) > 0 THEN
        CALL DoSingleHome(0)
    ENDIF

    ' 多轴顺序回零
    IF SCAN_EVENT(IN(1)) > 0 THEN
        CALL DoSequentialHome()
    ENDIF

    ' 多轴同时回零
    IF SCAN_EVENT(IN(2)) > 0 THEN
        CALL DoSimultaneousHome()
    ENDIF

    ' 状态检查
    IF SCAN_EVENT(IN(3)) > 0 THEN
        CALL ShowHomeStatus()
    ENDIF

    ' 回零验证
    IF SCAN_EVENT(IN(4)) > 0 THEN
        CALL VerifyAllHome()
    ENDIF
    
    DELAY(50)
WEND
END

' 编码器回零系统初始化
GLOBAL SUB InitEncoderHome()
    FOR i = 0 TO total_axis - 1
        BASE(i)
        ATYPE = 4                ' 脉冲+正交编码器
        UNITS = 1000             ' 脉冲当量
        SPEED = 200              ' 回零速度
        CREEP = 20               ' 爬行速度
        ACCEL = 1000             ' 加速度
        DECEL = 1000             ' 减速度
        HOMEWAIT = 20            ' 反找等待时间
        
        ' 信号映射
        DATUM_IN = 8 + i         ' 原点开关
        FWD_IN = 11 + i          ' 正限位
        REV_IN = 14 + i          ' 负限位
        
        ' 信号反转
        INVERT_IN(8 + i, ON)
        INVERT_IN(11 + i, ON)
        INVERT_IN(14 + i, ON)
        
        ' 编码器设置
        ENCODER_RATIO(1, 1)      ' 1:1比例
        
        encoder_home_status(i) = 0    ' 初始化为未回零
    NEXT
    
    PRINT "AB编码器回零系统初始化完成"
    PRINT "ATYPE=4: 脉冲+正交编码器"
END SUB

' 单轴回零
GLOBAL SUB DoSingleHome(axis_num)
    PRINT "开始轴", axis_num, "编码器回零..."
    
    BASE(axis_num)
    encoder_home_status(axis_num) = 1  ' 设置为回零中
    
    ' 记录回零前状态
    PRINT "回零前状态："
    PRINT "  DPOS：", DPOS(axis_num)
    PRINT "  MPOS：", MPOS(axis_num)
    PRINT "  原点信号：", IN(8 + axis_num)
    
    DIM enc_stat
    enc_stat = ENCODER_STATUS(axis_num)
    PRINT "  编码器状态：", HEX(enc_stat)
    
    ' 执行AB编码器回零（使用模式3）
    PRINT "执行DATUM(3)回零..."
    DATUM(3)
    WAIT UNTIL IDLE(axis_num) = -1
    DELAY(10)
    
    ' 检查结果
    IF AXISSTATUS(axis_num) = 0 THEN
        DPOS(axis_num) = 0
        MPOS(axis_num) = 0
        encoder_home_status(axis_num) = 2  ' 设置为已回零
        PRINT "✓ 轴", axis_num, "编码器回零成功！"
        PRINT "回零后状态："
        PRINT "  DPOS：", DPOS(axis_num)
        PRINT "  MPOS：", MPOS(axis_num)
    ELSE
        encoder_home_status(axis_num) = 3  ' 设置为回零失败
        PRINT "✗ 轴", axis_num, "编码器回零失败！"
        PRINT "错误状态：", HEX(AXISSTATUS(axis_num))
        CALL AnalyzeHomeError(axis_num)
    ENDIF
END SUB

' 多轴顺序回零
GLOBAL SUB DoSequentialHome()
    PRINT "=== 开始多轴顺序回零 ==="
    PRINT "按轴号顺序依次回零..."

    FOR i = 0 TO total_axis - 1
        PRINT "--- 开始轴", i, "回零 ---"
        CALL DoSingleHome(i)
        IF encoder_home_status(i) <> 2 THEN
            PRINT "✗ 多轴顺序回零失败，停止在轴", i
            RETURN
        ENDIF
        DELAY(500)  ' 轴间延时，避免冲击
    NEXT

    PRINT "✓ 所有轴顺序回零成功！"
END SUB

' 多轴同时回零
GLOBAL SUB DoSimultaneousHome()
    PRINT "=== 开始多轴同时回零 ==="
    PRINT "所有轴同时启动回零..."

    ' 设置所有轴状态为回零中
    FOR i = 0 TO total_axis - 1
        encoder_home_status(i) = 1
    NEXT

    ' 同时启动所有轴回零
    FOR i = 0 TO total_axis - 1
        BASE(i)
        PRINT "启动轴", i, "回零..."

        ' 显示回零前状态
        PRINT "  回零前 DPOS:", DPOS(i), " MPOS:", MPOS(i)

        ' 执行回零指令
        DATUM(3)
    NEXT

    PRINT "等待所有轴回零完成..."

    ' 等待所有轴回零完成
    WAIT UNTIL IDLE(0) = -1 AND IDLE(1) = -1 AND IDLE(2) = -1
    DELAY(10)  ' 状态稳定延时

    ' 检查所有轴的回零结果
    DIM all_success
    all_success = 1

    FOR i = 0 TO total_axis - 1
        BASE(i)
        IF AXISSTATUS(i) = 0 THEN
            ' 回零成功
            DPOS(i) = 0
            MPOS(i) = 0
            encoder_home_status(i) = 2
            PRINT "✓ 轴", i, "同时回零成功，位置：", DPOS(i)
        ELSE
            ' 回零失败
            encoder_home_status(i) = 3
            PRINT "✗ 轴", i, "同时回零失败，状态：", HEX(AXISSTATUS(i))
            all_success = 0
        ENDIF
    NEXT

    ' 显示最终结果
    IF all_success = 1 THEN
        PRINT "✓ 所有轴同时回零成功！"
    ELSE
        PRINT "✗ 部分轴同时回零失败！"
        PRINT "请检查失败轴的状态"
    ENDIF
END SUB

' 回零状态检查
GLOBAL SUB ShowHomeStatus()
    PRINT "=== 编码器回零状态检查 ==="
    
    FOR i = 0 TO total_axis - 1
        PRINT "轴", i, "详细状态："
        PRINT "  回零状态：", encoder_home_status(i), "(0-未回零,1-回零中,2-已回零,3-失败)"
        PRINT "  ATYPE：", ATYPE(i)
        PRINT "  DPOS位置：", DPOS(i)
        PRINT "  MPOS位置：", MPOS(i)
        PRINT "  轴状态：", HEX(AXISSTATUS(i))
        PRINT "  编码器值：", ENCODER(i)
        
        DIM enc_stat
        enc_stat = ENCODER_STATUS(i)
        PRINT "  编码器状态：", HEX(enc_stat)
        PRINT "  原点信号：", IN(8 + i)
        
        ' 运动状态
        IF IDLE(i) = -1 THEN
            PRINT "  运动状态：停止"
        ELSE
            PRINT "  运动状态：运动中"
        ENDIF
        
        PRINT ""
    NEXT
END SUB

' 验证所有轴回零
GLOBAL SUB VerifyAllHome()
    PRINT "=== 验证所有轴回零结果 ==="
    
    FOR i = 0 TO total_axis - 1
        CALL VerifyHome(i)
        PRINT ""
    NEXT
END SUB

' 验证单轴回零
GLOBAL SUB VerifyHome(axis_num)
    PRINT "验证轴", axis_num, "回零结果："
    
    BASE(axis_num)
    
    ' 检查1：轴状态正常
    IF AXISSTATUS(axis_num) = 0 THEN
        PRINT "✓ 轴状态正常"
    ELSE
        PRINT "✗ 轴状态异常：", HEX(AXISSTATUS(axis_num))
    ENDIF
    
    ' 检查2：位置已清零
    IF ABS(DPOS(axis_num)) < 0.1 AND ABS(MPOS(axis_num)) < 0.1 THEN
        PRINT "✓ 位置已清零"
        PRINT "  DPOS：", DPOS(axis_num)
        PRINT "  MPOS：", MPOS(axis_num)
    ELSE
        PRINT "⚠ 位置偏差"
        PRINT "  DPOS：", DPOS(axis_num)
        PRINT "  MPOS：", MPOS(axis_num)
    ENDIF
    
    ' 检查3：轴已停止
    IF IDLE(axis_num) = -1 THEN
        PRINT "✓ 轴已停止"
    ELSE
        PRINT "✗ 轴仍在运动"
    ENDIF
    
    ' 检查4：编码器信号
    DIM enc_stat
    enc_stat = ENCODER_STATUS(axis_num)
    PRINT "编码器状态：", HEX(enc_stat)
    
    ' 检查5：原点信号
    DIM home_sig
    home_sig = IN(8 + axis_num)
    IF home_sig = OFF THEN
        PRINT "✓ 已离开原点信号"
    ELSE
        PRINT "ℹ 仍在原点信号上"
    ENDIF
END SUB

' 回零错误分析
GLOBAL SUB AnalyzeHomeError(axis_num)
    DIM status_val
    status_val = AXISSTATUS(axis_num)
    
    PRINT "轴", axis_num, "回零错误分析："
    
    IF status_val AND 1 THEN
        PRINT "  ✗ 轴错误标志置位"
    ENDIF
    
    IF status_val AND 2 THEN
        PRINT "  ✗ 碰到正向硬限位"
    ENDIF
    
    IF status_val AND 4 THEN
        PRINT "  ✗ 碰到负向硬限位"
    ENDIF
    
    IF status_val AND 8 THEN
        PRINT "  ✗ 急停信号有效"
    ENDIF
    
    IF status_val AND 16 THEN
        PRINT "  ✗ 驱动器报警"
    ENDIF
    
    ' 编码器信号分析
    DIM enc_stat
    enc_stat = ENCODER_STATUS(axis_num)
    PRINT "编码器诊断："
    PRINT "  ATYPE设置：", ATYPE(axis_num)
    PRINT "  编码器状态：", HEX(enc_stat)
    PRINT "  编码器值：", ENCODER(axis_num)
    PRINT "  MPOS位置：", MPOS(axis_num)
    
    ' 原点信号分析
    DIM home_sig
    home_sig = IN(8 + axis_num)
    PRINT "  原点信号：", home_sig
    
    PRINT "故障排除建议："
    PRINT "  1. 检查编码器AB信号连接"
    PRINT "  2. 检查编码器供电（5V/24V）"
    PRINT "  3. 检查原点开关连接"
    PRINT "  4. 确认ATYPE=4（正交编码器）"
    PRINT "  5. 检查信号屏蔽和接地"
END SUB

' 编码器信号测试
GLOBAL SUB TestEncoderSignal(axis_num)
    PRINT "=== 轴", axis_num, "编码器信号测试 ==="

    BASE(axis_num)

    PRINT "请手动转动编码器，观察10秒..."
    FOR i = 1 TO 100
        DIM enc_val, mpos_val, enc_stat
        enc_val = ENCODER(axis_num)
        mpos_val = MPOS(axis_num)
        enc_stat = ENCODER_STATUS(axis_num)

        PRINT "ENCODER:", enc_val, " MPOS:", mpos_val, " STATUS:", HEX(enc_stat)
        DELAY(100)
    NEXT

    PRINT "编码器信号测试完成"
END SUB

' 重复性测试
GLOBAL SUB RepeatabilityTest(axis_num, test_count)
    PRINT "=== 轴", axis_num, "回零重复性测试 ==="
    PRINT "测试次数：", test_count

    DIM positions(10)
    DIM max_deviation, min_pos, max_pos

    FOR i = 1 TO test_count
        PRINT "第", i, "次回零测试"

        ' 先移动到测试位置
        BASE(axis_num)
        MOVE(100 + i * 20)
        WAIT IDLE
        PRINT "移动到位置：", DPOS(axis_num)

        ' 执行回零
        DATUM(3)
        WAIT UNTIL IDLE(axis_num) = -1
        DELAY(10)

        ' 检查回零结果
        IF AXISSTATUS(axis_num) = 0 THEN
            DPOS(axis_num) = 0
            MPOS(axis_num) = 0
            positions(i) = MPOS(axis_num)
            PRINT "回零位置：", positions(i)
        ELSE
            PRINT "第", i, "次回零失败"
            RETURN
        ENDIF

        DELAY(1000)
    NEXT

    ' 计算重复性
    min_pos = positions(1)
    max_pos = positions(1)

    FOR i = 2 TO test_count
        IF positions(i) < min_pos THEN min_pos = positions(i)
        IF positions(i) > max_pos THEN max_pos = positions(i)
    NEXT

    max_deviation = max_pos - min_pos

    PRINT "=== 重复性测试结果 ==="
    PRINT "最大偏差：", max_deviation
    PRINT "最小位置：", min_pos
    PRINT "最大位置：", max_pos

    IF max_deviation < 2 THEN
        PRINT "✓ 回零重复性优秀（偏差<2脉冲）"
    ELSEIF max_deviation < 5 THEN
        PRINT "⚠ 回零重复性良好（偏差<5脉冲）"
    ELSE
        PRINT "✗ 回零重复性需要改善（偏差≥5脉冲）"
        PRINT "建议："
        PRINT "  1. 降低CREEP爬行速度"
        PRINT "  2. 增加HOMEWAIT等待时间"
        PRINT "  3. 检查编码器信号质量"
    ENDIF
END SUB
```

---

## 🔧 故障排除指南

### 常见问题及解决方案

#### 1. 回零失败：编码器信号异常

**问题现象**：
- 回零过程中轴状态异常
- ENCODER_STATUS显示异常值
- MPOS位置不变化或跳变

**调试代码**：
```basic
SUB DiagnoseABSignal(axis_num)
    BASE(axis_num)

    PRINT "编码器AB信号诊断："
    PRINT "ATYPE设置：", ATYPE(axis_num)
    PRINT "编码器状态：", HEX(ENCODER_STATUS(axis_num))
    PRINT "编码器原始值：", ENCODER(axis_num)
    PRINT "MPOS位置：", MPOS(axis_num)

    ' 手动测试编码器
    PRINT "请手动转动编码器，观察信号变化..."
    FOR i = 1 TO 50
        PRINT "ENCODER:", ENCODER(axis_num), " MPOS:", MPOS(axis_num)
        DELAY(200)
    NEXT
END SUB
```

#### 2. ATYPE设置错误

**正确设置**：
```basic
SUB SetCorrectATYPE(axis_num)
    BASE(axis_num)

    ' 根据编码器类型选择正确的ATYPE
    ' 正交编码器(AB相90度相差)
    ATYPE = 4                ' 脉冲方向输出+正交编码器输入

    ' 或脉冲方向编码器(脉冲+方向)
    ' ATYPE = 5              ' 脉冲方向输出+脉冲方向编码器输入

    PRINT "ATYPE设置为：", ATYPE(axis_num)
END SUB
```

#### 3. 编码器方向错误

**解决方案**：
```basic
SUB CorrectEncoderDirection(axis_num)
    BASE(axis_num)

    ' 使用ENCODER_RATIO反转方向
    ENCODER_RATIO(1, -1)         ' 反转编码器方向

    PRINT "编码器方向已反转"
END SUB
```

---

## 🏆 最佳实践建议

### 1. AB信号回零流程设计

```basic
GLOBAL SUB StandardABHomingProcedure(axis_num)
    PRINT "=== 标准AB信号回零流程 ==="

    ' 步骤1：系统检查
    IF ABSystemCheck(axis_num) = 0 THEN
        PRINT "系统检查失败，停止回零"
        RETURN 0
    ENDIF

    ' 步骤2：执行AB回零
    IF ExecuteABHoming(axis_num) = 0 THEN
        PRINT "AB回零执行失败"
        RETURN 0
    ENDIF

    ' 步骤3：结果验证
    CALL VerifyABHoming(axis_num)

    PRINT "标准AB回零流程完成"
    RETURN 1
END SUB
```

### 2. AB信号质量检查

```basic
GLOBAL SUB CheckABSignal(axis_num)
    BASE(axis_num)

    ' 检查ATYPE设置
    IF ATYPE(axis_num) <> 4 AND ATYPE(axis_num) <> 5 THEN
        PRINT "错误：ATYPE设置不正确，当前值：", ATYPE(axis_num)
        RETURN 0
    ENDIF

    ' 检查编码器状态
    DIM encoder_status
    encoder_status = ENCODER_STATUS(axis_num)
    PRINT "编码器AB信号状态：", HEX(encoder_status)

    RETURN 1
END SUB
```

### 3. 重复性测试

```basic
GLOBAL SUB ABHomingRepeatabilityTest(axis_num, test_count)
    PRINT "=== AB回零重复性测试 ==="

    DIM positions(10)
    DIM max_deviation, min_pos, max_pos

    FOR i = 1 TO test_count
        PRINT "第", i, "次回零测试"

        ' 先移动到随机位置
        MOVE(50 + i * 10)
        WAIT IDLE

        ' 执行AB回零
        CALL ExecuteABHoming(axis_num)

        ' 记录回零位置
        positions(i) = MPOS(axis_num)
        PRINT "回零位置：", positions(i)

        DELAY(1000)
    NEXT

    ' 计算重复性
    min_pos = positions(1)
    max_pos = positions(1)

    FOR i = 2 TO test_count
        IF positions(i) < min_pos THEN min_pos = positions(i)
        IF positions(i) > max_pos THEN max_pos = positions(i)
    NEXT

    max_deviation = max_pos - min_pos

    PRINT "=== 重复性测试结果 ==="
    PRINT "最大偏差：", max_deviation

    IF max_deviation < 2 THEN
        PRINT "✓ AB回零重复性良好"
    ELSE
        PRINT "⚠ AB回零重复性需要改善"
    ENDIF
END SUB
```

---

## 📚 总结

### AB信号回零的关键要素

1. **正确的ATYPE设置**：ATYPE=4（正交编码器）或ATYPE=5（脉冲方向编码器）
2. **可靠的AB信号**：确保编码器供电和信号连接正常
3. **合适的回零模式**：推荐使用模式3或4
4. **优化的参数设置**：根据编码器分辨率调整速度参数

### 推荐的开发流程

1. **硬件验证**：确认编码器AB信号正常
2. **ATYPE设置**：根据编码器类型正确设置
3. **参数调试**：从低速开始，逐步优化
4. **单轴验证**：确保单轴AB回零稳定
5. **重复性测试**：验证回零精度和重复性
6. **多轴集成**：实现多轴AB回零功能

### AB信号回零适用场景

- ✅ **一般工业自动化**：精度要求不是极高的应用
- ✅ **成本敏感项目**：使用普通编码器降低成本
- ✅ **恶劣环境**：AB信号比Z信号更可靠
- ✅ **快速回零**：不需要寻找Z信号，回零速度快

### 与Z信号回零的对比

| 特性 | AB信号回零 | Z信号回零 | 建议 |
|------|------------|-----------|------|
| **精度** | 中等（±1个编码器脉冲） | 高（±0.25个编码器脉冲） | 精度要求高时使用Z信号 |
| **成本** | 低（普通编码器） | 高（带Z信号编码器） | 成本敏感时使用AB信号 |
| **可靠性** | 高（信号简单） | 中等（Z信号易受干扰） | 恶劣环境使用AB信号 |
| **速度** | 快（无需寻找Z信号） | 慢（需要寻找Z信号） | 效率优先使用AB信号 |

通过遵循本教程的指导，您可以实现稳定、可靠的AB信号回零功能，满足大多数工业应用的需求。
