===============================================================================
                        SYSTEM_ZSET设置VP_SPEED单轴监控说明 - 精确监控各轴速度
===============================================================================

【SYSTEM_ZSET参数详解】

=== SYSTEM_ZSET的位定义 ===
```
SYSTEM_ZSET -- 控制器设置
bit0：1-VP_SPEED缺省使用插补速度，0-VP_SPEED使用单轴的速度
bit1：1-使用MOVE_OP精确输出功能，0-MOVE_OP普通方式
bit4：1-对带编码器功能的轴，使用编码器位置的MOVE_OP精准方式
bit7：1-总线时钟优化，0-脉冲轴时钟优化

关键设置：
bit0 = 1（默认）：VP_SPEED显示插补合成速度
bit0 = 0（修改）：VP_SPEED显示单轴的命令速度，可正可负
```

=== 螺丝机的优化设置 ===
```
目标：让VP_SPEED显示各轴的真实分速度
方法：将SYSTEM_ZSET的bit0设置为0

设置代码：
DIM original_zset
original_zset = SYSTEM_ZSET                    ' 保存原始设置
SYSTEM_ZSET = original_zset AND (NOT 1)       ' 清除bit0

恢复代码：
DIM current_zset
current_zset = SYSTEM_ZSET
SYSTEM_ZSET = current_zset OR 1               ' 设置bit0

优势：
✅ VP_SPEED(0)直接显示X轴的真实速度
✅ VP_SPEED(3)直接显示Z轴的真实速度
✅ 可以显示正负值，更准确反映轴的运动方向
✅ 便于分析各轴在三段轨迹中的速度变化
```

【监控方案优化】

=== 优化后的监控配置 ===
```
设置SYSTEM_ZSET bit0=0后的监控信号：
VP_SPEED(0)：X轴单轴速度（可正可负）
VP_SPEED(3)：Z轴单轴速度（可正可负）
MSPEED(0)：X轴分速度（与VP_SPEED(0)效果一致）
MSPEED(3)：Z轴分速度（与VP_SPEED(3)效果一致）

推荐监控组合：
主要监控：VP_SPEED(0) + VP_SPEED(3)
辅助验证：MSPEED(0) + MSPEED(3)
```

=== 示波器显示设置 ===
```
推荐的示波器配置：
VP_SPEED(0)  垂直刻度 100，偏移 0     （蓝色）- X轴单轴速度
VP_SPEED(3)  垂直刻度 100，偏移 -60   （红色）- Z轴单轴速度
MSPEED(0)    垂直刻度 100，偏移 -120  （绿色）- X轴分速度（验证用）
MSPEED(3)    垂直刻度 100，偏移 -180  （黄色）- Z轴分速度（验证用）

触发设置：
TRIGGER      自动触发示波器
时间刻度：   根据运动时间调整（建议2-5秒）
```

【代码实现】

=== BeginContinuousPath()中的设置 ===
```basic
GLOBAL SUB BeginContinuousPath()
    BASE(0, 3)                      ' XZ两轴插补，X轴为主轴
    
    '设置VP_SPEED显示单轴速度
    DIM original_zset
    original_zset = SYSTEM_ZSET     ' 保存原始设置
    SYSTEM_ZSET = original_zset AND (NOT 1)  ' 清除bit0
    PRINT "设置VP_SPEED显示单轴速度，便于监控X轴和Z轴分速度"
    
    MERGE = ON                      ' 整个批次都保持连续插补
    CORNER_MODE = 32                ' 只倒角，不自动减速
    ZSMOOTH = 10                    ' 倒角半径10mm
    VP_MODE = 7, 7                  ' SS曲线
    SRAMP = 150, 150                ' S曲线时间150ms
    FORCE_SPEED = 80                ' 统一速度80mm/s
END SUB
```

=== EndContinuousPath()中的恢复 ===
```basic
GLOBAL SUB EndContinuousPath()
    WAIT IDLE(0)                    ' 等待X轴完成
    WAIT IDLE(3)                    ' 等待Z轴完成
    MERGE = OFF                     ' 关闭连续插补
    
    '恢复VP_SPEED为默认的插补速度显示
    DIM current_zset
    current_zset = SYSTEM_ZSET
    SYSTEM_ZSET = current_zset OR 1  ' 设置bit0
    PRINT "恢复VP_SPEED为插补速度显示"
END SUB
```

=== 速度监控设置函数 ===
```basic
GLOBAL SUB SetupSpeedMonitoring()
    TRIGGER                         ' 自动触发示波器
    PRINT "示波器监控设置："
    PRINT "VP_SPEED(0) - X轴单轴速度（蓝色，刻度100，偏移0）"
    PRINT "VP_SPEED(3) - Z轴单轴速度（红色，刻度100，偏移-60）"
    PRINT "MSPEED(0)  - X轴分速度（绿色，刻度100，偏移-120）"
    PRINT "MSPEED(3)  - Z轴分速度（黄色，刻度100，偏移-180）"
    PRINT "连续插补成功标志：各轴速度在衔接处不降到0，整体平滑连续"
END SUB
```

【三段轨迹速度分析】

=== 理想的单轴速度曲线 ===
```
第一段：抬Z（X不动，Z上升）
VP_SPEED(0)：X轴速度 = 0
VP_SPEED(3)：Z轴速度 > 0（正值，上升）

第二段：圆弧或直线（X和Z同时运动）
VP_SPEED(0)：X轴速度 ≠ 0（正值或负值，取决于方向）
VP_SPEED(3)：Z轴速度变化（可能正负变化）

第三段：下Z（X不动，Z下降）
VP_SPEED(0)：X轴速度 = 0
VP_SPEED(3)：Z轴速度 < 0（负值，下降）
```

=== 连续插补成功的标志 ===
```
X轴速度特征：
✅ 第一段和第三段：VP_SPEED(0) = 0
✅ 第二段：VP_SPEED(0)平滑变化，不突变
✅ 段间衔接：速度变化连续，无突跳

Z轴速度特征：
✅ 第一段：VP_SPEED(3) > 0（上升）
✅ 第二段：VP_SPEED(3)连续变化
✅ 第三段：VP_SPEED(3) < 0（下降）
✅ 段间衔接：速度不降到0，平滑过渡

整体特征：
✅ 无速度断点或突变
✅ 各轴协调运动
✅ 衔接处平滑过渡
```

【速度曲线对比分析】

=== 设置前后的监控对比 ===
```
设置前（SYSTEM_ZSET bit0=1）：
VP_SPEED(0)：插补合成速度 = √(X轴速度² + Z轴速度²)
VP_SPEED(3)：Z轴分速度
问题：无法直接看到X轴的真实速度变化

设置后（SYSTEM_ZSET bit0=0）：
VP_SPEED(0)：X轴单轴速度（可正可负）
VP_SPEED(3)：Z轴单轴速度（可正可负）
优势：直接显示各轴真实速度，便于分析
```

=== 理想的连续插补曲线 ===
```
VP_SPEED(0) - X轴单轴速度：
 ^
 |      /~~\
 |     /    \
 |    /      \
 |   /        \
 |  /          \
 | /            \
 +-----|-----|-----|---> 时间
      第1段 第2段 第3段
      (0)   (变化) (0)

VP_SPEED(3) - Z轴单轴速度：
 ^
 |  /\      /\
 | /  \    /  \
 |/    \  /    \
 |      \/      \
 +-----|-----|-----|---> 时间
      第1段 第2段 第3段
      (+)   (变化) (-)

特点：
✅ X轴在第二段有速度，第一、三段为0
✅ Z轴三段都有速度，方向可能变化
✅ 衔接处无断点，平滑连续
✅ 可以显示正负值，反映运动方向
```

【实际应用】

=== 监控测试流程 ===
```basic
'1. 设置速度监控
CALL SetupSpeedMonitoring()

'2. 执行连续轨迹测试
CALL BeginContinuousPath()
CALL PushThreeSegment(50, 10, 100, 30, 25, 25, 75, 50)
CALL EndContinuousPath()

'3. 分析速度曲线
'检查VP_SPEED(0)：X轴速度在第二段应该有值，第一三段为0
'检查VP_SPEED(3)：Z轴速度三段连续，无断点
'验证连续性：各轴速度衔接处不降到0
```

=== 问题诊断方法 ===
```
如果VP_SPEED(0)在衔接处有断点：
- 检查MERGE设置是否正确
- 确认无WAIT IDLE干扰
- 验证CORNER_MODE设置

如果VP_SPEED(3)有突变：
- 增大SRAMP时间
- 增大ZSMOOTH半径
- 调整VP_MODE设置

如果速度曲线不平滑：
- 检查轴参数ACCEL/DECEL
- 确认机械刚性足够
- 调整平滑参数
```

【优势总结】

=== SYSTEM_ZSET设置的优势 ===
```
监控精度提升：
✅ 直接显示各轴真实速度
✅ 可以显示正负值，反映方向
✅ 便于分析轴间协调关系
✅ 更准确地诊断连续插补效果

分析能力增强：
✅ 清晰看到X轴在三段中的速度变化
✅ 准确监控Z轴的连续性
✅ 便于发现速度衔接问题
✅ 支持精确的参数调优

使用便利性：
✅ 自动设置和恢复
✅ 不影响其他功能
✅ 兼容现有代码
✅ 提供详细的监控指导
```

=== 与MSPEED的关系 ===
```
设置SYSTEM_ZSET bit0=0后：
VP_SPEED(0) ≈ MSPEED(0)：X轴速度
VP_SPEED(3) ≈ MSPEED(3)：Z轴速度

区别：
VP_SPEED：命令速度，可正可负
MSPEED：分速度，通常为正值

推荐：
主要使用VP_SPEED监控（设置后）
辅助使用MSPEED验证
```

【注意事项】

=== 设置和恢复 ===
```
重要提醒：
✅ 在BeginContinuousPath()中设置
✅ 在EndContinuousPath()中恢复
✅ 避免影响其他系统功能
✅ 确保设置和恢复成对出现

错误做法：
❌ 只设置不恢复
❌ 在不合适的位置设置
❌ 忘记保存原始值
❌ 重复设置导致混乱
```

=== 兼容性考虑 ===
```
适用范围：
✅ 20170505以上固件版本支持
✅ 通用控制器都支持
✅ 不影响其他SYSTEM_ZSET位
✅ 可以与其他功能共存

使用建议：
✅ 仅在需要精确监控时使用
✅ 测试完成后及时恢复
✅ 避免长期保持此设置
✅ 注意与其他系统参数的协调
```

【总结】

SYSTEM_ZSET设置VP_SPEED单轴监控的关键：
✅ **精确设置**：bit0=0让VP_SPEED显示单轴速度，可正可负
✅ **自动管理**：BeginContinuousPath()设置，EndContinuousPath()恢复
✅ **监控优化**：VP_SPEED(0)+VP_SPEED(3)直接显示X轴和Z轴真实速度
✅ **分析增强**：便于诊断连续插补效果，发现速度衔接问题
✅ **使用便利**：提供SetupSpeedMonitoring()函数指导监控设置
✅ **兼容保证**：不影响其他功能，设置和恢复成对管理

通过SYSTEM_ZSET的优化设置，
VP_SPEED现在可以精确显示各轴的真实速度变化，
为连续插补的分析和优化提供了更准确的监控手段。

===============================================================================
