# 正运动Zbasic带Z信号回零详细教程

## 📋 目录
1. [Z信号回零基础概念](#Z信号回零基础概念)
2. [Z信号硬件连接要求](#Z信号硬件连接要求)
3. [Z信号回零模式详解](#Z信号回零模式详解)
4. [Z信号回零参数配置](#Z信号回零参数配置)
5. [Z信号回零编程实现](#Z信号回零编程实现)
6. [Z信号回零完整示例](#Z信号回零完整示例)
7. [Z信号回零故障排除](#Z信号回零故障排除)
8. [Z信号回零最佳实践](#Z信号回零最佳实践)

---

## 🎯 Z信号回零基础概念

### 什么是Z信号回零？
Z信号回零是利用编码器的Z相信号（索引脉冲）进行高精度原点定位的回零方式。Z信号是编码器每转一圈产生一个脉冲的索引信号，具有极高的重复定位精度，是精密定位应用的首选回零方式。

### Z信号回零的优势
- **超高精度**：定位精度可达±0.25个编码器脉冲
- **重复性好**：每次回零都能回到完全相同的位置
- **不受磨损影响**：不依赖机械开关，无接触磨损
- **适合高速**：可以在较高速度下进行精确定位
- **长期稳定**：不会因为机械磨损而影响精度

### 为什么需要回零？
- **建立坐标系**：确定运动的起始参考点
- **消除累积误差**：清除断电或异常导致的位置偏差
- **保证精度**：为后续精确运动奠定基础
- **安全要求**：确保设备在已知位置启动

### 回零的基本原理
1. **快速寻找**：以较高速度（SPEED）向原点方向运动
2. **碰到原点开关**：检测到原点信号后开始减速
3. **精确定位**：以较低速度（CREEP）进行精确定位
4. **位置清零**：将当前位置设为坐标原点（DPOS=0）

---

## 🔌 Z信号硬件连接要求

### Z信号回零必需的硬件信号

#### 1. 带Z信号的编码器（必需）
- **A相信号**：连接到控制器编码器输入的A相端子
- **B相信号**：连接到控制器编码器输入的B相端子
- **Z相信号**：连接到控制器编码器输入的Z相端子（**关键**）
- **电源**：编码器供电（通常5V或24V）
- **屏蔽**：使用屏蔽电缆，屏蔽层接地

**重要说明**：Z信号回零必须使用带Z相输出的编码器，普通AB相编码器无法实现Z信号回零。

#### 2. 原点开关（模式5/6需要）
- **作用**：配合Z信号实现更高精度的回零
- **连接**：通过 `DATUM_IN` 指令映射到输入口
- **类型**：接近开关、光电开关或微动开关

#### 3. 限位开关（安全保护）
- **正向限位**：通过 `FWD_IN` 指令映射
- **负向限位**：通过 `REV_IN` 指令映射
- **作用**：防止轴运动超出安全范围，Z信号回零时提供安全保护

#### 3. 信号电平说明（重要）

根据正运动官方手册，不同控制器系列的信号触发方式不同：

**ZMC系列控制器**：
- **触发方式**：0触发有效
- **信号状态**：输入为OFF状态时，表示到达原点/限位
- **常开信号**：需要采用INVERT_IN反转电平
- **常闭信号**：不需要反转

**ECI系列控制器**：
- **触发方式**：1触发有效
- **信号状态**：输入为ON状态时，表示到达原点/限位
- **常开信号**：不需要反转
- **常闭信号**：需要采用INVERT_IN反转电平
- **信号反转**：使用 `INVERT_IN` 指令调整信号极性

### 典型硬件连接图
```
控制器输入端口    |    传感器类型    |    连接说明
IN8              |    X轴原点开关   |    DATUM_IN = 8
IN9              |    Y轴原点开关   |    DATUM_IN = 9
IN10             |    Z轴原点开关   |    DATUM_IN = 10
IN11             |    X轴正限位     |    FWD_IN = 11
IN12             |    X轴负限位     |    REV_IN = 12
```

---

## 📊 Z信号回零模式详解

### Z信号回零专用模式

根据正运动官方手册，Z信号回零必须配置为ATYPE=4或7，支持以下专用模式：

| 模式 | 官方描述 | 运动过程 | 精度等级 | 适用场景 |
|------|----------|----------|----------|----------|
| **模式1** | Z信号正向回零 | CREEP正向→Z信号→停止 | ⭐⭐⭐⭐ | 编码器Z信号稳定，直接寻找 |
| **模式2** | Z信号负向回零 | CREEP负向→Z信号→停止 | ⭐⭐⭐⭐ | 编码器Z信号稳定，反向寻找 |
| **模式5** | 原点+Z信号正向 | SPEED正向→原点→CREEP反向→离开→CREEP反向→Z信号 | ⭐⭐⭐⭐⭐ | **最高精度**，推荐使用 |
| **模式6** | 原点+Z信号负向 | SPEED负向→原点→CREEP正向→离开→CREEP正向→Z信号 | ⭐⭐⭐⭐⭐ | **最高精度**，反向回零 |

### Z信号回零模式选择指南

#### 模式1/2：直接Z信号回零
- **优点**：速度快，流程简单
- **缺点**：需要确保Z信号位置在安全范围内
- **适用**：编码器Z信号位置已知且稳定的场合

#### 模式5/6：原点开关+Z信号回零（推荐）
- **优点**：精度最高，安全性好
- **缺点**：流程较复杂，时间稍长
- **适用**：高精度定位要求的精密设备

### Z信号回零关键要求

#### 1. ATYPE配置要求（必须）
根据官方手册，Z信号回零必须配置为带Z信号的ATYPE：
- **ATYPE = 4**：脉冲方向输出+正交编码器输入（推荐）
- **ATYPE = 7**：模拟量输出+正交编码器输入

#### 2. 编码器要求
- **必须带Z信号**：编码器必须有Z相输出
- **Z信号质量**：Z信号必须稳定可靠，无抖动
- **信号连接**：Z信号必须正确连接到控制器Z相输入端子

#### 3. Z信号回零精度优势
- **模式1/2精度**：±1个编码器脉冲
- **模式5/6精度**：±0.25个编码器脉冲（最高精度）
- **重复性**：每次回零位置完全一致

#### 4. 4系列控制器特殊功能
- **模式101/102**：对应模式1/2的自动清零MPOS版本
- **模式105/106**：对应模式5/6的自动清零MPOS版本

### 模式1详细说明（直接Z信号回零）

根据官方手册，模式1的完整运动过程：

```
运动过程：
1. 轴以CREEP速度正向运行直到Z信号出现
2. 碰到限位开关会直接停止
3. DPOS值重置为0同时纠正MPOS

适用场景：
- 编码器Z信号位置已知且在安全范围内
- 需要快速回零的场合
- Z信号质量稳定可靠

安全保护：
- 碰到限位开关会直接停止
```

### 模式5详细说明（原点+Z信号回零，推荐）

根据官方手册，模式5是精度最高的Z信号回零方式：

```
运动过程：
1. 轴以SPEED速度正向运行，直到碰到原点开关
2. 然后轴以CREEP速度反向运动直到离开原点开关
3. 然后再继续以爬行速度反转直到碰到Z信号
4. DPOS值重置为0同时纠正MPOS

精度优势：
- 结合了原点开关的粗定位和Z信号的精定位
- 定位精度可达±0.25个编码器脉冲
- 重复定位精度极高

安全保护：
- 碰到限位开关会直接停止
- 先找到原点开关确保安全范围
```

---

## ⚙️ Z信号回零参数配置

### Z信号回零基本轴参数

#### ATYPE轴类型设置（必须正确）

**Z信号回零的核心要求**：必须配置为带Z信号的ATYPE类型

```basic
BASE(0)                    ' 选择轴号

' Z信号回零必须的轴类型设置
ATYPE = 4                  ' 脉冲方向输出+正交编码器输入（推荐）
' 或者
ATYPE = 7                  ' 模拟量输出+正交编码器输入

UNITS = 1000               ' 脉冲当量：每单位脉冲数（建议1000以上）
SPEED = 100                ' 回零速度（units/s）
CREEP = 10                 ' 爬行速度（units/s，Z信号寻找速度）
ACCEL = 1000               ' 加速度（units/s²）
DECEL = 1000               ' 减速度（units/s²）
HOMEWAIT = 20              ' 反找等待时间（ms）
```

**Z信号回零ATYPE说明**：
- **ATYPE = 4**：适用于步进电机+编码器或伺服脉冲控制+编码器反馈
- **ATYPE = 7**：适用于伺服模拟量控制+编码器反馈
- **其他ATYPE**：无法实现Z信号回零功能

#### Z信号回零速度参数优化
- **SPEED**：不宜过快，建议50-200 units/s
- **CREEP**：Z信号寻找速度，建议5-20 units/s，越慢精度越高
- **UNITS**：建议设置较大值（1000以上），提高分辨率

### Z信号回零信号映射配置

#### 模式1/2（直接Z信号回零）配置
直接Z信号回零不需要原点开关，只需要配置限位保护：

```basic
' 限位信号映射（安全保护）
FWD_IN = 11                ' 正向限位连接到IN11
REV_IN = 12                ' 负向限位连接到IN12

' ZMC系列控制器信号反转
INVERT_IN(11, ON)          ' 常开限位信号反转
INVERT_IN(12, ON)

' ECI系列控制器
' 常闭信号才需要反转
```

#### 模式5/6（原点+Z信号回零）配置
需要配置原点开关和限位开关：

##### ZMC系列控制器（0触发有效）
```basic
' 原点信号映射
DATUM_IN = 8               ' 原点开关连接到IN8
INVERT_IN(8, ON)           ' 常开信号需要反转（ZMC系列OFF有效）

' 限位信号映射
FWD_IN = 11                ' 正向限位连接到IN11
REV_IN = 12                ' 负向限位连接到IN12
INVERT_IN(11, ON)          ' 常开限位信号反转
INVERT_IN(12, ON)
```

##### ECI系列控制器（1触发有效）
```basic
' 原点信号映射
DATUM_IN = 8               ' 原点开关连接到IN8
' INVERT_IN(8, ON)         ' 常闭信号需要反转（ECI系列ON有效）

' 限位信号映射
FWD_IN = 11                ' 正向限位连接到IN11
REV_IN = 12                ' 负向限位连接到IN12
' 常闭信号才需要反转
```

**Z信号回零信号要求**：
- **Z信号**：由编码器硬件连接提供，无需软件配置
- **原点开关**：模式5/6需要，用于粗定位
- **限位开关**：所有模式都建议配置，提供安全保护

### 参数选择建议
- **SPEED**：根据机械结构选择，一般50-500 units/s
- **CREEP**：SPEED的1/5到1/10，保证精度
- **HOMEWAIT**：脉冲轴建议20ms，总线轴可更短
- **信号反转**：根据传感器类型和控制器型号确定

---

## 💻 Z信号回零编程实现

### 步骤1：Z信号回零系统初始化

```basic
GLOBAL SUB ZSignalHomeInit()
    ' Z信号回零轴参数设置
    BASE(0)
    ATYPE = 4                  ' 必须：脉冲+编码器（Z信号回零要求）
    UNITS = 1000               ' 脉冲当量（建议1000以上）
    SPEED = 100                ' 回零速度（不宜过快）
    CREEP = 10                 ' 爬行速度（Z信号寻找速度）
    ACCEL = 1000               ' 加速度
    DECEL = 1000               ' 减速度
    HOMEWAIT = 20              ' 反找等待时间

    ' 信号映射（模式5/6需要原点开关）
    DATUM_IN = 8               ' 原点开关（模式5/6使用）
    FWD_IN = 11                ' 正向限位（安全保护）
    REV_IN = 12                ' 负向限位（安全保护）

    ' 信号反转（ZMC系列）
    INVERT_IN(8, ON)           ' 原点开关信号反转
    INVERT_IN(11, ON)          ' 限位信号反转
    INVERT_IN(12, ON)

    PRINT "Z信号回零系统初始化完成"
    PRINT "ATYPE=4: 脉冲+编码器（支持Z信号）"
END SUB
```

### 步骤2：执行Z信号回零

根据官方DATUM指令规范，Z信号回零的标准执行流程：

#### 模式1：直接Z信号回零
```basic
GLOBAL SUB ExecuteZSignalHome1(axis_num)
    PRINT "开始轴", axis_num, "直接Z信号回零（模式1）"

    BASE(axis_num)

    ' 清除轴错误状态（官方推荐）
    DATUM(0) AXIS(axis_num)     ' 清除指定轴的错误状态
    DELAY(10)

    ' 执行Z信号回零指令
    DATUM(1)                    ' 模式1：CREEP正向→Z信号→停止

    ' 等待回零完成
    WAIT UNTIL IDLE(axis_num) = -1
    DELAY(10)  ' 状态稳定延时

    ' 检查回零结果
    IF AXISSTATUS(axis_num) = 0 THEN
        ' Z信号回零成功 - DATUM指令已自动清零DPOS并纠正MPOS
        PRINT "轴", axis_num, "Z信号回零成功！"
        PRINT "DPOS位置：", DPOS(axis_num)    ' 应该为0
        PRINT "MPOS位置：", MPOS(axis_num)    ' 已被纠正
        PRINT "精度：±1个编码器脉冲"
        RETURN 1  ' 返回成功标志
    ELSE
        ' 回零失败处理
        PRINT "轴", axis_num, "Z信号回零失败！"
        PRINT "错误状态：", HEX(AXISSTATUS(axis_num))
        CALL AnalyzeZSignalError(axis_num)
        RETURN 0  ' 返回失败标志
    ENDIF
END SUB
```

#### 模式5：原点+Z信号回零（推荐）
```basic
GLOBAL SUB ExecuteZSignalHome5(axis_num)
    PRINT "开始轴", axis_num, "原点+Z信号回零（模式5）"

    BASE(axis_num)

    ' 清除轴错误状态
    DATUM(0) AXIS(axis_num)
    DELAY(10)

    ' 执行最高精度Z信号回零
    DATUM(5)                    ' 模式5：原点→离开→Z信号→停止

    ' 等待回零完成
    WAIT UNTIL IDLE(axis_num) = -1
    DELAY(10)

    ' 检查回零结果
    IF AXISSTATUS(axis_num) = 0 THEN
        ' 最高精度Z信号回零成功
        PRINT "轴", axis_num, "最高精度Z信号回零成功！"
        PRINT "DPOS位置：", DPOS(axis_num)
        PRINT "MPOS位置：", MPOS(axis_num)
        PRINT "精度：±0.25个编码器脉冲（最高精度）"
        RETURN 1
    ELSE
        PRINT "轴", axis_num, "Z信号回零失败！"
        PRINT "错误状态：", HEX(AXISSTATUS(axis_num))
        CALL AnalyzeZSignalError(axis_num)
        RETURN 0
    ENDIF
END SUB
```

**Z信号回零重要说明**：
- **ATYPE要求**：必须配置为ATYPE=4或7
- **自动清零**：DATUM指令成功后会自动将DPOS重置为0并纠正MPOS
- **精度优势**：模式1/2精度±1脉冲，模式5/6精度±0.25脉冲
- **Z信号质量**：确保编码器Z信号稳定可靠

### 步骤3：多轴回零
```basic
GLOBAL SUB MultiAxisHoming()
    LOCAL home_success
    home_success = 1
    
    ' 按顺序回零各轴
    FOR i = 0 TO 2
        IF ExecuteHoming(i, 3) = 0 THEN
            home_success = 0
            PRINT "多轴回零失败，停止在轴", i
            EXIT  ' 退出循环
        ENDIF
        DELAY(500)  ' 轴间延时
    NEXT
    
    IF home_success = 1 THEN
        PRINT "所有轴回零成功！"
    ELSE
        PRINT "多轴回零失败！"
    ENDIF
    
    RETURN home_success
END SUB
```

---

## 📝 完整示例代码

### 基础回零示例
```basic
'=============================================================================
' 实用回零示例程序
' 基于正运动官方手册和最佳实践
' 适用于ZMC系列运动控制器
'=============================================================================

'================ 全局变量定义 ================
GLOBAL gv_AxisMax                    '轴数量
GLOBAL ga_AxisNum(3)                 '轴号定义
GLOBAL gv_Status                     '系统状态：0-停止，1-运行中，2-已完成
GLOBAL ga_StatusAxis(3)              '各轴状态：0-未回零，1-回零中，2-已回零，3-失败
GLOBAL ga_HomeMode(3)                '回零方式：3-正向原点，4-负向原点，13-限位反找
GLOBAL ga_HomeEnable(3)              '回零使能：0-使能，1-禁用

'常量定义
CONST MAX_AXES = 3       '最大轴数
CONST HOME_SPEED = 200   '回零速度
CONST CREEP_SPEED = 20   '爬行速度

'================ 主程序 ================
PRACTICAL_INIT()     '系统初始化

PRINT "=== 实用回零示例程序 ==="
PRINT "基于正运动官方手册和最佳实践"
PRINT ""
PRINT "操作说明："
PRINT "IN0 - 单轴回零（轴0）"
PRINT "IN1 - 多轴顺序回零"
PRINT "IN2 - 多轴同时回零"
PRINT "IN3 - 状态检查"
PRINT "IN4 - 信号测试"
PRINT "IN7 - 紧急停止"

WHILE 1         '主循环
    PRACTICAL_SCAN() '主程序扫描
WEND

'================ 初始化子程序 ================
GLOBAL SUB PRACTICAL_INIT()
    '--- 变量初始化 ---
    gv_AxisMax = 3
    ga_AxisNum(0) = 0                    'X轴（0轴）
    ga_AxisNum(1) = 1                    'Y轴（1轴）
    ga_AxisNum(2) = 2                    'Z轴（2轴）
    gv_Status = 0
    
    '初始化轴状态数组
    FOR i = 0 TO 2
        ga_StatusAxis(i) = 0
        ga_HomeMode(i) = 3               '默认回零模式3（正向找原点）
        ga_HomeEnable(i) = 0             '默认使能回零
    NEXT
    
    '--- 轴参数初始化 ---
    CALL PracticalAxisInit()

    PRINT "系统初始化完成"
END SUB

'================ 轴参数初始化 ================
GLOBAL SUB PracticalAxisInit()
    BASE(ga_AxisNum(0), ga_AxisNum(1), ga_AxisNum(2))
    ATYPE = 1, 1, 1                      '脉冲轴
    UNITS = 100, 100, 100                '脉冲当量
    ACCEL = 1000, 1000, 1000             '加速度
    DECEL = 1000, 1000, 1000             '减速度
    SPEED = HOME_SPEED, HOME_SPEED, HOME_SPEED    '回零速度
    CREEP = CREEP_SPEED, CREEP_SPEED, CREEP_SPEED '爬行速度
    HOMEWAIT = 20, 20, 20                '反找等待时间(ms)
    
    '原点输入设置
    DATUM_IN = 8, 9, 10                  '原点输入：IN8, IN9, IN10
    INVERT_IN(8, ON)                     '反转原点信号（ZMC系列OFF有效）
    INVERT_IN(9, ON)
    INVERT_IN(10, ON)
    
    '限位输入设置
    FWD_IN = 11, 12, 13                  '正向限位
    REV_IN = 14, 15, 16                  '负向限位
    INVERT_IN(11, ON)
    INVERT_IN(12, ON)
    INVERT_IN(13, ON)
    INVERT_IN(14, ON)
    INVERT_IN(15, ON)
    INVERT_IN(16, ON)
    
    '关闭所有输出
    FOR i = 0 TO 7
        OP(i, OFF)
    NEXT
END SUB

'================ 主程序扫描子程序 ================
GLOBAL SUB PRACTICAL_SCAN()
    '--- 扫描输入信号事件 ---
    IF SCAN_EVENT(IN(0)) > 0 THEN       '单轴回零
        CALL practical_home_axis(0)
    ENDIF

    IF SCAN_EVENT(IN(1)) > 0 THEN       '多轴顺序回零
        CALL practical_home_sequential()
    ENDIF

    IF SCAN_EVENT(IN(2)) > 0 THEN       '多轴同时回零
        CALL practical_home_simul()
    ENDIF

    IF SCAN_EVENT(IN(3)) > 0 THEN       '状态检查
        CALL PracticalCheckStatus()
    ENDIF

    IF SCAN_EVENT(IN(4)) > 0 THEN       '信号测试
        CALL PracticalTestSignals()
    ENDIF

    '紧急停止
    IF IN(7) = ON THEN
        CALL practical_stop()
    ENDIF
    
    '状态指示灯
    IF gv_Status = 1 THEN               '回零中
        OP(0, TICKS AND 1)              '红灯闪烁
        OP(1, OFF)
    ELSEIF gv_Status = 2 THEN           '回零完成
        OP(0, OFF)
        OP(1, ON)                       '绿灯亮
    ELSE                                '待机
        OP(0, OFF)
        OP(1, OFF)
    ENDIF
    
    DELAY(50)
END SUB

'================ 单轴回零处理 ================
GLOBAL SUB practical_home_axis(num)
    IF gv_Status <> 1 THEN              '判断系统状态不在回零中
        STOPTASK 2
        RUN "practical_task_home_axis", 2, num
    ELSE
        PRINT "系统忙，无法回零"
    ENDIF
END SUB

GLOBAL SUB practical_task_home_axis(num)
    PRINT "=== 单轴回零：轴", num, " ==="
    
    gv_Status = 1                        '状态切换成回零中
    ga_StatusAxis(num) = 1
    
    '执行回零前检查
    IF PracticalSystemCheck() = 0 THEN
        PRINT "系统检查失败，停止回零"
        gv_Status = 0
        ga_StatusAxis(num) = 3
        RETURN
    ENDIF
    
    BASE(num)
    
    '清除轴状态
    AXIS_STOPREASON = 0
    
    '根据回零模式执行不同的回零指令
    IF ga_HomeMode(num) = 3 THEN         '正向找原点
        DATUM(3)
    ELSEIF ga_HomeMode(num) = 4 THEN     '负向找原点
        DATUM(4)
    ELSEIF ga_HomeMode(num) = 13 THEN    '正向找原点+限位反找
        DATUM(13)
    ELSEIF ga_HomeMode(num) = 14 THEN    '负向找原点+限位反找
        DATUM(14)
    ELSE
        DATUM(3)                         '默认使用模式3
    ENDIF
    
    '等待回零完成
    WAIT UNTIL IDLE(num) = -1
    DELAY(10)  '状态稳定延时
    
    '检查回零结果
    IF AXISSTATUS(num) = 0 THEN
        '回零成功处理
        DPOS(num) = 0                    '清零位置
        MPOS(num) = 0                    '清零编码器位置
        ga_StatusAxis(num) = 2           '设置为已回零
        PRINT "轴", num, "回零成功！位置：", DPOS(num)
    ELSE
        '回零失败处理
        ga_StatusAxis(num) = 3           '设置为回零失败
        PRINT "轴", num, "回零失败！状态：", HEX(AXISSTATUS(num))
        CALL PracticalAnalyzeError(num, AXISSTATUS(num))
    ENDIF
    
    gv_Status = 0                        '状态切换成待机
END SUB

'================ 多轴顺序回零处理 ================
GLOBAL SUB practical_home_sequential()
    IF gv_Status <> 1 THEN
        STOPTASK 2
        RUN "practical_task_sequential", 2
    ELSE
        PRINT "系统忙，无法回零"
    ENDIF
END SUB

GLOBAL SUB practical_task_sequential()
    PRINT "=== 多轴顺序回零演示 ==="
    
    gv_Status = 1                        '状态切换成回零中
    
    '设置所有轴状态为回零中
    FOR i = 0 TO gv_AxisMax - 1
        IF ga_HomeEnable(i) = 0 THEN
            ga_StatusAxis(i) = 1
        ENDIF
    NEXT
    
    '按轴号顺序回零（可根据需要调整顺序）
    FOR i = 0 TO gv_AxisMax - 1
        IF ga_HomeEnable(i) = 0 THEN
            BASE(i)
            PRINT "开始轴", i, "回零..."
            
            '根据回零模式执行
            IF ga_HomeMode(i) = 3 THEN
                DATUM(3)
            ELSEIF ga_HomeMode(i) = 4 THEN
                DATUM(4)
            ELSEIF ga_HomeMode(i) = 13 THEN
                DATUM(13)
            ELSEIF ga_HomeMode(i) = 14 THEN
                DATUM(14)
            ELSE
                DATUM(3)                 '默认模式
            ENDIF
            
            '等待当前轴回零完成
            WAIT UNTIL IDLE(i) = -1
            DELAY(10)
            
            '检查回零结果
            IF AXISSTATUS(i) = 0 THEN
                ga_StatusAxis(i) = 2
                DPOS(i) = 0
                MPOS(i) = 0
                PRINT "轴", i, "回零成功"
            ELSE
                ga_StatusAxis(i) = 3
                PRINT "轴", i, "回零失败，状态：", HEX(AXISSTATUS(i))
                '如果有轴失败，停止后续回零
                gv_Status = 0
                PRINT "回零过程中断，轴", i, "失败"
                RETURN
            ENDIF
            
            DELAY(200)  '轴间延时
        ENDIF
    NEXT
    
    gv_Status = 2                        '状态切换成已完成
    PRINT "多轴顺序回零完成！"
END SUB

'================ 多轴同时回零处理 ================
GLOBAL SUB practical_home_simul()
    IF gv_Status <> 1 THEN
        STOPTASK 2
        RUN "practical_task_simul", 2
    ELSE
        PRINT "系统忙，无法回零"
    ENDIF
END SUB

GLOBAL SUB practical_task_simul()
    PRINT "=== 多轴同时回零演示 ==="
    
    gv_Status = 1                        '状态切换成回零中
    
    '同时启动所有轴回零
    FOR i = 0 TO gv_AxisMax - 1
        IF ga_HomeEnable(i) = 0 THEN
            ga_StatusAxis(i) = 1
            BASE(i)
            
            '根据模式执行回零
            IF ga_HomeMode(i) = 3 THEN
                DATUM(3)
            ELSEIF ga_HomeMode(i) = 4 THEN
                DATUM(4)
            ELSEIF ga_HomeMode(i) = 13 THEN
                DATUM(13)
            ELSEIF ga_HomeMode(i) = 14 THEN
                DATUM(14)
            ELSE
                DATUM(3)                 '默认模式
            ENDIF
            
            PRINT "轴", i, "开始回零..."
        ENDIF
    NEXT
    
    '等待所有轴回零完成
    WAIT UNTIL IDLE(0) = -1 AND IDLE(1) = -1 AND IDLE(2) = -1
    DELAY(10)
    
    '检查所有轴的回零结果
    DIM home_success
    home_success = 1
    
    FOR i = 0 TO gv_AxisMax - 1
        IF ga_HomeEnable(i) = 0 THEN
            IF AXISSTATUS(i) = 0 THEN
                ga_StatusAxis(i) = 2
                DPOS(i) = 0
                MPOS(i) = 0
                PRINT "轴", i, "回零成功，位置：", DPOS(i)
            ELSE
                ga_StatusAxis(i) = 3
                PRINT "轴", i, "回零失败，状态：", HEX(AXISSTATUS(i))
                home_success = 0
            ENDIF
        ENDIF
    NEXT
    
    IF home_success = 1 THEN
        gv_Status = 2                    '所有轴回零成功
        PRINT "所有轴同时回零完成！"
    ELSE
        gv_Status = 0                    '有轴回零失败
        PRINT "部分轴回零失败！"
    ENDIF
END SUB

'================ 停止按钮处理 ================
GLOBAL SUB practical_stop()
    STOPTASK 2
    RAPIDSTOP(2)
    gv_Status = 0

    FOR j = 0 TO gv_AxisMax - 1
        IF ga_StatusAxis(j) = 1 THEN
            ga_StatusAxis(j) = 0
        ENDIF
    NEXT

    PRINT "紧急停止！所有轴停止运动"
END SUB

'================ 系统检查 ================
GLOBAL SUB PracticalSystemCheck()
    PRINT "执行系统检查..."

    '检查急停状态
    IF IN(7) = ON THEN
        PRINT "错误：急停信号有效，无法回零"
        RETURN 0
    ENDIF

    '检查驱动器使能
    FOR i = 0 TO gv_AxisMax - 1
        IF AXIS_ENABLE(i) = 0 THEN
            PRINT "警告：轴", i, "未使能"
        ENDIF
    NEXT

    PRINT "系统检查通过"
    RETURN 1
END SUB

'================ 回零状态检查 ================
GLOBAL SUB PracticalCheckStatus()
    PRINT "=== 回零状态检查 ==="

    FOR i = 0 TO gv_AxisMax - 1
        PRINT "轴", i, "状态检查："
        PRINT "  位置：", DPOS(i)
        PRINT "  编码器位置：", MPOS(i)
        PRINT "  轴状态：", HEX(AXISSTATUS(i))

        IF IDLE(i) = -1 THEN
            PRINT "  运动状态：停止"
        ELSE
            PRINT "  运动状态：运动中"
        ENDIF

        PRINT "  回零状态：", ga_StatusAxis(i), "(0-未回零,1-回零中,2-已回零,3-失败)"

        '检查是否在原点附近
        IF ABS(DPOS(i)) < 1 THEN
            PRINT "  原点状态：在原点"
        ELSE
            PRINT "  原点状态：偏离原点", DPOS(i), "单位"
        ENDIF

        '检查原点输入信号
        DIM home_input
        home_input = IN(8 + i)
        IF home_input = ON THEN
            PRINT "  原点信号：有效"
        ELSE
            PRINT "  原点信号：无效"
        ENDIF

        PRINT ""
    NEXT

    '显示系统状态
    PRINT "系统状态：", gv_Status, "(0-停止,1-运行中,2-已完成)"
    IF gv_Status = 0 THEN
        PRINT "状态说明：待机"
    ELSEIF gv_Status = 1 THEN
        PRINT "状态说明：回零中"
    ELSEIF gv_Status = 2 THEN
        PRINT "状态说明：回零完成"
    ENDIF
END SUB

'================ 信号测试 ================
GLOBAL SUB PracticalTestSignals()
    PRINT "=== 原点信号测试 ==="
    PRINT "测试时间：10秒，请手动触发各轴原点开关"

    FOR test_time = 1 TO 100  '10秒测试
        PRINT "时间:", test_time * 0.1, "s"

        FOR i = 0 TO gv_AxisMax - 1
            DIM home_signal, fwd_signal, rev_signal
            home_signal = IN(8 + i)
            fwd_signal = IN(11 + i)
            rev_signal = IN(14 + i)

            PRINT "轴", i, ": 原点=", home_signal, " 正限位=", fwd_signal, " 负限位=", rev_signal
        NEXT

        PRINT "---"
        DELAY(100)
    NEXT

    PRINT "信号测试完成"
END SUB

'================ 轴错误分析子程序 ================
GLOBAL SUB PracticalAnalyzeError(axis_num, status_value)
    PRINT "轴", axis_num, "错误分析："

    IF status_value AND 1 THEN
        PRINT "  - 轴错误标志置位"
    ENDIF

    IF status_value AND 2 THEN
        PRINT "  - 碰到正向硬限位"
    ENDIF

    IF status_value AND 4 THEN
        PRINT "  - 碰到负向硬限位"
    ENDIF

    IF status_value AND 8 THEN
        PRINT "  - 急停信号有效"
    ENDIF

    IF status_value AND 16 THEN
        PRINT "  - 驱动器报警"
    ENDIF

    '检查原点信号状态
    DIM home_signal
    home_signal = IN(8 + axis_num)
    IF home_signal = ON THEN
        PRINT "  - 原点信号状态：有效"
    ELSE
        PRINT "  - 原点信号状态：无效"
    ENDIF

    '建议解决方案
    PRINT "建议检查："
    PRINT "  1. 原点开关连接和信号"
    PRINT "  2. 限位开关状态"
    PRINT "  3. 驱动器使能和报警"
    PRINT "  4. 轴运动方向设置"
END SUB
```

---

## 🔧 故障排除指南

### 常见问题及解决方案

#### 1. 回零失败：AXISSTATUS ≠ 0

**问题现象**：回零指令执行后，AXISSTATUS返回非零值

**状态码对照表**：
| 状态码 | 含义 | 解决方案 |
|--------|------|----------|
| 0x01 | 轴错误标志 | 检查驱动器使能和连接 |
| 0x02 | 正向硬限位 | 检查正限位开关状态 |
| 0x04 | 负向硬限位 | 检查负限位开关状态 |
| 0x08 | 急停信号 | 检查急停开关状态 |
| 0x10 | 驱动器报警 | 检查驱动器报警信号 |

**调试代码**：
```basic
SUB DiagnoseHomingError(axis_num)
    LOCAL status_code
    status_code = AXISSTATUS(axis_num)

    PRINT "轴", axis_num, "回零诊断："
    PRINT "状态码：0x", HEX(status_code)

    IF status_code AND 1 THEN
        PRINT "错误：轴错误标志置位"
        PRINT "解决：检查AXIS_ENABLE和驱动器连接"
    ENDIF

    IF status_code AND 2 THEN
        PRINT "错误：碰到正向限位"
        PRINT "解决：手动移动轴离开限位或使用模式13"
    ENDIF

    IF status_code AND 4 THEN
        PRINT "错误：碰到负向限位"
        PRINT "解决：手动移动轴离开限位或使用模式14"
    ENDIF

    PRINT "原点信号状态：", IN(DATUM_IN(axis_num))
    PRINT "正限位信号：", IN(FWD_IN(axis_num))
    PRINT "负限位信号：", IN(REV_IN(axis_num))
END SUB
```

#### 2. 找不到原点信号

**检查步骤**：
```basic
SUB CheckHomeSignal(axis_num)
    LOCAL home_input
    home_input = DATUM_IN(axis_num)

    PRINT "原点信号检查："
    PRINT "映射输入口：IN", home_input
    PRINT "当前信号状态：", IN(home_input)

    ' 手动测试原点开关
    PRINT "请手动触发原点开关，观察信号变化..."
    FOR i = 1 TO 20
        PRINT "IN", home_input, "=", IN(home_input)
        DELAY(200)
    NEXT
END SUB
```

#### 3. 回零精度优化

```basic
SUB OptimizeHomingAccuracy(axis_num)
    BASE(axis_num)

    ' 降低爬行速度提高精度
    CREEP = 5  ' 原来20，现在5

    ' 增加反找等待时间
    HOMEWAIT = 50  ' 原来20，现在50

    ' 使用更精确的回零模式
    ' DATUM(5)  ' 使用Z信号回零（需要编码器支持）

    PRINT "已优化轴", axis_num, "回零精度参数"
END SUB
```

---

## 🏆 最佳实践建议

### 1. 标准回零流程

```basic
GLOBAL SUB StandardHomingProcedure()
    PRINT "=== 标准回零流程 ==="

    ' 步骤1：系统检查
    IF SystemCheck() = 0 THEN
        PRINT "系统检查失败，停止回零"
        RETURN
    ENDIF

    ' 步骤2：执行回零
    FOR i = 0 TO axis_count - 1
        CALL SingleAxisHome(i)
        IF home_status(i) <> 2 THEN
            PRINT "轴", i, "回零失败，停止流程"
            RETURN
        ENDIF
        DELAY(500)
    NEXT

    ' 步骤3：结果验证
    CALL VerifyHomingResult()
    PRINT "标准回零流程完成"
END SUB

GLOBAL SUB SystemCheck()
    ' 检查急停状态
    IF IN(7) = ON THEN
        PRINT "错误：急停信号有效"
        RETURN 0
    ENDIF

    ' 检查驱动器使能
    FOR i = 0 TO axis_count - 1
        IF AXIS_ENABLE(i) = 0 THEN
            PRINT "警告：轴", i, "未使能"
        ENDIF
    NEXT

    PRINT "系统检查通过"
    RETURN 1
END SUB
```

### 2. 回零成功判断标准

```basic
GLOBAL SUB VerifyHomingResult()
    PRINT "=== 回零结果验证 ==="

    FOR i = 0 TO axis_count - 1
        PRINT "轴", i, "验证："

        ' 检查1：轴状态正常
        IF AXISSTATUS(i) = 0 THEN
            PRINT "  ✓ 轴状态正常"
        ELSE
            PRINT "  ✗ 轴状态异常：", HEX(AXISSTATUS(i))
        ENDIF

        ' 检查2：位置已清零
        IF ABS(DPOS(i)) < 0.1 THEN
            PRINT "  ✓ 位置已清零：", DPOS(i)
        ELSE
            PRINT "  ⚠ 位置偏差：", DPOS(i)
        ENDIF

        ' 检查3：轴已停止
        IF IDLE(i) = -1 THEN
            PRINT "  ✓ 轴已停止"
        ELSE
            PRINT "  ✗ 轴仍在运动"
        ENDIF

        PRINT ""
    NEXT
END SUB
```

### 3. 安全保护措施

```basic
GLOBAL SUB HomingSafetyProtection()
    ' 设置软限位保护
    FOR i = 0 TO axis_count - 1
        BASE(i)
        FS_LIMIT = 500   ' 正向软限位
        RS_LIMIT = -500  ' 负向软限位
    NEXT

    ' 设置急停保护
    AXISEMG_IN = 7  ' 急停信号映射到IN7

    ' 设置驱动器报警保护
    FOR i = 0 TO axis_count - 1
        ALM_IN(i) = 20 + i  ' 驱动器报警信号
        INVERT_IN(20 + i, ON)
    NEXT

    PRINT "安全保护设置完成"
END SUB
```

---

## 📚 总结

### 回零成功的三个关键判断条件

1. **`AXISSTATUS(axis) = 0`** - 轴状态无错误（最重要）
2. **`IDLE(axis) = -1`** - 轴已停止运动
3. **`ABS(DPOS(axis)) < 允许误差`** - 位置精度满足要求

### 标准回零代码模板

```basic
' 推荐的回零实现模板
GLOBAL SUB RecommendedHoming(axis_num)
    PRINT "开始轴", axis_num, "回零"

    BASE(axis_num)

    ' 1. 执行回零指令
    DATUM(3)  ' 使用模式3（最常用）

    ' 2. 等待回零完成
    WAIT UNTIL IDLE(axis_num) = -1
    DELAY(10)  ' 状态稳定延时

    ' 3. 检查回零结果
    IF AXISSTATUS(axis_num) = 0 THEN
        ' 4. 回零成功处理
        DPOS(axis_num) = 0      ' 清零脉冲位置
        MPOS(axis_num) = 0      ' 清零编码器位置
        PRINT "轴", axis_num, "回零成功！"
        RETURN 1
    ELSE
        ' 5. 回零失败处理
        PRINT "轴", axis_num, "回零失败！状态：", HEX(AXISSTATUS(axis_num))
        RETURN 0
    ENDIF
END SUB
```

### 开发建议

1. **从简单开始**：先实现单轴回零，再扩展到多轴
2. **充分测试**：在不同位置多次测试回零的重复性
3. **完善错误处理**：为各种异常情况提供明确的错误信息
4. **参数优化**：根据实际机械特性调整速度参数
5. **安全第一**：始终考虑安全保护措施

通过遵循本教程，您可以实现稳定、可靠、高精度的回零功能！

---

## 📖 附录：官方DATUM指令完整参考

### DATUM指令语法
```basic
DATUM(mode)                  ' 基本回零模式
DATUM(21, mode2)            ' EtherCAT/Rtex总线回零
DATUM(0) AXIS(axis_num)     ' 清除指定轴错误状态
```

### 官方回零模式完整列表

| 模式 | 官方描述 | 运动过程 | ATYPE要求 |
|------|----------|----------|-----------|
| **0** | 清除错误状态 | 清除所有轴的错误状态 | 通用 |
| **1** | Z信号正向回零 | CREEP正向→Z信号→DPOS清零 | ATYPE=4或7 |
| **2** | Z信号负向回零 | CREEP负向→Z信号→DPOS清零 | ATYPE=4或7 |
| **3** | 原点开关正向回零 | SPEED正向→原点→CREEP反向→离开→DPOS清零 | 通用 |
| **4** | 原点开关负向回零 | SPEED负向→原点→CREEP正向→离开→DPOS清零 | 通用 |
| **5** | 原点+Z信号正向 | SPEED正向→原点→CREEP反向→离开→CREEP反向→Z信号→DPOS清零 | ATYPE=4或7 |
| **6** | 原点+Z信号负向 | SPEED负向→原点→CREEP正向→离开→CREEP正向→Z信号→DPOS清零 | ATYPE=4或7 |
| **8** | 简单正向回零 | SPEED正向→原点→停止 | 通用 |
| **9** | 简单负向回零 | SPEED负向→原点→停止 | 通用 |
| **13** | 限位反找正向回零 | 模式3+限位反找功能 | 通用 |
| **21** | EtherCAT驱动器回零 | 使用驱动器内部回零功能 | 总线轴 |

### 控制器信号电平差异

| 控制器系列 | 触发电平 | 常开信号 | 常闭信号 | 示例 |
|------------|----------|----------|----------|------|
| **ZMC系列** | 0触发有效 | 需要INVERT_IN反转 | 不需要反转 | `INVERT_IN(8, ON)` |
| **ECI系列** | 1触发有效 | 不需要反转 | 需要INVERT_IN反转 | `INVERT_IN(8, ON)` |

### 重要注意事项

1. **Z信号回零要求**：必须配置为ATYPE=4或7
2. **指令限制**：DATUM指令后不能接绝对指令和mover指令
3. **多轴回零**：每个轴都需要单独使用DATUM指令
4. **自动清零**：DATUM指令成功后自动将DPOS重置为0并纠正MPOS
5. **总线控制器**：使用控制器找原点模式完成后，需要手动清零MPOS
6. **4系列特殊功能**：ATYPE=4时，模式100+n可自动清零MPOS

### Z信号回零官方示例代码

#### 示例1：直接Z信号回零（模式1）
```basic
BASE(0)
DPOS = 0
ATYPE = 4                   ' 必须：脉冲+编码器（Z信号回零要求）
SPEED = 100                 ' 找原点速度
CREEP = 10                  ' Z信号寻找速度（爬行速度）
FWD_IN = 6                  ' 正限位保护
INVERT_IN(6, ON)            ' 反转限位信号（ZMC控制器）
DATUM(1)                    ' 执行直接Z信号回零
```

#### 示例2：原点+Z信号回零（模式5，推荐）
```basic
BASE(0)
DPOS = 0
ATYPE = 4                   ' 必须：脉冲+编码器（Z信号回零要求）
SPEED = 100                 ' 找原点速度
CREEP = 10                  ' Z信号寻找速度
DATUM_IN = 5                ' 输入IN5作为原点开关
FWD_IN = 6                  ' 输入IN6作为正限位开关
INVERT_IN(5, ON)            ' 反转原点信号（ZMC控制器）
INVERT_IN(6, ON)            ' 反转限位信号
DATUM(5)                    ' 执行最高精度Z信号回零
```

#### 示例3：Z信号回零验证
```basic
' Z信号回零完整流程
BASE(0)
ATYPE = 4                   ' 必须设置为支持Z信号的轴类型

' 清除轴错误状态
DATUM(0) AXIS(0)
DELAY(10)

' 执行Z信号回零
DATUM(5)                    ' 使用最高精度模式
WAIT UNTIL IDLE(0) = -1
DELAY(10)

' 验证回零结果
IF AXISSTATUS(0) = 0 THEN
    PRINT "Z信号回零成功"
    PRINT "DPOS：", DPOS(0), "（已自动清零）"
    PRINT "MPOS：", MPOS(0), "（已自动纠正）"
    PRINT "精度：±0.25个编码器脉冲"
ELSE
    PRINT "Z信号回零失败，状态：", HEX(AXISSTATUS(0))
ENDIF
```

### Z信号回零标准代码模板

#### 模板1：直接Z信号回零（模式1）
```basic
' 基于官方DATUM指令的直接Z信号回零模板
BASE(axis_num)
ATYPE = 4                   ' 必须：脉冲+编码器（Z信号回零要求）
UNITS = 1000                ' 脉冲当量（建议1000以上）
SPEED = 100                 ' 回零速度
CREEP = 10                  ' Z信号寻找速度
FWD_IN = 11                 ' 正限位保护
INVERT_IN(11, ON)           ' ZMC系列限位信号反转

' 清除轴错误状态
DATUM(0) AXIS(axis_num)
DELAY(10)

' 执行直接Z信号回零
DATUM(1)                    ' 模式1：CREEP正向→Z信号→停止
WAIT UNTIL IDLE(axis_num) = -1
DELAY(10)

' 判断回零结果
IF AXISSTATUS(axis_num) = 0 THEN
    PRINT "Z信号回零成功，精度：±1个编码器脉冲"
    PRINT "DPOS：", DPOS(axis_num), "（已自动清零）"
    PRINT "MPOS：", MPOS(axis_num), "（已自动纠正）"
ELSE
    PRINT "Z信号回零失败，状态：", HEX(AXISSTATUS(axis_num))
ENDIF
```

#### 模板2：原点+Z信号回零（模式5，推荐）
```basic
' 基于官方DATUM指令的最高精度Z信号回零模板
BASE(axis_num)
ATYPE = 4                   ' 必须：脉冲+编码器（Z信号回零要求）
UNITS = 1000                ' 脉冲当量
SPEED = 100                 ' 回零速度
CREEP = 10                  ' Z信号寻找速度
DATUM_IN = 8                ' 原点开关输入
FWD_IN = 11                 ' 正限位保护
INVERT_IN(8, ON)            ' ZMC系列原点信号反转
INVERT_IN(11, ON)           ' ZMC系列限位信号反转

' 清除轴错误状态
DATUM(0) AXIS(axis_num)
DELAY(10)

' 执行最高精度Z信号回零
DATUM(5)                    ' 模式5：原点→离开→Z信号→停止
WAIT UNTIL IDLE(axis_num) = -1
DELAY(10)

' 判断回零结果
IF AXISSTATUS(axis_num) = 0 THEN
    PRINT "Z信号回零成功，精度：±0.25个编码器脉冲（最高精度）"
    PRINT "DPOS：", DPOS(axis_num), "（已自动清零）"
    PRINT "MPOS：", MPOS(axis_num), "（已自动纠正）"
ELSE
    PRINT "Z信号回零失败，状态：", HEX(AXISSTATUS(axis_num))
ENDIF
```

### Z信号回零关键要点总结

1. **ATYPE要求**：必须配置为ATYPE=4或7（带Z信号的轴类型）
2. **编码器要求**：必须使用带Z相输出的编码器
3. **精度优势**：模式1/2精度±1脉冲，模式5/6精度±0.25脉冲
4. **模式选择**：推荐使用模式5/6，安全性和精度都最高
5. **自动清零**：DATUM指令成功后自动清零DPOS并纠正MPOS

本教程专门针对Z信号回零，完全基于正运动官方DATUM指令手册编写，确保与官方规范完全一致。
