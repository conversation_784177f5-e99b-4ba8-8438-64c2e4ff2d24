'=============================================================================
' AB信号回零简化程序
' 适用于不带Z信号的编码器回零
' 避免系统函数名冲突的简化版本
'=============================================================================

' 全局变量
DIM total_axes           ' 轴数量
DIM ab_status(3)         ' 各轴AB回零状态

' 主程序
total_axes = 3
CALL InitABSystem()

PRINT "=== AB信号回零简化程序 ==="
PRINT "适用于不带Z信号的编码器"
PRINT ""
PRINT "操作说明："
PRINT "IN0 - 单轴AB回零（轴0）"
PRINT "IN1 - 多轴AB回零"
PRINT "IN2 - 状态检查"

WHILE 1
    ' 单轴AB回零
    IF SCAN_EVENT(IN(0)) > 0 THEN
        CALL SingleABHome(0)
    ENDIF
    
    ' 多轴AB回零
    IF SCAN_EVENT(IN(1)) > 0 THEN
        CALL MultiABHome()
    ENDIF
    
    ' 状态检查
    IF SCAN_EVENT(IN(2)) > 0 THEN
        CALL CheckABStatus()
    ENDIF
    
    DELAY(50)
WEND
END

' AB信号回零系统初始化
SUB InitABSystem()
    PRINT "初始化AB信号回零系统..."
    
    FOR i = 0 TO total_axes - 1
        BASE(i)
        ATYPE = 4                ' 脉冲方向输出+正交编码器输入
        UNITS = 1000             ' 脉冲当量
        SPEED = 200              ' 回零速度
        CREEP = 20               ' 爬行速度
        ACCEL = 1000             ' 加速度
        DECEL = 1000             ' 减速度
        HOMEWAIT = 20            ' 反找等待时间
        
        ' 信号映射
        DATUM_IN = 8 + i         ' 原点开关
        FWD_IN = 11 + i          ' 正限位
        REV_IN = 14 + i          ' 负限位
        
        ' 信号反转（ZMC系列OFF有效）
        INVERT_IN(8 + i, ON)
        INVERT_IN(11 + i, ON)
        INVERT_IN(14 + i, ON)
        
        ' 编码器设置
        ENCODER_RATIO(1, 1)      ' 1:1比例
        
        ab_status(i) = 0         ' 初始化为未回零
    NEXT
    
    PRINT "AB信号回零系统初始化完成"
    PRINT "ATYPE设置：4（脉冲+正交编码器）"
END SUB

' 单轴AB回零
SUB SingleABHome(axis_num)
    PRINT "=== 开始轴", axis_num, "AB信号回零 ==="
    
    BASE(axis_num)
    ab_status(axis_num) = 1      ' 设置为回零中
    
    ' 显示回零前状态
    PRINT "回零前状态："
    PRINT "  DPOS位置：", DPOS(axis_num)
    PRINT "  MPOS位置：", MPOS(axis_num)
    PRINT "  原点信号：", IN(8 + axis_num)
    
    ' 检查ATYPE设置
    IF ATYPE(axis_num) <> 4 AND ATYPE(axis_num) <> 5 THEN
        PRINT "警告：ATYPE设置可能不正确，当前值：", ATYPE(axis_num)
    ENDIF
    
    ' 执行AB信号回零（使用模式3）
    PRINT "执行回零指令 DATUM(3)..."
    DATUM(3)
    WAIT UNTIL IDLE(axis_num) = -1
    DELAY(10)                    ' 状态稳定延时
    
    ' 检查回零结果
    IF AXISSTATUS(axis_num) = 0 THEN
        ' 回零成功处理
        DPOS(axis_num) = 0       ' 清零脉冲位置
        MPOS(axis_num) = 0       ' 清零编码器位置
        ab_status(axis_num) = 2  ' 设置为已回零
        
        PRINT "✓ 轴", axis_num, "AB信号回零成功！"
        PRINT "回零后状态："
        PRINT "  DPOS位置：", DPOS(axis_num)
        PRINT "  MPOS位置：", MPOS(axis_num)
    ELSE
        ' 回零失败处理
        ab_status(axis_num) = 3  ' 设置为回零失败
        PRINT "✗ 轴", axis_num, "AB信号回零失败！"
        PRINT "错误状态：", HEX(AXISSTATUS(axis_num))
        CALL AnalyzeABError(axis_num)
    ENDIF
    
    PRINT "=== 轴", axis_num, "AB回零完成 ==="
END SUB

' 多轴AB回零
SUB MultiABHome()
    PRINT "=== 开始多轴AB信号回零 ==="
    
    FOR i = 0 TO total_axes - 1
        CALL SingleABHome(i)
        IF ab_status(i) <> 2 THEN
            PRINT "多轴AB回零失败，停止在轴", i
            RETURN
        ENDIF
        DELAY(500)  ' 轴间延时
    NEXT
    
    PRINT "✓ 所有轴AB信号回零成功！"
END SUB

' AB回零状态检查
SUB CheckABStatus()
    PRINT "=== AB信号回零状态检查 ==="
    
    FOR i = 0 TO total_axes - 1
        PRINT "轴", i, "详细状态："
        PRINT "  回零状态：", ab_status(i), "(0-未回零,1-回零中,2-已回零,3-失败)"
        PRINT "  ATYPE设置：", ATYPE(i)
        PRINT "  DPOS位置：", DPOS(i)
        PRINT "  MPOS位置：", MPOS(i)
        PRINT "  轴状态：", HEX(AXISSTATUS(i))
        PRINT "  编码器原始值：", ENCODER(i)
        PRINT "  原点信号：", IN(8 + i)
        
        ' 运动状态
        IF IDLE(i) = -1 THEN
            PRINT "  运动状态：停止"
        ELSE
            PRINT "  运动状态：运动中"
        ENDIF
        
        ' 编码器状态检查
        DIM enc_stat
        enc_stat = ENCODER_STATUS(i)
        PRINT "  编码器状态：", HEX(enc_stat)
        
        PRINT ""
    NEXT
END SUB

' AB回零错误分析
SUB AnalyzeABError(axis_num)
    DIM status_val, enc_stat
    status_val = AXISSTATUS(axis_num)
    enc_stat = ENCODER_STATUS(axis_num)
    
    PRINT "轴", axis_num, "AB回零错误分析："
    
    ' 轴状态分析
    IF status_val AND 1 THEN
        PRINT "  ✗ 轴错误标志置位"
    ENDIF
    
    IF status_val AND 2 THEN
        PRINT "  ✗ 碰到正向硬限位"
    ENDIF
    
    IF status_val AND 4 THEN
        PRINT "  ✗ 碰到负向硬限位"
    ENDIF
    
    IF status_val AND 8 THEN
        PRINT "  ✗ 急停信号有效"
    ENDIF
    
    IF status_val AND 16 THEN
        PRINT "  ✗ 驱动器报警"
    ENDIF
    
    ' 编码器信号分析
    PRINT "编码器AB信号诊断："
    PRINT "  ATYPE设置：", ATYPE(axis_num)
    PRINT "  编码器状态：", HEX(enc_stat)
    PRINT "  编码器原始值：", ENCODER(axis_num)
    PRINT "  MPOS位置：", MPOS(axis_num)
    
    ' 原点信号分析
    DIM home_sig
    home_sig = IN(8 + axis_num)
    PRINT "  原点信号状态：", home_sig
    
    PRINT "AB信号回零故障排除建议："
    PRINT "  1. 检查编码器AB信号连接"
    PRINT "  2. 检查编码器供电（5V或24V）"
    PRINT "  3. 检查原点开关连接"
    PRINT "  4. 确认ATYPE=4（正交编码器）"
    PRINT "  5. 使用屏蔽电缆，屏蔽层接地"
END SUB

' AB信号测试（简化版）
SUB TestABSignal(axis_num)
    PRINT "=== 轴", axis_num, "AB信号测试 ==="
    
    BASE(axis_num)
    
    PRINT "请手动转动编码器，观察信号变化..."
    FOR i = 1 TO 20
        DIM enc_val, mpos_val, enc_stat
        enc_val = ENCODER(axis_num)
        mpos_val = MPOS(axis_num)
        enc_stat = ENCODER_STATUS(axis_num)
        
        PRINT "ENCODER:", enc_val, " MPOS:", mpos_val, " STATUS:", HEX(enc_stat)
        DELAY(500)
    NEXT
    
    PRINT "AB信号测试完成"
END SUB

' 重复性测试（简化版）
SUB RepeatabilityTest(axis_num)
    PRINT "=== 轴", axis_num, "重复性测试 ==="
    
    DIM positions(5)
    DIM test_count
    test_count = 3
    
    FOR i = 1 TO test_count
        PRINT "第", i, "次回零测试"
        
        ' 移动到测试位置
        BASE(axis_num)
        MOVE(100)
        WAIT IDLE
        
        ' 执行回零
        DATUM(3)
        WAIT UNTIL IDLE(axis_num) = -1
        DELAY(10)
        
        IF AXISSTATUS(axis_num) = 0 THEN
            DPOS(axis_num) = 0
            MPOS(axis_num) = 0
            positions(i) = MPOS(axis_num)
            PRINT "回零位置：", positions(i)
        ELSE
            PRINT "第", i, "次回零失败"
            RETURN
        ENDIF
        
        DELAY(1000)
    NEXT
    
    PRINT "重复性测试完成"
END SUB
