===============================================================================
                螺丝机多线程优化版 - 统一打螺丝任务的高效架构
===============================================================================

【架构优化】
❌ 原方案：左右两个独立的打螺丝任务 → 代码重复
✅ 优化方案：统一打螺丝任务 → 避免代码重复，更高效

【优化后的多任务架构】
任务0：主控制任务（螺丝机多线程优化版.bas）
- 持续扫描输入信号（50ms周期）
- 队列管理和状态控制
- 双Y轴滑轨控制
- 任务调度和状态监控

任务1：回零任务（任务1_回零任务.bas）
- 独立执行四轴回零
- 完成后双Y轴回到用户位置
- 不阻塞主控制任务

任务2：统一打螺丝任务（任务2_打螺丝任务.bas）⭐ 核心优化
- 持续监控左右两侧队列
- 哪边需要就打哪边
- 避免代码重复
- 统一的运动控制逻辑

【文件列表】
1. 螺丝机多线程优化版.bas      - 主控制任务（任务0）⭐ 主程序
2. 任务1_回零任务.bas          - 回零任务
3. 任务2_打螺丝任务.bas        - 统一打螺丝任务 ⭐ 核心优化
4. 多线程优化版使用说明.txt    - 本说明文件

【启动方法】
1. 确保所有任务文件都已下载到控制器
2. 运行主控制任务：
   STOP
   RUN "螺丝机多线程优化版.bas"

【统一打螺丝任务工作原理】

=== 持续监控模式 ===
任务2启动后持续运行：
```
WHILE screw_task_stop = 0
    IF screwdriver_busy = 0 THEN        ' 电批空闲
        IF left_queue = 1 THEN          ' 左侧有任务
            执行左侧打螺丝
        ELSEIF right_queue = 1 THEN     ' 右侧有任务
            执行右侧打螺丝
        ENDIF
    ENDIF
    DELAY(100)                          ' 100ms检查周期
WEND
```

=== 统一处理逻辑 ===
左右两侧使用相同的打螺丝流程：
1. ExecuteLeftScrew() - 处理左侧螺丝
2. ExecuteRightScrew() - 处理右侧螺丝
3. WorkScrew() - 统一的打螺丝流程
4. 共享运动控制函数

【优势特点】

=== 代码优化 ===
✅ 避免左右两个相似任务文件
✅ 统一的运动控制逻辑
✅ 更易维护和扩展
✅ 减少代码冗余

=== 响应性能 ===
✅ 主控制任务50ms扫描周期
✅ 打螺丝任务100ms检查周期
✅ 按键立即响应，无延迟
✅ 急停立即生效

=== 资源利用 ===
✅ 电批资源统一调度
✅ 队列优先级管理
✅ 任务自动切换
✅ 更高的生产效率

【典型使用场景】

场景1：左侧优先处理
1. 左侧按键按下 → 左Y轴移动到工作位置
2. 打螺丝任务检测到left_queue=1 → 开始左侧作业
3. 右侧按键按下 → 右Y轴移动到工作位置，加入队列
4. 左侧完成 → 自动开始右侧作业

场景2：右侧优先处理
1. 右侧按键按下 → 右Y轴移动到工作位置
2. 打螺丝任务检测到right_queue=1 → 开始右侧作业
3. 左侧按键按下 → 左Y轴移动到工作位置，加入队列
4. 右侧完成 → 自动开始左侧作业

场景3：连续作业
1. 左侧完成后，Y轴自动回到用户位置
2. 用户放置新工件，再次按键
3. 系统立即响应，开始新的作业周期
4. 实现连续高效生产

【任务间通信】

=== 全局变量 ===
主控制任务 ↔ 打螺丝任务：
- left_queue, right_queue：队列状态
- screwdriver_busy：电批状态
- screw_task_stop：任务停止标志
- left_slide_status, right_slide_status：滑轨状态

主控制任务 ↔ 回零任务：
- sys_status：系统状态
- axis_home()：轴回零状态
- task_home_running：任务运行状态

=== 状态同步 ===
```
队列状态流转：
0 (无任务) → 1 (等待中) → 2 (执行中) → 0 (完成)

电批状态流转：
0 (空闲) → 1 (忙碌) → 0 (空闲)
```

【任务生命周期管理】

=== 任务启动 ===
- 主控制任务启动时自动启动打螺丝任务
- 回零任务按需启动（按下IN2时）
- 打螺丝任务持续运行，监控队列

=== 任务停止 ===
- 急停时：screw_task_stop = 1，打螺丝任务自动停止
- 异常时：主控制任务检测并重启打螺丝任务
- 正常时：任务持续运行，不会自动停止

=== 任务重启 ===
```basic
' 急停后重启打螺丝任务
screw_task_stop = 0
STOPTASK 2
RUNTASK 2, ScrewTask
task_screw_running = 1
```

【输入信号响应】
所有输入信号都由主控制任务处理：
- IN0：左侧开始（随时响应）
- IN1：右侧开始（随时响应）
- IN2：系统回零（随时响应）
- IN3：急停（随时响应）
- IN4：手动左Y轴到用户侧
- IN5：手动右Y轴到用户侧

【调试功能】
查看系统状态：
CALL ShowStatus()

显示内容包括：
- 系统状态和当前螺丝
- 双Y轴滑轨状态
- 队列状态和电批状态
- 任务运行状态（包含任务停止标志）
- 轴回零状态

手动控制：
CALL LeftSlideToWork()    ' 左Y轴到工作位置
CALL LeftSlideToUser()    ' 左Y轴到用户位置
CALL RightSlideToWork()   ' 右Y轴到工作位置
CALL RightSlideToUser()   ' 右Y轴到用户位置

【安全特性】

=== 急停处理 ===
1. 硬件急停：RAPIDSTOP(2)
2. 软件停止：screw_task_stop = 1
3. 状态清除：清除所有队列和状态
4. 任务重启：自动重启打螺丝任务

=== 异常恢复 ===
1. 任务异常检测：PROC_STATUS()监控
2. 自动重启：异常任务自动重启
3. 状态恢复：全局变量状态恢复
4. 队列保护：异常时清除队列状态

【性能参数】
- 主控制任务扫描周期：50ms
- 打螺丝任务检查周期：100ms
- 按键响应时间：<100ms
- 任务启动时间：<50ms
- 状态更新频率：20Hz
- 支持最大并发任务：3个

【注意事项】
1. 必须先运行主控制任务（螺丝机多线程优化版.bas）
2. 所有任务文件必须在同一目录下
3. 任务文件名不能修改
4. 打螺丝任务会自动启动并持续运行
5. 急停后打螺丝任务会自动重启

【故障排除】
1. 按键无响应
   - 检查主控制任务是否正在运行
   - 确认输入信号连接正确

2. 打螺丝不工作
   - 检查打螺丝任务是否运行：task_screw_running
   - 检查任务停止标志：screw_task_stop
   - 检查队列状态和滑轨位置

3. 任务异常
   - 调用ShowStatus()查看详细状态
   - 检查PROC_STATUS(1)和PROC_STATUS(2)
   - 确认全局变量值正确

【版本对比】
多线程版 → 多线程优化版：
+ 统一打螺丝任务，避免代码重复
+ 更高效的资源利用
+ 更简洁的架构设计
+ 更易维护和扩展
+ 减少文件数量（从5个减少到4个）
+ 统一的运动控制逻辑

===============================================================================
