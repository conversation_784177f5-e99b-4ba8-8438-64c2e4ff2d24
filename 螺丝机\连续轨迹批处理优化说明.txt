===============================================================================
                        连续轨迹批处理优化说明 - 彻底分离轨迹规划和停机控制
===============================================================================

【问题根源深度分析】

=== 三个"刹车指令"的罪魁祸首 ===
```
🚨 专家级分析发现的三个问题：

问题1：RAPIDSTOP(2)
位置：StandardThreeSegment 第一行
影响：清空运动缓冲并立即执行快速减速
结果：后续指令重新排队，速度必降为0

问题2：每段末尾 WAIT IDLE
位置：ThreeSegmentMove 末尾
影响：主动等所有轴停完，再发下一段
结果：等同于"编程上把轨迹切断"

问题3：反复 MERGE = OFF/ON
位置：StandardThreeSegment 结尾
影响：每关一次MERGE，控制器都会把前一段结尾减速到LSPEED（缺省0）
结果：破坏连续性，强制减速到0
```

=== 根本问题：轨迹规划和停机控制混淆 ===
```
错误的思路：
- 在每个三段轨迹内部设置参数
- 在每个三段轨迹内部等待完成
- 在每个三段轨迹内部开关MERGE

正确的思路：
- 把"让控制器自己做连续轨迹规划"和"不要在每一段里强制停机"彻底分离
- 一次设好前瞻/倒角参数，期间别反复改
- 让MERGE=ON从第一条MOVE持续到最后一条MOVE
- 控制器才能把所有指令视为一条长轨迹来做LOOKAHEAD
```

【解决方案：三阶段分离设计】

=== 阶段1：预设（只在整个批次开始时做一次）================
```basic
GLOBAL SUB BeginContinuousPath()
    BASE(0, 3)                      ' XZ两轴插补，X轴为主轴
    MERGE = ON                      ' 整个批次都保持连续插补
    CORNER_MODE = 32                ' 只倒角，不自动减速
    ZSMOOTH = 10                    ' 倒角半径放大到10mm，更平滑
    VP_MODE = 7, 7                  ' SS曲线，最平滑的曲线类型
    SRAMP = 150, 150                ' S曲线时间加长到150ms，更柔和
    FORCE_SPEED = 80                ' 统一行进速度80mm/s
END SUB

关键点：
✅ 一次设好所有参数，期间不再修改
✅ MERGE=ON开启后不再关闭（直到批次结束）
✅ 所有平滑参数都设置为最优值
✅ 无任何"刹车指令"
```

=== 阶段2：排指令（调用多次，不等待）================
```basic
GLOBAL SUB PushThreeSegment(start_x, start_z, end_x, end_z, start_safe_z, end_safe_z, std_mid_x, std_distance)
    '纯粹排指令，不做任何等待或参数修改
    
    '第一段：抬Z到起点安全高度
    MOVEABS(start_x, start_safe_z)
    
    '第二段：安全高度之间的圆弧插补
    IF std_distance >= 5 THEN
        MOVECIRC2ABS(std_mid_x, arc_top_height, end_x, end_safe_z)
    ELSE
        MOVEABS(end_x, end_safe_z)
    ENDIF
    
    '第三段：Z下降到目标位置
    MOVEABS(end_x, end_z)
    
    '不做任何等待，让控制器自行与下一条轨迹融合
END SUB

关键点：
✅ 没有BASE：始终沿用BeginContinuousPath()里设定的轴组
✅ 没有MERGE & CORNER_MODE：一次设好就别改
✅ 没有WAIT IDLE：让控制器自行把这一条与下一条融合
✅ 没有RAPIDSTOP：不清空缓冲区
✅ 纯粹的MOVE指令排队
```

=== 阶段3：收尾（批次结束时调用）================
```basic
GLOBAL SUB EndContinuousPath()
    '批次全部发送完毕后等待完成
    WAIT IDLE(0)                    ' 等待X轴完成
    WAIT IDLE(3)                    ' 等待Z轴完成
    MERGE = OFF                     ' 关闭连续插补
END SUB

关键点：
✅ 只在真正需要停机时才WAIT IDLE
✅ 只在批次结束时才MERGE = OFF
✅ 确保整个批次的连续性
```

【技术细节深度解析】

=== 参数优化升级 ===
```basic
ZSMOOTH = 10：
- 从5mm增加到10mm
- 倒角半径再放大一点
- 更平滑的拐角过渡
- 减少速度波动

SRAMP = 150, 150：
- 从100ms增加到150ms
- S曲线时间加长一点
- 更柔和的加减速
- 进一步减少冲击

VP_MODE = 7, 7：
- 保持SS曲线
- 最平滑的曲线类型
- 与连续轨迹完美配合

CORNER_MODE = 32：
- 只倒角，不自动减速
- 避免90度角触发停车
- 保持连续性
```

=== 连续轨迹的工作原理 ===
```
控制器的LOOKAHEAD机制：
1. 接收到多条MOVE指令
2. 分析整个轨迹的几何形状
3. 计算最优的速度分配
4. 生成连续的速度曲线
5. 执行平滑的运动

破坏LOOKAHEAD的操作：
❌ RAPIDSTOP：清空缓冲区，重新开始
❌ WAIT IDLE：等待停止，切断轨迹
❌ MERGE OFF/ON：关闭连续性，强制减速
❌ 修改BASE/CORNER_MODE：改变规划参数

保持LOOKAHEAD的操作：
✅ 连续发送MOVE指令
✅ 保持参数不变
✅ 让MERGE持续开启
✅ 不做任何等待
```

【应用示例】

=== 单个螺丝的连续轨迹 ===
```basic
'开始连续轨迹批处理
CALL BeginContinuousPath()

'去程：从取螺丝位置到螺丝孔
CALL PushThreeSegment(pick_x, pick_z, target_x, target_z, pick_safe_height, work_safe_height)

'回程：从螺丝孔到取螺丝位置
CALL PushThreeSegment(target_x, target_z, pick_x, pick_z, work_safe_height, pick_safe_height)

'结束连续轨迹批处理
CALL EndContinuousPath()

效果：
✅ 去程三段完全连续
✅ 去程和回程之间连续
✅ 回程三段完全连续
✅ 整个过程一条"长蛇形"轨迹
✅ 只在最后才停止
```

=== 多个螺丝的连续轨迹 ===
```basic
'开始连续轨迹批处理
CALL BeginContinuousPath()

'第一个螺丝
CALL PushThreeSegment(pick_x, pick_z, target1_x, target1_z, pick_safe_height, work_safe_height)
CALL PushThreeSegment(target1_x, target1_z, pick_x, pick_z, work_safe_height, pick_safe_height)

'第二个螺丝
CALL PushThreeSegment(pick_x, pick_z, target2_x, target2_z, pick_safe_height, work_safe_height)
CALL PushThreeSegment(target2_x, target2_z, pick_x, pick_z, work_safe_height, pick_safe_height)

'第三个螺丝
CALL PushThreeSegment(pick_x, pick_z, target3_x, target3_z, pick_safe_height, work_safe_height)
CALL PushThreeSegment(target3_x, target3_z, pick_x, pick_z, work_safe_height, pick_safe_height)

'结束连续轨迹批处理
CALL EndContinuousPath()

效果：
✅ 整批螺丝孔之间完全连续
✅ 每个三段内部完全连续
✅ 一条超长的"蛇形"轨迹
✅ 效率最高，冲击最小
```

【预期效果】

=== 速度曲线特征 ===
```
连续轨迹批处理的速度曲线：
速度
 ^
 |  /~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\
 | /                                  \
 |/                                    \
 +---|---|---|---|---|---|---|---|---|---> 时间
    螺丝1 螺丝2 螺丝3 螺丝4 螺丝5 螺丝6
    去回  去回  去回  去回  去回  去回

特点：
✅ 整个批次一条连续的速度曲线
✅ 无任何中间停顿或减速到0
✅ 只在开始加速，结束减速
✅ 中间保持恒速或轻微波动
✅ 机械冲击降到最低
```

=== 与之前的对比 ===
```
之前的分段式（每段都停）：
速度
 ^
 |/\  /\  /\  /\  /\  /\
 |  \/  \/  \/  \/  \/  \
 +---|---|---|---|---|---|---> 时间
    停  停  停  停  停  停

问题：
❌ 每段都有停顿
❌ 频繁的启停冲击
❌ 效率低下
❌ 机械磨损大

现在的连续式（批处理）：
速度
 ^
 |  /~~~~~~~~~~~~~~~~\
 | /                  \
 |/                    \
 +---|---|---|---|---|---> 时间
    连续 连续 连续 连续

优势：
✅ 完全连续，无停顿
✅ 机械冲击最小
✅ 效率最高
✅ 设备寿命最长
```

【进一步优化建议】

=== 可选的更平滑设置 ===
```basic
让拐角稍微减速但不停：
CORNER_MODE = 2 + 32            ' 倒角+自动减速
DECEL_ANGLE = 25 * (PI/180)    ' 开始减速角度25度
STOP_ANGLE = 80 * (PI/180)     ' 最低速角度80度

倒角半径再顺滑：
ZSMOOTH = 15                    ' 增加到15mm

整体更柔和：
SRAMP = 200, 200                ' 增加到200ms
```

=== 监控和验证 ===
```
示波器监控：
- 观察MSPEED(0)、MSPEED(3)曲线
- 确认拐角不掉到0
- 只出现轻微坡谷即可
- 整体保持连续性

机械观察：
- 听取运动声音，应该极其平滑
- 观察是否有任何停顿
- 检查机械振动情况
- 验证定位精度
```

【兼容性设计】

=== 兼容旧接口 ===
```basic
GLOBAL SUB StandardThreeSegment(...)
    '兼容旧接口，内部使用新的连续轨迹方式
    CALL BeginContinuousPath()      ' 开始连续轨迹
    CALL PushThreeSegment(...)      ' 排入轨迹
    CALL EndContinuousPath()        ' 结束连续轨迹
END SUB

优势：
✅ 旧代码无需修改
✅ 自动获得连续轨迹优化
✅ 向后兼容
✅ 渐进式升级
```

=== 新接口的使用 ===
```basic
推荐的新用法：
CALL BeginContinuousPath()      ' 批次开始
CALL PushThreeSegment(...)      ' 排入多个轨迹
CALL PushThreeSegment(...)
CALL PushThreeSegment(...)
CALL EndContinuousPath()        ' 批次结束

优势：
✅ 最大化连续性
✅ 最高效率
✅ 最小冲击
✅ 最佳性能
```

【总结】

连续轨迹批处理优化的关键：
✅ **三阶段分离**：预设→排指令→收尾，彻底分离轨迹规划和停机控制
✅ **去除刹车指令**：消除RAPIDSTOP、WAIT IDLE、MERGE OFF/ON的干扰
✅ **一次设参数**：BeginContinuousPath()一次设好，期间不再修改
✅ **纯粹排指令**：PushThreeSegment()只排MOVE指令，不做任何等待
✅ **批次化处理**：让控制器把整个批次视为一条长轨迹做LOOKAHEAD
✅ **参数优化**：ZSMOOTH=10、SRAMP=150，更平滑的设置

这个优化实现了真正的"长蛇形"连续轨迹，
整个批次螺丝孔之间以及三段内部都维持无间断的速度曲线，
只有在真正需要停机时才完全停下，速度曲线极其平滑。

===============================================================================
