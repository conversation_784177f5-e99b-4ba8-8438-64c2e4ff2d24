===============================================================================
                        绝对值安全高度说明 - 所有高度参数都使用绝对坐标值
===============================================================================

【绝对值安全高度概念】

=== 什么是绝对值安全高度 ===
```
绝对值安全高度：
- 所有Z轴高度参数都是相对于机械原点的绝对坐标值
- 不是相对于工件表面或其他参考点的偏移量
- 直接使用MOVEABS指令移动到指定的绝对位置
- 简化计算，避免相对值转换的复杂性和错误
```

=== 与相对值的区别 ===
```
相对值方式（之前可能的方式）：
- 安全高度 = 工件高度 + 安全偏移量
- 需要实时计算：actual_height = work_height - safe_offset
- 容易出错，需要考虑不同工件的高度差异

绝对值方式（当前方式）：
- 安全高度 = 固定的绝对坐标值
- 直接使用：MOVEABS(safe_height)
- 简单可靠，所有位置都是预设的绝对值
```

【当前绝对值高度设置】

=== 四个关键高度参数 ===
```basic
GLOBAL screw_work_height = 30       ' 打螺丝工作高度30mm（绝对值）
GLOBAL pick_safe_height = 8         ' 取螺丝位置安全高度8mm（绝对值）
GLOBAL work_safe_height = 25        ' 工件位置安全高度25mm（绝对值）
GLOBAL arc_top_height = 30          ' 圆弧插补最高点30mm（绝对值）
```

=== 高度关系图 ===
```
Z轴坐标系（往下为正）：
0mm    ← 机械原点
│
8mm    ← pick_safe_height（取螺丝安全高度）
│
10mm   ← pick_z（取螺丝位置）
│
25mm   ← work_safe_height（工件安全高度）
│
30mm   ← screw_work_height（打螺丝工作高度）
│
       ← arc_top_height = 30mm（圆弧最高点，应该调整为更小值）
```

=== 高度设置原则 ===
```
设置原则：
1. arc_top_height < pick_safe_height < work_safe_height < screw_work_height
2. 所有高度都必须 > 0
3. 圆弧最高点必须高于（小于）所有安全高度
4. 安全高度必须确保无碰撞

建议调整：
arc_top_height = 5               ' 调整为5mm，确保高于所有安全高度
```

【绝对值安全高度的使用】

=== 三段轨迹中的使用 ===
```basic
从取螺丝位置(50,10)到螺丝孔位(100,30)：

第一段：抬Z到起点安全高度
MOVEABS(50, 8)                  ' 移动到绝对坐标(50,8)

第二段：圆弧插补到终点安全高度
MOVECIRC2ABS(75, 5, 100, 25)    ' 圆弧到绝对坐标(100,25)

第三段：Z下降到目标位置
MOVEABS(100, 30)                ' 移动到绝对坐标(100,30)
```

=== 函数调用中的传递 ===
```basic
'从取螺丝位置到螺丝孔位
start_safe = pick_safe_height   ' 8mm（绝对值）
end_safe = work_safe_height     ' 25mm（绝对值）
CALL ThreeSegmentMove(pick_x, pick_z, target_x, target_z, start_safe, end_safe)

'从螺丝孔位回到取螺丝位置
start_safe = work_safe_height   ' 25mm（绝对值）
end_safe = pick_safe_height     ' 8mm（绝对值）
CALL ThreeSegmentMove(current_x, current_z, pick_x, pick_z, start_safe, end_safe)
```

【绝对值的优势】

=== 简化计算 ===
```
✅ 无需实时计算相对偏移
✅ 直接使用MOVEABS指令
✅ 避免相对值转换错误
✅ 代码逻辑更清晰
```

=== 提高可靠性 ===
```
✅ 所有位置都是预设的固定值
✅ 不受工件高度变化影响
✅ 减少计算错误的可能性
✅ 便于调试和验证
```

=== 便于维护 ===
```
✅ 参数设置直观明了
✅ 高度关系一目了然
✅ 调整参数简单直接
✅ 便于标定和校准
```

【实际应用示例】

=== 完整作业流程的高度变化 ===
```
1. 系统启动后初始位置：
   Z = 8mm（pick_safe_height，取螺丝安全高度）

2. 吸取螺丝：
   Z = 10mm（pick_z，取螺丝位置）

3. 三段轨迹到螺丝孔位：
   第一段：Z = 8mm（pick_safe_height，抬升到安全高度）
   第二段：Z = 8mm → 25mm（圆弧插补到work_safe_height）
   第三段：Z = 30mm（screw_work_height，打螺丝工作位置）

4. 打螺丝：
   Z = 30mm（保持在工作位置，不移动）

5. 三段轨迹回取螺丝位置：
   第一段：Z = 25mm（work_safe_height，抬升到安全高度）
   第二段：Z = 25mm → 8mm（圆弧插补到pick_safe_height）
   第三段：Z = 10mm（pick_z，取螺丝位置）
```

=== 16个螺丝的高度一致性 ===
```
所有螺丝使用相同的绝对高度：
- 取螺丝安全高度：始终8mm
- 工件安全高度：始终25mm
- 打螺丝工作高度：始终30mm
- 圆弧最高点：始终5mm（建议调整值）

优势：
✅ 所有螺丝的轨迹高度完全一致
✅ 无需考虑不同螺丝位置的高度差异
✅ 简化了多螺丝作业的复杂性
```

【参数调整指南】

=== 安全高度调整原则 ===
```basic
pick_safe_height = 8            ' 取螺丝安全高度

调整考虑：
- 必须高于取螺丝器表面
- 确保吸嘴不会碰撞
- 留有足够的安全余量
- 不要设置过高，影响效率

调整方法：
1. 手动移动Z轴到合适高度
2. 记录绝对坐标值
3. 修改pick_safe_height参数
4. 重新测试验证
```

=== 工件安全高度调整 ===
```basic
work_safe_height = 25           ' 工件安全高度

调整考虑：
- 必须高于工件表面和螺丝头
- 确保螺丝机头不会碰撞
- 考虑工件夹具的高度
- 平衡安全性和效率

调整方法：
1. 测量工件最高点
2. 加上5-10mm安全余量
3. 设置为绝对坐标值
4. 验证所有螺丝位置都安全
```

=== 圆弧最高点调整 ===
```basic
arc_top_height = 30             ' 当前设置（需要调整）

问题：当前30mm > work_safe_height(25mm)，违反了高度关系

建议调整：
arc_top_height = 5              ' 调整为5mm

调整原因：
- 圆弧最高点应该是最高的（最小的Z值）
- 确保圆弧轨迹不会碰撞任何障碍物
- 5mm高于取螺丝安全高度8mm，符合要求
```

【高度验证方法】

=== 启动时验证 ===
```
系统启动时会显示：
"安全高度设置（绝对值）：取螺丝=8mm，工件=25mm，圆弧最高点=30mm"

检查要点：
1. 所有高度都是正值
2. 高度关系是否合理
3. 圆弧最高点是否正确
```

=== 运行时验证 ===
```
观察三段轨迹的高度变化：
"第一段：抬Z到安全高度8mm（绝对值）"
"第二段：安全高度间圆弧插补"
"第三段：Z下降到目标位置30mm（绝对值）"

验证要点：
1. 各段高度是否符合预期
2. 运动是否平滑连续
3. 是否有碰撞风险
```

=== 手动验证方法 ===
```
手动测试各个高度位置：
1. 手动移动到pick_safe_height(8mm)，检查是否安全
2. 手动移动到work_safe_height(25mm)，检查是否安全
3. 手动移动到arc_top_height(5mm)，检查是否为最高点
4. 验证所有位置都无碰撞风险
```

【常见问题和解决方案】

=== 问题1：圆弧最高点设置错误 ===
```
现象：圆弧轨迹异常或碰撞
原因：arc_top_height设置不当
解决：调整arc_top_height = 5mm，确保为最高点
```

=== 问题2：安全高度不够安全 ===
```
现象：运动过程中有碰撞
原因：安全高度设置过低
解决：增加安全高度的绝对值，确保足够间隙
```

=== 问题3：高度关系混乱 ===
```
现象：运动逻辑不合理
原因：高度参数关系设置错误
解决：按照正确的高度关系重新设置所有参数
```

=== 问题4：相对值和绝对值混用 ===
```
现象：计算错误或运动异常
原因：代码中混用了相对值计算
解决：确保所有高度参数都使用绝对值
```

【总结】

绝对值安全高度系统特点：
✅ **简化计算**：所有高度都是绝对坐标值，无需转换
✅ **提高可靠性**：避免相对值计算错误
✅ **便于维护**：参数设置直观，调整简单
✅ **确保一致性**：所有螺丝使用相同的绝对高度
✅ **便于调试**：高度关系清晰，问题容易定位
✅ **标准化操作**：所有运动指令都使用MOVEABS

建议立即调整：
⚠️ **arc_top_height = 5**：从30mm调整为5mm，确保正确的高度关系

这套绝对值安全高度系统为螺丝机提供了简单、可靠、易维护的
高度控制方案，确保所有运动都安全、准确、一致。

===============================================================================
