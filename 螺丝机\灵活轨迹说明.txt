===============================================================================
                        灵活轨迹说明 - 根据安全高度值自动选择轨迹类型
===============================================================================

【灵活轨迹原理】

=== 设计理念 ===
不使用额外的开关变量，直接根据安全高度变量的值来判断是否使用安全高度：
✅ **pick_safe_height = 0**：跳过从取螺丝位置的抬Z动作
✅ **work_safe_height = 0**：跳过从工件位置的抬Z动作
✅ **灵活组合**：可能都用，都不用，或一端用另一端不用
✅ **自动选择**：系统根据安全高度值自动选择最合适的轨迹类型

=== 四种轨迹模式 ===
```
模式1：两端都用安全高度（pick_safe_height≠0, work_safe_height≠0）
轨迹：抬Z → 圆弧 → Z下降（标准三段轨迹）

模式2：两端都不用安全高度（pick_safe_height=0, work_safe_height=0）
轨迹：直接圆弧插补（跳过所有抬Z和Z下降）

模式3：起点用安全高度，终点不用（pick_safe_height≠0, work_safe_height=0）
轨迹：抬Z → 圆弧直达终点（两段轨迹）

模式4：起点不用安全高度，终点用（pick_safe_height=0, work_safe_height≠0）
轨迹：圆弧到安全高度 → Z下降（两段轨迹）
```

【安全高度参数设置】

=== 当前默认设置 ===
```basic
pick_safe_height = 8            ' 取螺丝位置安全高度8mm
work_safe_height = 25           ' 工件位置安全高度25mm
arc_top_height = 20             ' 圆弧最高点20mm
```

=== 灵活调整示例 ===
```basic
' 示例1：标准三段轨迹
pick_safe_height = 8
work_safe_height = 25
结果：抬Z(8mm) → 圆弧 → Z下降(25mm)

' 示例2：纯圆弧插补
pick_safe_height = 0
work_safe_height = 0
结果：直接圆弧插补，无抬Z和Z下降

' 示例3：只在起点抬Z
pick_safe_height = 5
work_safe_height = 0
结果：抬Z(5mm) → 圆弧直达终点

' 示例4：只在终点下降
pick_safe_height = 0
work_safe_height = 20
结果：圆弧到安全高度 → Z下降(20mm)
```

【四种轨迹模式详解】

=== 模式1：标准三段轨迹 ===
```
条件：pick_safe_height≠0 AND work_safe_height≠0
示例：从取螺丝位置(50,10)到螺丝孔位(100,30)

第一段：抬Z到起点安全高度
MOVEABS(50, 8)                  ' 从(50,10)抬升到(50,8)

第二段：安全高度间圆弧插补
MOVECIRC2ABS(75, 20, 100, 25)   ' 从(50,8)圆弧到(100,25)

第三段：Z下降到目标位置
MOVEABS(100, 30)                ' 从(100,25)下降到(100,30)
```

=== 模式2：纯圆弧插补 ===
```
条件：pick_safe_height=0 AND work_safe_height=0
示例：从取螺丝位置(50,10)到螺丝孔位(100,30)

单段：直接圆弧插补
MOVECIRC2ABS(75, 20, 100, 30)   ' 从(50,10)直接圆弧到(100,30)

优势：最简洁，最快速，适合无障碍环境
```

=== 模式3：起点抬Z，终点直达 ===
```
条件：pick_safe_height≠0 AND work_safe_height=0
示例：从取螺丝位置(50,10)到螺丝孔位(100,30)

第一段：抬Z到起点安全高度
MOVEABS(50, 8)                  ' 从(50,10)抬升到(50,8)

第二段：从安全高度圆弧直达终点
MOVECIRC2ABS(75, 20, 100, 30)   ' 从(50,8)圆弧到(100,30)

适用：起点有障碍，终点无障碍
```

=== 模式4：起点直达，终点下降 ===
```
条件：pick_safe_height=0 AND work_safe_height≠0
示例：从取螺丝位置(50,10)到螺丝孔位(100,30)

第一段：从起点圆弧到终点安全高度
MOVECIRC2ABS(75, 20, 100, 25)   ' 从(50,10)圆弧到(100,25)

第二段：Z下降到目标位置
MOVEABS(100, 30)                ' 从(100,25)下降到(100,30)

适用：起点无障碍，终点有障碍
```

【实际应用场景】

=== 场景1：标准生产环境 ===
```basic
pick_safe_height = 8            ' 取螺丝区域有夹具，需要抬升
work_safe_height = 25           ' 工件区域有突起，需要安全高度
结果：标准三段轨迹，最安全
```

=== 场景2：高速生产环境 ===
```basic
pick_safe_height = 0            ' 取螺丝区域平整，无障碍
work_safe_height = 0            ' 工件区域平整，无障碍
结果：纯圆弧插补，最高效
```

=== 场景3：混合环境A ===
```basic
pick_safe_height = 5            ' 取螺丝区域有轻微障碍
work_safe_height = 0            ' 工件区域平整
结果：起点抬Z，终点直达
```

=== 场景4：混合环境B ===
```basic
pick_safe_height = 0            ' 取螺丝区域平整
work_safe_height = 20           ' 工件区域有障碍
结果：起点直达，终点下降
```

【核心函数详解】

=== FlexibleTrajectory() - 灵活轨迹选择器 ===
```basic
功能：根据安全高度值自动选择最合适的轨迹类型
算法：
1. 分析安全高度设置
2. 判断四种模式
3. 调用对应的轨迹函数

特点：
- 无需额外开关变量
- 自动智能选择
- 支持所有组合
```

=== 四个专用轨迹函数 ===
```basic
DirectArcMove()           ' 纯圆弧插补
StandardThreeSegment()    ' 标准三段轨迹
TwoSegmentStartSafe()     ' 起点安全高度
TwoSegmentEndSafe()       ' 终点安全高度
```

【性能对比】

=== 运动时间对比 ===
```
纯圆弧插补（模式2）：
- 时间：0.10秒
- 效率：最高

两段轨迹（模式3/4）：
- 时间：0.15秒
- 效率：中等

三段轨迹（模式1）：
- 时间：0.20秒
- 效率：较低，但最安全
```

=== 安全性对比 ===
```
三段轨迹：安全性最高，适合复杂环境
两段轨迹：安全性中等，适合混合环境
纯圆弧：安全性依赖环境，适合简单环境
```

【参数调优指南】

=== 安全高度设置原则 ===
```basic
pick_safe_height设置：
- 0：取螺丝区域完全平整，无任何障碍
- 2-5mm：有轻微障碍或夹具
- 5-10mm：有明显障碍，需要足够间隙

work_safe_height设置：
- 0：工件表面完全平整，无突起
- 15-20mm：有轻微突起或螺丝头
- 20-30mm：有明显突起，需要足够间隙
```

=== 圆弧最高点设置 ===
```basic
arc_top_height = 20             ' 圆弧最高点

调整原则：
- 必须高于所有安全高度
- 不能太高，影响效率
- 推荐：比最高安全高度高5-10mm
```

=== 距离判断阈值 ===
```basic
IF distance >= 5 THEN           ' 圆弧/直线判断阈值

调整原则：
- 太小：很小距离也用圆弧，效率低
- 太大：较大距离用直线，失去圆弧优势
- 推荐：3-8mm之间
```

【调试和测试】

=== 测试不同模式 ===
```basic
' 测试模式1：标准三段
pick_safe_height = 8
work_safe_height = 25

' 测试模式2：纯圆弧
pick_safe_height = 0
work_safe_height = 0

' 测试模式3：起点安全
pick_safe_height = 5
work_safe_height = 0

' 测试模式4：终点安全
pick_safe_height = 0
work_safe_height = 20
```

=== 观察要点 ===
✅ **轨迹选择**：确认系统选择了正确的轨迹模式
✅ **运动连续性**：检查所有模式的连续性
✅ **碰撞检查**：验证安全高度设置的有效性
✅ **效率评估**：对比不同模式的运动时间

【常见问题解答】

=== Q1：如何选择最合适的模式？ ===
A：根据实际环境：有障碍用安全高度，无障碍设为0

=== Q2：可以动态调整安全高度吗？ ===
A：可以，修改pick_safe_height和work_safe_height即可

=== Q3：圆弧最高点会自动调整吗？ ===
A：不会，arc_top_height是固定值，需要手动调整

=== Q4：纯圆弧模式安全吗？ ===
A：在无障碍环境下安全，但需要确认环境条件

=== Q5：两段轨迹的效果如何？ ===
A：介于三段和纯圆弧之间，适合混合环境

【总结】

灵活轨迹系统特点：
✅ **智能选择**：根据安全高度值自动选择轨迹类型
✅ **四种模式**：标准三段、纯圆弧、两种两段轨迹
✅ **无需开关**：直接使用安全高度变量，简化配置
✅ **高度灵活**：可能都用，都不用，或一端用另一端不用
✅ **性能优化**：根据环境选择最合适的轨迹，平衡安全和效率

这套灵活轨迹系统为螺丝机提供了最大的适应性和配置灵活性，
能够根据实际生产环境自动选择最合适的运动轨迹。

===============================================================================
