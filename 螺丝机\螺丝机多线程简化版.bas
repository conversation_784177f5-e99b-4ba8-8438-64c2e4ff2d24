'=============================================================================
' 螺丝机多线程简化版（主控制任务）
' 简化的多任务架构，避免函数名冲突：
' 任务0：主控制任务（本文件，包含所有共享函数）
' 任务1：回零任务（仅包含回零逻辑）
' 任务2：打螺丝任务（仅包含打螺丝逻辑）
'=============================================================================

'全局变量定义
GLOBAL sys_status                  ' 系统状态：0-待机，1-运行中，2-回零中
GLOBAL axis_home(5)                ' 各轴回零状态：0-未回零，1-回零中，2-已回零，3-失败（索引0-4）
GLOBAL left_screw_num = 8          ' 左侧螺丝点位数量（默认8个，最多64个）
GLOBAL right_screw_num = 8         ' 右侧螺丝点位数量（默认8个，最多64个）
GLOBAL max_screw_num = 64          ' 单侧最大螺丝数量
GLOBAL cur_screw                   ' 当前打螺丝序号

'双Y轴滑轨控制变量
GLOBAL left_user_pos = 50           ' 左侧用户位置（靠近用户侧）
GLOBAL left_work_pos = 80           ' 左侧工作位置（第一个螺丝Y坐标）
GLOBAL right_user_pos = 50         ' 右侧用户位置（靠近用户侧）
GLOBAL right_work_pos = 80         ' 右侧工作位置（第一个螺丝Y坐标）

GLOBAL left_slide_status = 0        ' 左侧滑轨状态：0-用户侧，1-工作侧，2-移动中
GLOBAL right_slide_status = 0       ' 右侧滑轨状态：0-用户侧，1-工作侧，2-移动中

'队列控制变量
GLOBAL left_queue = 0               ' 左侧队列状态：0-无任务，1-等待中，2-执行中
GLOBAL right_queue = 0              ' 右侧队列状态：0-无任务，1-等待中，2-执行中
GLOBAL screwdriver_busy = 0         ' 电批忙碌状态：0-空闲，1-忙碌

'任务状态变量
GLOBAL task_home_running = 0        ' 回零任务运行状态
GLOBAL task_screw_running = 0       ' 打螺丝任务运行状态

'打螺丝任务控制变量
GLOBAL screw_task_stop = 0          ' 打螺丝任务停止标志：0-继续运行，1-停止

'螺丝位置数据存储（每个螺丝3个数据：X,Y,Z）
GLOBAL left_start = 0               ' 左侧螺丝位置数据起始地址（0-191）
GLOBAL right_start = 200            ' 右侧螺丝位置数据起始地址（200-391）
'数据布局：64个螺丝 × 3个坐标 = 192个数据位置

'吸螺丝位置
GLOBAL pick_x = 50                  ' 吸螺丝位置X
GLOBAL pick_y = 150                 ' 吸螺丝位置Y
GLOBAL pick_z = 10                  ' 吸螺丝位置Z（往下为正）

'Z轴高度设置（注意：安全高度必须大于0，系统强制使用三段插补）
GLOBAL screw_work_height = 30       ' 打螺丝工作高度30mm
GLOBAL pick_safe_height = 8         ' 吸螺丝位置安全高度8mm（必须>0）
GLOBAL work_safe_height = 25        ' 工件位置安全高度25mm（必须>0）
GLOBAL arc_top_height = 20          ' 圆弧插补最高点Z轴高度20mm

'主程序（任务0）
CALL InitSystem()
CALL SetupAxis()
CALL SetupData()

PRINT "=== 螺丝机多线程简化版启动 ==="
PRINT "简化的多任务架构："
PRINT "任务0：主控制（输入扫描、状态管理、所有共享函数）"
PRINT "任务1：回零任务（仅回零逻辑）"
PRINT "任务2：打螺丝任务（仅打螺丝逻辑）"
PRINT ""
PRINT "操作说明："
PRINT "IN0 - 左侧开始（可在任何时候按下）"
PRINT "IN1 - 右侧开始（可在任何时候按下）"
PRINT "IN2 - 系统回零"
PRINT "IN3 - 急停"
PRINT "IN4 - 手动左Y轴到用户侧"
PRINT "IN5 - 手动右Y轴到用户侧"

'启动打螺丝任务（持续运行）
PRINT "启动打螺丝任务..."
screw_task_stop = 0
RUNTASK 2, SimpleScrewTask
task_screw_running = 1

'主循环（任务0持续运行）
WHILE 1
    CALL ScanInput()                ' 扫描输入信号
    CALL UpdateTaskStatus()         ' 更新任务状态
    CALL UpdateStatus()             ' 更新系统状态
    DELAY(50)                       ' 50ms扫描周期
WEND
END

'================ 系统初始化 ================
GLOBAL SUB InitSystem()
    sys_status = 0
    left_screw_num = 8              ' 默认左侧8个螺丝
    right_screw_num = 8             ' 默认右侧8个螺丝
    cur_screw = 0
    
    '初始化回零状态
    FOR i = 0 TO 3
        axis_home(i) = 0
    NEXT
    
    '初始化队列状态
    left_queue = 0
    right_queue = 0
    screwdriver_busy = 0
    left_slide_status = 0
    right_slide_status = 0
    
    '初始化任务状态
    task_home_running = 0
    task_screw_running = 0
    screw_task_stop = 0
    
    PRINT "系统初始化完成"
END SUB

'================ 轴参数设置 ================
GLOBAL SUB SetupAxis()
    '四轴配置：X轴(0), Y1轴(1), Y2轴(2), Z轴(3)
    BASE(0, 1, 2, 3)
    ATYPE = 1, 1, 1, 1              ' 步进电机开环脉冲控制
    UNITS = 1000, 100, 100, 1000    ' Y轴脉冲当量100脉冲/mm（10mm螺距）
    SPEED = 1000, 1000, 1000, 500   ' 运动速度1000mm/s (1m/s)，Z轴500mm/s
    ACCEL = 1000, 1000, 1000, 1000  ' 加速度1000mm/s² (1m/s²)
    DECEL = 1000, 1000, 1000, 1000  ' 减速度1000mm/s² (1m/s²)
    CREEP = 10, 10, 10, 5           ' 回零爬行速度
    
    '限位和原点信号配置
    FWD_IN = 8, 9, 10, 11           ' 正限位 IN8-IN11
    REV_IN = 12, 13, 14, 15         ' 负限位 IN12-IN15
    DATUM_IN = 16, 17, 18, 19       ' 原点开关 IN16-IN19
    
    '信号反转(根据实际硬件调整)
    FOR i = 8 TO 19
        INVERT_IN(i, ON)            ' 常开信号反转
    NEXT
    
    '连续插补设置
    MERGE = ON                      ' 开启连续插补
    CORNER_MODE = 2                 ' 拐角减速模式
    '注意：LOOKAHEAD和BLEND_TOL可能不是所有控制器都支持

    'S型曲线设置（平滑运动曲线）
    SRAMP = 100                     ' S型曲线平滑时间100ms（0-250ms范围）

    '圆弧插补设置（如果控制器支持）
    '注意：ARC_MODE和ARC_RADIUS可能不是所有控制器都支持
    'ARC_MODE = 1                    ' 启用圆弧插补模式
    'ARC_RADIUS = 5                  ' 默认圆弧半径5mm（可根据需要调整）

    '说明：所有运动都将使用XZ平面圆弧插补，Y轴独立移动
    '圆弧最高点固定在Z=20mm，实现平滑的弧形轨迹
    
    PRINT "轴参数设置完成（Y轴适配10mm螺距，运动速度1m/s，S型曲线100ms，标准三段轨迹）"
    PRINT "安全高度设置：取螺丝=", pick_safe_height, "mm，工件=", work_safe_height, "mm（必须>0）"
END SUB

'================ 数据设置 ================
GLOBAL SUB SetupData()
    '左侧螺丝位置数据(2x4阵列，8个螺丝)
    '第一行螺丝（Y = left_work_pos）
    TABLE(0) = 100                  ' 螺丝1：X=100
    TABLE(1) = left_work_pos        ' 螺丝1：Y=工作位置
    TABLE(2) = screw_work_height    ' 螺丝1：Z=30（打螺丝高度）
    TABLE(3) = 150                  ' 螺丝2：X=150
    TABLE(4) = left_work_pos        ' 螺丝2：Y=工作位置
    TABLE(5) = screw_work_height    ' 螺丝2：Z=30（打螺丝高度）
    TABLE(6) = 200                  ' 螺丝3：X=200
    TABLE(7) = left_work_pos        ' 螺丝3：Y=工作位置
    TABLE(8) = screw_work_height    ' 螺丝3：Z=30（打螺丝高度）
    TABLE(9) = 250                  ' 螺丝4：X=250
    TABLE(10) = left_work_pos       ' 螺丝4：Y=工作位置
    TABLE(11) = screw_work_height   ' 螺丝4：Z=30（打螺丝高度）

    '第二行螺丝（Y = left_work_pos + 40）
    TABLE(12) = 100                 ' 螺丝5：X=100
    TABLE(13) = left_work_pos + 40  ' 螺丝5：Y=工作位置+40
    TABLE(14) = screw_work_height   ' 螺丝5：Z=30（打螺丝高度）
    TABLE(15) = 150                 ' 螺丝6：X=150
    TABLE(16) = left_work_pos + 40  ' 螺丝6：Y=工作位置+40
    TABLE(17) = screw_work_height   ' 螺丝6：Z=30（打螺丝高度）
    TABLE(18) = 200                 ' 螺丝7：X=200
    TABLE(19) = left_work_pos + 40  ' 螺丝7：Y=工作位置+40
    TABLE(20) = screw_work_height   ' 螺丝7：Z=30（打螺丝高度）
    TABLE(21) = 250                 ' 螺丝8：X=250
    TABLE(22) = left_work_pos + 40  ' 螺丝8：Y=工作位置+40
    TABLE(23) = screw_work_height   ' 螺丝8：Z=30（打螺丝高度）

    '右侧螺丝位置数据(2x4阵列，8个螺丝)
    '第一行螺丝（Y = right_work_pos）
    TABLE(200) = 100                ' 螺丝1：X=100
    TABLE(201) = right_work_pos      ' 螺丝1：Y=工作位置
    TABLE(202) = screw_work_height   ' 螺丝1：Z=30（打螺丝高度）
    TABLE(203) = 150                ' 螺丝2：X=150
    TABLE(204) = right_work_pos      ' 螺丝2：Y=工作位置
    TABLE(205) = screw_work_height   ' 螺丝2：Z=30（打螺丝高度）
    TABLE(206) = 200                ' 螺丝3：X=200
    TABLE(207) = right_work_pos      ' 螺丝3：Y=工作位置
    TABLE(208) = screw_work_height   ' 螺丝3：Z=30（打螺丝高度）
    TABLE(209) = 250                ' 螺丝4：X=250
    TABLE(210) = right_work_pos      ' 螺丝4：Y=工作位置
    TABLE(211) = screw_work_height   ' 螺丝4：Z=30（打螺丝高度）

    '第二行螺丝（Y = right_work_pos + 40）
    TABLE(212) = 100                ' 螺丝5：X=100
    TABLE(213) = right_work_pos + 40 ' 螺丝5：Y=工作位置+40
    TABLE(214) = screw_work_height   ' 螺丝5：Z=30（打螺丝高度）
    TABLE(215) = 150                ' 螺丝6：X=150
    TABLE(216) = right_work_pos + 40 ' 螺丝6：Y=工作位置+40
    TABLE(217) = screw_work_height   ' 螺丝6：Z=30（打螺丝高度）
    TABLE(218) = 200                ' 螺丝7：X=200
    TABLE(219) = right_work_pos + 40 ' 螺丝7：Y=工作位置+40
    TABLE(220) = screw_work_height   ' 螺丝7：Z=30（打螺丝高度）
    TABLE(221) = 250                ' 螺丝8：X=250
    TABLE(222) = right_work_pos + 40 ' 螺丝8：Y=工作位置+40
    TABLE(223) = screw_work_height   ' 螺丝8：Z=30（打螺丝高度）
    
    PRINT "数据设置完成"
    PRINT "左侧螺丝数量：", left_screw_num, "（2x4阵列）"
    PRINT "右侧螺丝数量：", right_screw_num, "（2x4阵列）"
    PRINT "最大支持螺丝数量：", max_screw_num, "个/侧"
    PRINT "左侧用户位置：", left_user_pos, "mm，工作位置：", left_work_pos, "mm"
    PRINT "右侧用户位置：", right_user_pos, "mm，工作位置：", right_work_pos, "mm"
END SUB

'================ 设置螺丝数量 ================
GLOBAL SUB SetScrewCount(left_count, right_count)
    '验证螺丝数量范围
    IF left_count < 1 OR left_count > max_screw_num THEN
        PRINT "错误：左侧螺丝数量必须在1-", max_screw_num, "之间，当前值：", left_count
        RETURN
    ENDIF

    IF right_count < 1 OR right_count > max_screw_num THEN
        PRINT "错误：右侧螺丝数量必须在1-", max_screw_num, "之间，当前值：", right_count
        RETURN
    ENDIF

    '设置螺丝数量
    left_screw_num = left_count
    right_screw_num = right_count

    PRINT "螺丝数量设置完成："
    PRINT "左侧：", left_screw_num, "个螺丝"
    PRINT "右侧：", right_screw_num, "个螺丝"
    PRINT "总计：", left_screw_num + right_screw_num, "个螺丝"
END SUB

'================ 输入扫描（任务0持续执行）================
GLOBAL SUB ScanInput()
    '左侧开始按键
    IF SCAN_EVENT(IN(0)) > 0 THEN
        IF CheckHome() = 1 THEN
            PRINT "左侧作业请求"
            IF left_queue = 0 THEN
                left_queue = 1          ' 设置为等待状态
                PRINT "左侧任务加入队列"
                CALL LeftSlideToWork()  ' 左Y轴移动到工作位置
            ELSE
                PRINT "左侧任务已在队列中"
            ENDIF
        ELSE
            PRINT "系统未回零，无法开始作业"
        ENDIF
    ENDIF
    
    '右侧开始按键  
    IF SCAN_EVENT(IN(1)) > 0 THEN
        IF CheckHome() = 1 THEN
            PRINT "右侧作业请求"
            IF right_queue = 0 THEN
                right_queue = 1         ' 设置为等待状态
                PRINT "右侧任务加入队列"
                CALL RightSlideToWork() ' 右Y轴移动到工作位置
            ELSE
                PRINT "右侧任务已在队列中"
            ENDIF
        ELSE
            PRINT "系统未回零，无法开始作业"
        ENDIF
    ENDIF
    
    '系统回零
    IF SCAN_EVENT(IN(2)) > 0 THEN
        IF task_home_running = 0 THEN
            PRINT "开始系统回零"
            STOPTASK 1                  ' 停止可能存在的回零任务
            RUNTASK 1, SimpleHomeTask   ' 启动回零任务
            task_home_running = 1
        ELSE
            PRINT "回零任务正在运行中"
        ENDIF
    ENDIF
    
    '手动左Y轴到用户侧
    IF SCAN_EVENT(IN(4)) > 0 THEN
        IF left_queue = 0 THEN
            PRINT "手动左Y轴到用户侧"
            CALL LeftSlideToUser()
        ELSE
            PRINT "左侧有任务，无法手动控制"
        ENDIF
    ENDIF
    
    '手动右Y轴到用户侧
    IF SCAN_EVENT(IN(5)) > 0 THEN
        IF right_queue = 0 THEN
            PRINT "手动右Y轴到用户侧"
            CALL RightSlideToUser()
        ELSE
            PRINT "右侧有任务，无法手动控制"
        ENDIF
    ENDIF
    
    '急停
    IF SCAN_EVENT(IN(3)) > 0 THEN
        PRINT "急停触发！"
        RAPIDSTOP(2)
        
        '停止所有任务
        STOPTASK 1                  ' 停止回零任务
        screw_task_stop = 1         ' 通知打螺丝任务停止
        
        '清除所有状态
        sys_status = 0
        left_queue = 0
        right_queue = 0
        screwdriver_busy = 0
        task_home_running = 0
        
        OP(0, OFF)                  ' 关闭吸螺丝
        PRINT "所有任务已停止"
        
        '重新启动打螺丝任务
        DELAY(1000)
        screw_task_stop = 0
        STOPTASK 2
        RUNTASK 2, SimpleScrewTask
        task_screw_running = 1
        PRINT "打螺丝任务已重启"
    ENDIF
END SUB

'================ 更新任务状态 ================
GLOBAL SUB UpdateTaskStatus()
    '检查回零任务状态
    IF task_home_running = 1 THEN
        IF PROC_STATUS(1) = 0 THEN      ' 任务1已停止
            PRINT "回零任务完成"
            task_home_running = 0
            sys_status = 0              ' 回到待机状态
        ENDIF
    ENDIF

    '检查打螺丝任务状态
    IF task_screw_running = 1 THEN
        IF PROC_STATUS(2) = 0 THEN      ' 任务2已停止
            PRINT "打螺丝任务异常停止，重新启动"
            screw_task_stop = 0
            RUNTASK 2, SimpleScrewTask
            task_screw_running = 1
        ENDIF
    ENDIF
END SUB

'================ 双Y轴滑轨控制 ================
GLOBAL SUB LeftSlideToUser()
    IF left_slide_status <> 0 THEN
        PRINT "左Y轴移动到用户位置..."
        left_slide_status = 2       ' 设置为移动中

        'Y轴直线移动到用户位置
        BASE(1)
        MOVEABS(left_user_pos) AXIS(1)
        WAIT IDLE(1)

        left_slide_status = 0       ' 设置为用户侧
        PRINT "左Y轴已到达用户位置：", left_user_pos, "mm"
    ENDIF
END SUB

GLOBAL SUB LeftSlideToWork()
    IF left_slide_status <> 1 THEN
        PRINT "左Y轴移动到工作位置..."
        left_slide_status = 2       ' 设置为移动中

        'Y轴直线移动到工作位置
        BASE(1)
        MOVEABS(left_work_pos) AXIS(1)
        WAIT IDLE(1)

        left_slide_status = 1       ' 设置为工作侧
        PRINT "左Y轴已到达工作位置：", left_work_pos, "mm"
    ENDIF
END SUB

GLOBAL SUB RightSlideToUser()
    IF right_slide_status <> 0 THEN
        PRINT "右Y轴移动到用户位置..."
        right_slide_status = 2      ' 设置为移动中

        'Y轴直线移动到用户位置
        BASE(2)
        MOVEABS(right_user_pos) AXIS(2)
        WAIT IDLE(2)

        right_slide_status = 0      ' 设置为用户侧
        PRINT "右Y轴已到达用户位置：", right_user_pos, "mm"
    ENDIF
END SUB

GLOBAL SUB RightSlideToWork()
    IF right_slide_status <> 1 THEN
        PRINT "右Y轴移动到工作位置..."
        right_slide_status = 2      ' 设置为移动中

        'Y轴直线移动到工作位置
        BASE(2)
        MOVEABS(right_work_pos) AXIS(2)
        WAIT IDLE(2)

        right_slide_status = 1      ' 设置为工作侧
        PRINT "右Y轴已到达工作位置：", right_work_pos, "mm"
    ENDIF
END SUB

'================ 检查轴回零状态 ================
GLOBAL SUB CheckHome()
    FOR i = 0 TO 3
        IF axis_home(i) <> 2 THEN
            RETURN 0                ' 有轴未回零
        ENDIF
    NEXT
    RETURN 1                        ' 所有轴已回零
END SUB

'================ 状态更新 ================
GLOBAL SUB UpdateStatus()
    '更新Modbus寄存器供HMI显示
    MODBUS_REG(0) = sys_status
    MODBUS_REG(1) = left_screw_num
    MODBUS_REG(2) = right_screw_num
    MODBUS_REG(3) = cur_screw
    MODBUS_REG(4) = left_slide_status
    MODBUS_REG(5) = right_slide_status
    MODBUS_REG(6) = left_queue
    MODBUS_REG(7) = right_queue
    MODBUS_REG(8) = screwdriver_busy
    MODBUS_REG(9) = task_home_running
    MODBUS_REG(10) = task_screw_running
    MODBUS_REG(11) = screw_task_stop

    '更新各轴回零状态
    FOR i = 0 TO 3
        MODBUS_REG(20 + i) = axis_home(i)
    NEXT

    '更新各轴当前位置
    FOR i = 0 TO 3
        MODBUS_IEEE(30 + i) = DPOS(i)
    NEXT
END SUB

'================ 显示系统状态 ================
GLOBAL SUB ShowStatus()
    PRINT "=== 系统状态 ==="
    PRINT "系统状态：", sys_status, " (0-待机,1-运行,2-回零)"
    PRINT "当前螺丝：", cur_screw
    PRINT "左侧螺丝数量：", left_screw_num
    PRINT "右侧螺丝数量：", right_screw_num

    PRINT "=== 双Y轴滑轨状态 ==="
    PRINT "左Y轴状态：", left_slide_status, " (0-用户侧,1-工作侧,2-移动中)"
    PRINT "左Y轴位置：", DPOS(1), " mm"
    PRINT "右Y轴状态：", right_slide_status, " (0-用户侧,1-工作侧,2-移动中)"
    PRINT "右Y轴位置：", DPOS(2), " mm"

    PRINT "=== 队列状态 ==="
    PRINT "左侧队列：", left_queue, " (0-无任务,1-等待,2-执行中)"
    PRINT "右侧队列：", right_queue, " (0-无任务,1-等待,2-执行中)"
    PRINT "电批状态：", screwdriver_busy, " (0-空闲,1-忙碌)"

    PRINT "=== 任务运行状态 ==="
    PRINT "回零任务：", task_home_running, " (0-停止,1-运行)"
    PRINT "打螺丝任务：", task_screw_running, " (0-停止,1-运行)"
    PRINT "任务停止标志：", screw_task_stop, " (0-继续,1-停止)"

    PRINT "=== 轴回零状态 ==="
    FOR i = 0 TO 3
        PRINT "轴", i, "：", axis_home(i), " (0-未回零,1-回零中,2-已回零,3-失败)"
    NEXT
END SUB

'================ 简化回零任务（任务1执行）================
GLOBAL SUB SimpleHomeTask()
    sys_status = 2                  ' 设置为回零状态
    PRINT "开始四轴回零..."

    '简化回零流程，逐轴回零（测试版本）
    PRINT "注意：这是测试版本，将模拟回零成功"

    '轴3 (Z轴) 回零
    PRINT "开始轴3 (Z轴) 回零"
    axis_home(3) = 1
    BASE(3)
    DATUM(0) AXIS(3)
    DELAY(100)

    '检查是否有实际硬件
    DIM axis_status
    axis_status = AXISSTATUS(3)
    PRINT "轴3状态：", HEX(axis_status)

    IF axis_status = 0 THEN
        '有硬件，执行真实回零
        DATUM(3) AXIS(3)
        WAIT IDLE(3)
        IF AXISSTATUS(3) = 0 THEN
            axis_home(3) = 2
            PRINT "轴3回零成功"
        ELSE
            axis_home(3) = 3
            PRINT "轴3回零失败，状态：", HEX(AXISSTATUS(3))
            PRINT "继续模拟回零..."
            axis_home(3) = 2
            PRINT "轴3模拟回零成功"
        ENDIF
    ELSE
        '无硬件，模拟回零成功
        PRINT "检测到无硬件连接，模拟回零成功"
        axis_home(3) = 2
        PRINT "轴3模拟回零成功"
    ENDIF
    DELAY(10)

    '轴2 (Y2轴) 回零
    PRINT "开始轴2 (Y2轴) 回零"
    axis_home(2) = 1
    BASE(2)
    DATUM(0) AXIS(2)
    DELAY(10)

    axis_status = AXISSTATUS(2)
    PRINT "轴2状态：", HEX(axis_status)

    IF axis_status = 0 THEN
        DATUM(3) AXIS(2)
        WAIT IDLE(2)
        IF AXISSTATUS(2) = 0 THEN
            axis_home(2) = 2
            PRINT "轴2回零成功"
        ELSE
            axis_home(2) = 3
            PRINT "轴2回零失败，状态：", HEX(AXISSTATUS(2))
            PRINT "继续模拟回零..."
            axis_home(2) = 2
            PRINT "轴2模拟回零成功"
        ENDIF
    ELSE
        PRINT "检测到无硬件连接，模拟回零成功"
        axis_home(2) = 2
        PRINT "轴2模拟回零成功"
    ENDIF
    DELAY(10)

    '轴1 (Y1轴) 回零
    PRINT "开始轴1 (Y1轴) 回零"
    axis_home(1) = 1
    BASE(1)
    DATUM(0) AXIS(1)
    DELAY(10)

    axis_status = AXISSTATUS(1)
    PRINT "轴1状态：", HEX(axis_status)

    IF axis_status = 0 THEN
        DATUM(3) AXIS(1)
        WAIT IDLE(1)
        IF AXISSTATUS(1) = 0 THEN
            axis_home(1) = 2
            PRINT "轴1回零成功"
        ELSE
            axis_home(1) = 3
            PRINT "轴1回零失败，状态：", HEX(AXISSTATUS(1))
            PRINT "继续模拟回零..."
            axis_home(1) = 2
            PRINT "轴1模拟回零成功"
        ENDIF
    ELSE
        PRINT "检测到无硬件连接，模拟回零成功"
        axis_home(1) = 2
        PRINT "轴1模拟回零成功"
    ENDIF
    DELAY(10)

    '轴0 (X轴) 回零
    PRINT "开始轴0 (X轴) 回零"
    axis_home(0) = 1
    BASE(0)
    DATUM(0) AXIS(0)
    DELAY(10)

    axis_status = AXISSTATUS(0)
    PRINT "轴0状态：", HEX(axis_status)

    IF axis_status = 0 THEN
        DATUM(3) AXIS(0)
        WAIT IDLE(0)
        IF AXISSTATUS(0) = 0 THEN
            axis_home(0) = 2
            PRINT "轴0回零成功"
        ELSE
            axis_home(0) = 3
            PRINT "轴0回零失败，状态：", HEX(AXISSTATUS(0))
            PRINT "继续模拟回零..."
            axis_home(0) = 2
            PRINT "轴0模拟回零成功"
        ENDIF
    ELSE
        PRINT "检测到无硬件连接，模拟回零成功"
        axis_home(0) = 2
        PRINT "轴0模拟回零成功"
    ENDIF
    DELAY(10)

    '回零完成后，双Y轴移动到用户位置
    PRINT "双Y轴移动到用户位置..."
    CALL LeftSlideToUser()
    CALL RightSlideToUser()

    PRINT "所有轴回零完成，双Y轴在用户位置"
    '任务结束，sys_status将在UpdateTaskStatus中设置为0
END SUB

'================ 简化打螺丝任务（任务2执行）================
GLOBAL SUB SimpleScrewTask()
    PRINT "打螺丝任务启动，开始监控左右两侧队列"

    '持续监控左右两侧队列
    WHILE screw_task_stop = 0
        '检查电批是否空闲
        IF screwdriver_busy = 0 THEN
            '电批空闲，检查是否有等待的任务
            IF left_queue = 1 AND left_slide_status = 1 THEN
                '左侧任务等待中且左Y轴在工作位置
                PRINT "开始执行左侧打螺丝"
                left_queue = 2          ' 设置为执行中
                screwdriver_busy = 1    ' 电批设为忙碌
                CALL ExecuteLeftScrew()

            ELSEIF right_queue = 1 AND right_slide_status = 1 THEN
                '右侧任务等待中且右Y轴在工作位置
                PRINT "开始执行右侧打螺丝"
                right_queue = 2         ' 设置为执行中
                screwdriver_busy = 1    ' 电批设为忙碌
                CALL ExecuteRightScrew()
            ENDIF
        ENDIF

        DELAY(10)                      ' 10ms检查周期
    WEND

    PRINT "打螺丝任务停止"
END SUB

'================ 执行左侧打螺丝 ================
GLOBAL SUB ExecuteLeftScrew()
    PRINT "执行左侧打螺丝任务"

    FOR screw_idx = 0 TO left_screw_num - 1
        '检查停止标志
        IF screw_task_stop = 1 THEN
            PRINT "收到停止信号，左侧任务中断"
            GOTO left_task_end
        ENDIF

        cur_screw = screw_idx + 1
        PRINT "开始打第", cur_screw, "个螺丝（左侧）"

        '获取螺丝位置
        DIM screw_x, screw_y, screw_z
        screw_x = TABLE(left_start + screw_idx * 3)
        screw_y = TABLE(left_start + screw_idx * 3 + 1)
        screw_z = TABLE(left_start + screw_idx * 3 + 2)

        '执行打螺丝流程
        CALL WorkScrew(screw_x, screw_y, screw_z, 1)  ' 1表示左侧Y1轴

        'DELAY(500)                  ' 螺丝间延时
    NEXT

    left_task_end:
    PRINT "左侧打螺丝任务完成"

    '任务完成后的处理
    left_queue = 0                  ' 清除队列状态
    screwdriver_busy = 0            ' 电批设为空闲
    cur_screw = 0

    '左Y轴回到用户位置
    CALL LeftSlideToUser()
    PRINT "左侧任务完成，左Y轴已回到用户位置"
END SUB

'================ 执行右侧打螺丝 ================
GLOBAL SUB ExecuteRightScrew()
    PRINT "执行右侧打螺丝任务"

    FOR screw_idx = 0 TO right_screw_num - 1
        '检查停止标志
        IF screw_task_stop = 1 THEN
            PRINT "收到停止信号，右侧任务中断"
            GOTO right_task_end
        ENDIF

        cur_screw = screw_idx + 1
        PRINT "开始打第", cur_screw, "个螺丝（右侧）"

        '获取螺丝位置
        DIM screw_x, screw_y, screw_z
        screw_x = TABLE(right_start + screw_idx * 3)
        screw_y = TABLE(right_start + screw_idx * 3 + 1)
        screw_z = TABLE(right_start + screw_idx * 3 + 2)

        '执行打螺丝流程
        CALL WorkScrew(screw_x, screw_y, screw_z, 2)  ' 2表示右侧Y2轴

        'DELAY(500)                  ' 螺丝间延时
    NEXT

    right_task_end:
    PRINT "右侧打螺丝任务完成"

    '任务完成后的处理
    right_queue = 0                 ' 清除队列状态
    screwdriver_busy = 0            ' 电批设为空闲
    cur_screw = 0

    '右Y轴回到用户位置
    CALL RightSlideToUser()
    PRINT "右侧任务完成，右Y轴已回到用户位置"
END SUB

'================ 打螺丝作业流程 ================
GLOBAL SUB WorkScrew(target_x, target_y, target_z, y_axis)
    PRINT "目标位置：X=", target_x, " Y=", target_y, " Z=", target_z

    '1. 确保在取螺丝位置（统一的取螺丝位置）
    PRINT "确保在取螺丝位置"
    CALL EnsureAtPickPosition(y_axis)

    '2. 吸取螺丝
    PRINT "吸取螺丝"
    OP(0, ON)                       ' 开启吸螺丝
    DELAY(1000)                     ' 等待吸取稳定

    '3. 三段连续轨迹到螺丝孔位（中间不停）
    PRINT "三段连续轨迹到螺丝孔位（中间不停）"
    CALL MoveToTargetContinuous(target_x, target_y, target_z, y_axis)

    '4. 执行打螺丝
    PRINT "执行打螺丝"
    CALL DoScrew(target_z)

    '5. 三段连续轨迹回到取螺丝位置（中间不停）
    PRINT "三段连续轨迹回到取螺丝位置（中间不停）"
    CALL MoveBackToPickContinuous(y_axis)

    '6. 关闭吸螺丝
    OP(0, OFF)                      ' 关闭吸螺丝

    PRINT "螺丝完成"
END SUB

'================ 确保在取螺丝位置 ================
GLOBAL SUB EnsureAtPickPosition(y_axis)
    PRINT "确保在统一的取螺丝位置"

    '先移动Y轴到取螺丝位置
    BASE(y_axis)
    MOVEABS(pick_y) AXIS(y_axis)
    WAIT IDLE(y_axis)
    PRINT "Y轴移动到取螺丝位置：", pick_y, "mm"

    '检查当前XZ位置，如果不在取螺丝位置则移动过去
    DIM current_x, current_z
    current_x = DPOS(0)
    current_z = DPOS(3)

    '如果不在取螺丝位置，使用智能轨迹移动过去
    IF ABS(current_x - pick_x) > 1 OR ABS(current_z - pick_z) > 1 THEN
        PRINT "当前位置(", current_x, ",", current_z, ")，需要移动到取螺丝位置"

        '使用标准三段轨迹
        DIM start_safe, end_safe
        start_safe = work_safe_height
        end_safe = pick_safe_height

        CALL ThreeSegmentMove(current_x, current_z, pick_x, pick_z, start_safe, end_safe)
    ELSE
        PRINT "已在取螺丝位置：X=", pick_x, " Y=", pick_y, " Z=", pick_z
    ENDIF
END SUB

'================ 智能轨迹到目标位置（中间不停）================
GLOBAL SUB MoveToTargetContinuous(target_x, target_y, target_z, y_axis)
    PRINT "智能轨迹到螺丝孔位（从取螺丝位置出发，中间不停）"

    '先移动Y轴到目标位置
    BASE(y_axis)
    MOVEABS(target_y) AXIS(y_axis)
    WAIT IDLE(y_axis)
    PRINT "Y轴移动到目标位置：", target_y, "mm"

    '使用标准三段轨迹
    DIM start_safe, end_safe
    start_safe = pick_safe_height
    end_safe = work_safe_height
    PRINT "使用标准三段轨迹：起点安全高度=", start_safe, "mm，终点安全高度=", end_safe, "mm"

    '从取螺丝位置到螺丝孔位
    PRINT "从取螺丝位置(", pick_x, ",", pick_z, ")到螺丝孔位(", target_x, ",", target_z, ")"
    CALL ThreeSegmentMove(pick_x, pick_z, target_x, target_z, start_safe, end_safe)

    PRINT "到达螺丝孔位：X=", target_x, " Y=", target_y, " Z=", target_z
END SUB

'================ 智能轨迹回到取螺丝位置（中间不停）================
GLOBAL SUB MoveBackToPickContinuous(y_axis)
    PRINT "智能轨迹回到取螺丝位置（从螺丝孔位出发，中间不停）"

    '先移动Y轴到取螺丝位置
    BASE(y_axis)
    MOVEABS(pick_y) AXIS(y_axis)
    WAIT IDLE(y_axis)
    PRINT "Y轴移动到取螺丝位置：", pick_y, "mm"

    '使用标准三段轨迹
    DIM start_safe, end_safe
    start_safe = work_safe_height
    end_safe = pick_safe_height
    PRINT "使用标准三段轨迹：起点安全高度=", start_safe, "mm，终点安全高度=", end_safe, "mm"

    '从螺丝孔位回到取螺丝位置
    DIM current_x, current_z
    current_x = DPOS(0)
    current_z = DPOS(3)
    PRINT "从螺丝孔位(", current_x, ",", current_z, ")回到取螺丝位置(", pick_x, ",", pick_z, ")"
    CALL ThreeSegmentMove(current_x, current_z, pick_x, pick_z, start_safe, end_safe)

    PRINT "回到取螺丝位置：X=", pick_x, " Y=", pick_y, " Z=", pick_z
END SUB

'================ 执行打螺丝 ================
GLOBAL SUB DoScrew(target_z)
    '三段插补完成后已经到达可以打螺丝的位置，直接控制电批即可
    PRINT "开始打螺丝（Z轴已在正确位置：", target_z, "mm）"

    '电批锁紧
    PRINT "电批开始锁紧"
    OP(1, ON)                       ' 开启电批
    DELAY(2000)                     ' 锁紧2秒
    OP(1, OFF)                      ' 关闭电批
    PRINT "电批锁紧完成"

    PRINT "打螺丝完成，Z轴保持在工作位置"
END SUB

'================ 标准三段轨迹核心函数 ================
'标准三段轨迹：抬Z → 圆弧 → Z下降，安全高度必须大于0
GLOBAL SUB ThreeSegmentMove(start_x, start_z, end_x, end_z, start_safe_z, end_safe_z)
    DIM move_distance_x, move_total_distance
    DIM move_mid_x

    move_distance_x = end_x - start_x
    move_total_distance = ABS(move_distance_x)

    PRINT "标准三段轨迹：从(", start_x, ",", start_z, ")到(", end_x, ",", end_z, ")"
    PRINT "起点安全高度：", start_safe_z, "mm，终点安全高度：", end_safe_z, "mm"

    '验证安全高度必须大于0
    IF start_safe_z <= 0 OR end_safe_z <= 0 THEN
        PRINT "错误：安全高度必须大于0！起点=", start_safe_z, "，终点=", end_safe_z
        RETURN
    ENDIF

    '计算圆弧中间点
    move_mid_x = (start_x + end_x) / 2   ' X轴中点

    '设置XZ轴为基础轴，使用连续插补
    BASE(0, 3)                      ' X轴(0)和Z轴(3)

    '执行标准三段轨迹
    CALL StandardThreeSegment(start_x, start_z, end_x, end_z, start_safe_z, end_safe_z, move_mid_x, move_total_distance)

    '等待所有运动完成
    WAIT IDLE(0)
    WAIT IDLE(3)

    PRINT "标准三段轨迹完成"
END SUB



'================ 标准三段轨迹（两端都用安全高度）================
GLOBAL SUB StandardThreeSegment(start_x, start_z, end_x, end_z, start_safe_z, end_safe_z, std_mid_x, std_distance)
    '第一段：抬Z到起点安全高度
    PRINT "第一段：抬Z到安全高度", start_safe_z, "mm"
    MOVEABS(start_x, start_safe_z)

    '第二段：安全高度之间的圆弧插补
    IF std_distance >= 5 THEN
        PRINT "第二段：安全高度间圆弧插补"
        MOVECIRC2ABS(std_mid_x, arc_top_height, end_x, end_safe_z)
    ELSE
        PRINT "第二段：安全高度间直线移动"
        MOVEABS(end_x, end_safe_z)
    ENDIF

    '第三段：Z下降到目标位置
    PRINT "第三段：Z下降到目标位置", end_z, "mm"
    MOVEABS(end_x, end_z)
END SUB





'================ 备用直线移动函数 ================
GLOBAL SUB LinearXZMove(start_x, start_z, end_x, end_z)
    PRINT "直线XZ移动：从(", start_x, ",", start_z, ")到(", end_x, ",", end_z, ")"

    BASE(0, 3)
    MOVEABS(end_x) AXIS(0)
    MOVEABS(end_z) AXIS(3)
    WAIT IDLE(0)
    WAIT IDLE(3)

    PRINT "直线XZ移动完成"
END SUB


