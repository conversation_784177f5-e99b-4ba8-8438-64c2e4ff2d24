'=============================================================================
' Zbasic 回原点演示程序
' 深圳市正运动技术有限公司
' 演示各种回零方式和回零状态检测
'=============================================================================

'变量定义
DIM home_status          '回零状态：0-未回零，1-回零中，2-回零完成，3-回零失败
DIM current_axis         '当前回零轴号
DIM home_mode            '回零模式
DIM total_axes           '总轴数

'常量定义
CONST MAX_AXES = 3       '最大轴数
CONST HOME_SPEED = 100   '回零速度
CONST CREEP_SPEED = 20   '爬行速度

'=============================================================================
' 主程序
'=============================================================================
PRINT "=== 正运动回原点演示程序 ==="
PRINT "支持多种回零模式和状态监控"

'初始化系统
CALL SystemInit()

'主循环
WHILE 1
    PRINT ""
    PRINT "请选择操作："
    PRINT "IN0 - 单轴回零演示"
    PRINT "IN1 - 多轴顺序回零"
    PRINT "IN2 - 多轴同时回零"
    PRINT "IN3 - 回零状态检查"
    PRINT "IN7 - 紧急停止"
    
    '检查输入信号
    IF SCAN_EVENT(IN(0)) > 0 THEN
        CALL SingleAxisHome()
    ENDIF
    
    IF SCAN_EVENT(IN(1)) > 0 THEN
        CALL MultiAxisSequentialHome()
    ENDIF
    
    IF SCAN_EVENT(IN(2)) > 0 THEN
        CALL MultiAxisSimultaneousHome()
    ENDIF
    
    IF SCAN_EVENT(IN(3)) > 0 THEN
        CALL CheckHomeStatus()
    ENDIF
    
    '紧急停止
    IF IN(7) = ON THEN
        RAPIDSTOP(2)
        PRINT "紧急停止！所有轴停止运动"
        home_status = 3
        DELAY(1000)
    ENDIF
    
    '状态指示灯
    IF home_status = 1 THEN      '回零中
        OP(0, TICKS AND 1)       '红灯闪烁
        OP(1, OFF)
    ELSEIF home_status = 2 THEN  '回零完成
        OP(0, OFF)
        OP(1, ON)                '绿灯亮
    ELSEIF home_status = 3 THEN  '回零失败
        OP(0, ON)                '红灯亮
        OP(1, OFF)
    ELSE                         '待机
        OP(0, OFF)
        OP(1, OFF)
    ENDIF
    
    DELAY(100)
WEND
END

'=============================================================================
' 系统初始化
'=============================================================================
SUB SystemInit()
    PRINT "初始化回零系统..."
    
    total_axes = MAX_AXES
    home_status = 0
    current_axis = 0
    
    '轴参数设置
    FOR i = 0 TO total_axes - 1
        BASE(i)
        ATYPE = 1                    '脉冲轴
        UNITS = 100                  '脉冲当量
        SPEED = HOME_SPEED           '回零速度
        ACCEL = 1000                 '加速度
        DECEL = 1000                 '减速度
        CREEP = CREEP_SPEED          '爬行速度
        HOMEWAIT = 100               '反找等待时间
        
        '原点输入设置
        DATUM_IN = 8 + i             '原点输入：IN8, IN9, IN10
        INVERT_IN(8 + i, ON)         '反转原点信号（ZMC系列OFF有效）
        
        '限位输入设置
        FWD_IN = 11 + i              '正向限位：IN11, IN12, IN13
        REV_IN = 14 + i              '负向限位：IN14, IN15, IN16
        INVERT_IN(11 + i, ON)        '反转限位信号
        INVERT_IN(14 + i, ON)
    NEXT
    
    '关闭所有输出
    FOR i = 0 TO 7
        OP(i, OFF)
    NEXT
    
    PRINT "系统初始化完成"
    PRINT "轴数量：", total_axes
    PRINT "回零速度：", HOME_SPEED
    PRINT "爬行速度：", CREEP_SPEED
END SUB

'=============================================================================
' 单轴回零演示
'=============================================================================
SUB SingleAxisHome()
    PRINT "=== 单轴回零演示 ==="
    
    '选择要回零的轴（这里以轴0为例）
    current_axis = 0
    PRINT "开始轴", current_axis, "回零..."
    
    home_status = 1  '设置为回零中状态
    
    '执行回零
    CALL HomeAxis(current_axis, 3)  '使用回零模式3
    
    '检查回零结果
    IF AXISSTATUS(current_axis) AND 1 = 0 THEN
        home_status = 2  '回零成功
        PRINT "轴", current_axis, "回零成功！"
        PRINT "回零后位置：", DPOS(current_axis)
    ELSE
        home_status = 3  '回零失败
        PRINT "轴", current_axis, "回零失败！"
        PRINT "轴状态：", HEX(AXISSTATUS(current_axis))
    ENDIF
END SUB

'=============================================================================
' 多轴顺序回零
'=============================================================================
SUB MultiAxisSequentialHome()
    PRINT "=== 多轴顺序回零演示 ==="
    
    home_status = 1  '设置为回零中状态
    
    '逐个轴进行回零
    FOR i = 0 TO total_axes - 1
        current_axis = i
        PRINT "开始轴", i, "回零..."
        
        '执行回零
        CALL HomeAxis(i, 3)
        
        '检查回零结果
        IF AXISSTATUS(i) AND 1 = 0 THEN
            PRINT "轴", i, "回零成功，位置：", DPOS(i)
        ELSE
            PRINT "轴", i, "回零失败，状态：", HEX(AXISSTATUS(i))
            home_status = 3  '有轴回零失败
            RETURN  '退出回零过程
        ENDIF
        
        DELAY(500)  '轴间延时
    NEXT
    
    home_status = 2  '所有轴回零成功
    PRINT "所有轴顺序回零完成！"
END SUB

'=============================================================================
' 多轴同时回零
'=============================================================================
SUB MultiAxisSimultaneousHome()
    PRINT "=== 多轴同时回零演示 ==="
    
    home_status = 1  '设置为回零中状态
    
    '同时启动所有轴回零
    FOR i = 0 TO total_axes - 1
        BASE(i)
        DATUM(3)  '启动回零，不等待完成
        PRINT "轴", i, "开始回零..."
    NEXT
    
    '等待所有轴回零完成
    LOCAL all_home_done
    all_home_done = 0
    
    WHILE all_home_done = 0
        all_home_done = 1
        
        '检查每个轴的回零状态
        FOR i = 0 TO total_axes - 1
            IF IDLE(i) = 0 THEN  '轴还在运动中
                all_home_done = 0
                PRINT "等待轴", i, "回零完成..."
                DELAY(500)
                EXIT  '跳出FOR循环，继续等待
            ENDIF
        NEXT
    WEND
    
    '检查所有轴的回零结果
    LOCAL home_success
    home_success = 1
    
    FOR i = 0 TO total_axes - 1
        IF AXISSTATUS(i) AND 1 = 0 THEN
            PRINT "轴", i, "回零成功，位置：", DPOS(i)
        ELSE
            PRINT "轴", i, "回零失败，状态：", HEX(AXISSTATUS(i))
            home_success = 0
        ENDIF
    NEXT
    
    IF home_success = 1 THEN
        home_status = 2  '所有轴回零成功
        PRINT "所有轴同时回零完成！"
    ELSE
        home_status = 3  '有轴回零失败
        PRINT "部分轴回零失败！"
    ENDIF
END SUB

'=============================================================================
' 回零单个轴的子程序
'=============================================================================
SUB HomeAxis(axis_num, mode)
    PRINT "执行轴", axis_num, "回零，模式：", mode
    
    BASE(axis_num)
    
    '清除轴状态
    AXIS_STOPREASON = 0
    
    '执行回零
    DATUM(mode)
    WAIT IDLE  '等待回零完成
    
    '延时确保状态稳定
    DELAY(100)
END SUB

'=============================================================================
' 检查回零状态
'=============================================================================
SUB CheckHomeStatus()
    PRINT "=== 回零状态检查 ==="
    
    FOR i = 0 TO total_axes - 1
        PRINT "轴", i, "状态检查："
        PRINT "  位置：", DPOS(i)
        PRINT "  编码器位置：", MPOS(i)
        PRINT "  轴状态：", HEX(AXISSTATUS(i))
        PRINT "  运动状态：", IIF(IDLE(i) = -1, "停止", "运动中")
        
        '检查是否在原点附近
        IF ABS(DPOS(i)) < 1 THEN
            PRINT "  原点状态：在原点"
        ELSE
            PRINT "  原点状态：偏离原点", DPOS(i), "单位"
        ENDIF
        
        '检查原点输入信号
        LOCAL home_input
        home_input = IN(8 + i)
        PRINT "  原点信号：", IIF(home_input = ON, "有效", "无效")
        
        PRINT ""
    NEXT
    
    '显示系统状态
    PRINT "系统回零状态：", home_status
    IF home_status = 0 THEN
        PRINT "状态说明：未回零"
    ELSEIF home_status = 1 THEN
        PRINT "状态说明：回零中"
    ELSEIF home_status = 2 THEN
        PRINT "状态说明：回零完成"
    ELSE
        PRINT "状态说明：回零失败"
    ENDIF
END SUB

'=============================================================================
' 回零模式说明子程序
'=============================================================================
SUB ShowHomeModes()
    PRINT "=== 回零模式说明 ==="
    PRINT "模式1：正向找原点，反向离开"
    PRINT "模式2：负向找原点，正向离开"
    PRINT "模式3：正向找原点，反向离开（推荐）"
    PRINT "模式4：负向找原点，正向离开"
    PRINT "模式5：正向找原点+Z信号"
    PRINT "模式6：负向找原点+Z信号"
    PRINT "模式8：正向找原点（简单）"
    PRINT "模式9：负向找原点（简单）"
    PRINT ""
    PRINT "当前使用模式3：适合大多数应用"
END SUB
