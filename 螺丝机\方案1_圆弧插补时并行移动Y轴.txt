===============================================================================
                    方案1：第二段圆弧插补开始时并行移动Y轴（高效但需谨慎）
===============================================================================

【方案1实现代码】

如果您想要最高效率的并行运动，可以使用以下代码替换当前的实现：

=== 修改WorkScrew函数 ===
```basic
'5. 三段连续轨迹回到取螺丝位置（中间不停）
PRINT "三段连续轨迹回到取螺丝位置（中间不停）"
PRINT "在第二段圆弧插补时并行移动Y轴"
CALL MoveBackToPickWithParallelY(y_axis, next_y, last_flag)

'6. 关闭吸螺丝
OP(0, OFF)                      ' 关闭吸螺丝

PRINT "螺丝完成"
```

=== 新增MoveBackToPickWithParallelY函数 ===
```basic
'================ 回程三段轨迹+Y轴并行移动 ================
GLOBAL SUB MoveBackToPickWithParallelY(y_axis, next_y, last_flag)
    PRINT "智能轨迹回到取螺丝位置（第二段时并行移动Y轴）"

    '使用标准三段轨迹
    DIM start_safe, end_safe
    start_safe = work_safe_height
    end_safe = pick_safe_height
    PRINT "使用标准三段轨迹：起点安全高度=", start_safe, "mm，终点安全高度=", end_safe, "mm"

    '从螺丝孔位回到取螺丝位置
    DIM current_x, current_z
    current_x = DPOS(0)
    current_z = DPOS(3)
    
    DIM move_mid_x, move_total_distance
    move_mid_x = (current_x + pick_x) / 2
    move_total_distance = ABS(pick_x - current_x)
    
    PRINT "从螺丝孔位(", current_x, ",", current_z, ")回到取螺丝位置(", pick_x, ",", pick_z, ")"
    
    BASE(0, 3)                      ' X轴(0)和Z轴(3)
    
    '执行带Y轴并行移动的三段轨迹
    CALL StandardThreeSegmentWithParallelY(current_x, current_z, pick_x, pick_z, start_safe, end_safe, move_mid_x, move_total_distance, y_axis, next_y, last_flag)
    
    '等待所有运动完成
    WAIT IDLE(0)
    WAIT IDLE(3)
    CALL WaitForYMove()             ' 等待Y轴移动完成
    
    PRINT "回程三段轨迹+Y轴并行移动完成"
END SUB
```

=== 新增StandardThreeSegmentWithParallelY函数 ===
```basic
'================ 带Y轴并行移动的标准三段轨迹 ================
GLOBAL SUB StandardThreeSegmentWithParallelY(start_x, start_z, end_x, end_z, start_safe_z, end_safe_z, std_mid_x, std_distance, y_axis, next_y, last_flag)
    '第一段：抬Z到起点安全高度（绝对值）
    PRINT "第一段：抬Z到安全高度", start_safe_z, "mm（绝对值）"
    MOVEABS(start_x, start_safe_z)
    
    '等待第一段完成，确保批头已经抬升到安全高度
    WAIT IDLE(0)
    WAIT IDLE(3)
    PRINT "批头已抬升到安全高度，可以安全移动Y轴"

    '第二段：安全高度之间的圆弧插补，同时启动Y轴并行移动
    PRINT "第二段：安全高度间圆弧插补，同时启动Y轴并行移动"
    CALL StartParallelYMove(y_axis, next_y, last_flag)
    
    IF std_distance >= 5 THEN
        PRINT "执行圆弧插补，Y轴并行移动中..."
        MOVECIRC2ABS(std_mid_x, arc_top_height, end_x, end_safe_z)
    ELSE
        PRINT "执行直线移动，Y轴并行移动中..."
        MOVEABS(end_x, end_safe_z)
    ENDIF

    '第三段：Z下降到目标位置（绝对值）
    '在Z下降前，等待Y轴移动完成，确保协调配合
    CALL WaitForYMove()
    PRINT "Y轴已到位，执行第三段Z下降"
    PRINT "第三段：Z下降到目标位置", end_z, "mm（绝对值）"
    MOVEABS(end_x, end_z)
END SUB
```

【方案1的优势】

=== 最高效率 ===
```
✅ 真正的并行运动：Y轴和XZ轴同时运动
✅ 时间最优化：充分利用圆弧插补的时间
✅ 效率最大化：Y轴移动时间完全隐藏在XZ轴运动中
```

=== 安全保障 ===
```
✅ 第一段完成后再移动Y轴：确保批头已抬升到安全高度
✅ 第三段前等待Y轴：确保Y轴到位后再下降
✅ 分段控制：每个阶段都有明确的安全检查
```

【方案1的风险和注意事项】

=== 潜在风险 ===
```
⚠️ 机械振动：Y轴移动可能影响XZ轴的圆弧插补精度
⚠️ 时序复杂：需要精确控制Y轴启动和等待时机
⚠️ 调试复杂：并行运动的问题更难定位
⚠️ 硬件要求：需要机械结构足够刚性
```

=== 使用条件 ===
```
✅ 机械结构刚性好，Y轴移动不会影响XZ轴精度
✅ 对效率要求极高的场合
✅ 有充分的测试和验证条件
✅ 操作人员有足够的技术水平
```

【方案选择建议】

=== 推荐方案2（当前实现）===
```
适用场景：
✅ 首次部署和调试
✅ 对安全性要求高
✅ 机械精度要求高
✅ 维护人员技术水平一般

优势：
✅ 安全可靠，无碰撞风险
✅ 逻辑简单，易于调试
✅ 不影响三段插补精度
✅ 维护成本低
```

=== 可选方案1（高效版本）===
```
适用场景：
✅ 系统调试完成后的优化阶段
✅ 对效率要求极高
✅ 机械结构刚性好
✅ 有专业技术人员维护

优势：
✅ 效率最高
✅ 真正的并行运动
✅ 时间利用率最大化
```

【实施建议】

=== 分阶段实施 ===
```
第一阶段：使用方案2（当前实现）
- 完成系统调试
- 验证基本功能
- 确保安全可靠

第二阶段：测试方案1（可选）
- 在方案2稳定运行后
- 小批量测试方案1
- 对比效率提升
- 评估风险和收益

第三阶段：选择最终方案
- 根据实际需求选择
- 考虑维护成本
- 平衡效率和安全性
```

=== 测试要点 ===
```
如果选择方案1，重点测试：
1. Y轴移动是否影响圆弧插补精度
2. 批头抬升后Y轴移动的安全性
3. 第三段前Y轴是否准确到位
4. 整体运动的流畅性
5. 长时间运行的稳定性
```

【结论】

当前实现的方案2是最安全可靠的选择，建议先使用方案2完成系统调试和验证。
如果后续对效率有更高要求，可以考虑测试方案1。

方案2的特点：
✅ 安全第一：完全避免批头碰撞风险
✅ 稳定可靠：不影响三段插补的流畅性
✅ 易于维护：逻辑简单，问题容易定位
✅ 效率良好：虽然不是最高效，但已经比传统方式有显著提升

===============================================================================
