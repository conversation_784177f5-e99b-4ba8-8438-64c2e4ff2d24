BASIC 语言编程入门


深圳市正运动技术有限公司 2024-12-24

Basic 语言编程入门
1Basic 编程须知	
1.1Basic 语言简介
Basic 编程提供所有标准程序语法：变量、数组、条件判断，循环以及数学运算，此外，扩展的指令以及函数提供广泛的运动控制功能和机器视觉功能。
运动控制功能，例如单轴运动、多轴的插补运动、轴的同步和异步运动，同时还有对数字和模拟 I/O 的控制等。
机器视觉功能，例如视觉定位、测量、识别、检测等，机器视觉和运动控制等系统可在一台控制器上完成开发。
Basic 支持自定义 SUB 过程，可以把一些通用的功能编写为自定义 SUB 过程；方便程序编写和修改；同时支持G 代码形式的 SUB 过程。支持全局的变量、数组和 SUB 过程；文件模块变量、数组、和 SUB 过程、以及局部变量(LOCAL)。
Basic 有实时多任务特性，多个 Basic 程序可以同时构建并同时运行，使得复杂的应用变得简单易用。
Basic 支持中断程序，例如掉电中断，通过掉电中断可以使得掉电的状态得到记录，以方便上电时恢复。

1.2开发环境
Basic 语言在 RTSys 开发环境上开发，RTSys 开发环境支持 Basic、PLC、HMI、C 四种编程语言，并可以实现混合编程，不同语言之间支持互相调用。
编程之前，先建立工程项目文件.zpj，项目之下再新建.bas 文件，详情参见 RTBasic 编程手册的新建工程说明。一个项目下可包含多个.bas 文件，文件运行有两个必须的条件，一是给文件设置自动运行任务号，二是要将程序下载到控制器/仿真器里。


Basic 开发之前要先熟悉 RTSys 软件的基础操作，详情参见软件的帮助菜单，打开 RTSys“帮助文档”查看。
开发环境支持仿真，在没有控制器的场合支持先连接到仿真器调试程序，菜单栏“常用”-“连接”-“仿真器”或“控制器”-“连接”-“仿真器”即可，连接成功后会打印提示信息。

1.3参数查看
在让轴运动之前，需要设置一系列的轴参数，例如脉冲当量 UNITS，轴类型 ATYPE，轴运动速度 SPEED等等。参数设置完成下载到控制器运行之后，可借助下图右侧“轴参数”窗口查看当前的参数是否更新成功，在此窗口填写的参数支持立即修改生效。
“轴参数”窗口上有两个按钮，轴选择：选择此窗口要显示的轴；参数选择：自定义添加需要的轴参数到此窗口。“轴参数”窗口若关闭，可在菜单栏“视图”-“轴参数”再次打开。
参数查看有以下三种方式。
(1)“轴参数”窗口实时读取参数显示，判断当前参数的设置情况参考此窗口更为准确.
(2)在调试状态下添加需要的参数到“视图”-“监视”窗口，“监视”窗口的内容也是实时刷新的。
(3)通过发送在线命令打印查看。使用“?*参数名”可以打印一个所有轴的单个参数。




轴选择：选择“轴参数”窗口要显示的轴号，勾选后确定。

参数选择：选择“轴参数”窗口要显示的参数，勾选后确定。



1.4错误提示
Basic 程序编写完成之后，需要下载到控制器才能执行。如下图，程序点击了下载之后或程序正在运行时出现了红色字体的 Error 错误提示，此时程序已经停止运行，我们要根据报错的打印信息提示修改错误，然后再次下载程序运行。
一般指令参数出现黑色的字体需要注意，可能是错误的或当前不支持的命令。正确的命令和参数都会显示其他的颜色，比如下方窗口中的程序逻辑语法显示蓝色，自定义参数显示棕色，轴参数显示紫色，注释的内容为绿色等。


还有一类警报是 AXISSTATUS 轴参数报警，也会影响程序的运行，要在 Basic 手册里查询此指令的说明再针对性的清除错误。
例如下图，报警 10h，查询指令说明得知是碰到了正向硬限位，此时我们让轴远离正向硬限位或者取消正向硬限位输入，可以解除此报警。

2执行逻辑	
Basic 程序从上至下只执行一次，代码扫描完或者遇 END 指令便停止。需要循环执行的部分使用循环指令。可用任务停止指令停止 Basic 程序的扫描执行，按下快捷键可快速停止所有程序和轴。
主要结构有顺序、循环、选择、延时、等待、跳转等。
在程序运行过程中，“命令与输出”窗口打印橙色字体显示的 warn 报警时程序仍可以运行；打印红色字体的 error 错误时，程序无法运行，需要清除错误后再次下载程序运行。
程序中“?”为指令 PRINT 打印指令的简写；WA 为 DELAY 延时指令的简写。

2.1顺序
在没有其他结构时，basic 程序扫描执行一次后结束。重新启动任务或重新下载程序可再次运行。
程序上电扫描的速度非常快，若不想代码上电就运行，建议在程序中加入条件判断、延时或等待等手段。

	ZMotion	正运动技术	


2.2循环
程序执行到循环语句结构时，先判断循环条件，满足条件循环一直执行，循环条件不满足的情况才退出，需要先执行完全部循环，再继续往下执行程序。
程序遇到死循环时，在循环语句结构之后的程序都不会再执行。在循环内使用EXIT 指令可以强制退出循环。



常见的循环指令有 FOR、WHILE、REPEAT。
FOR 一般用于有限次循环，条件不满足退出循环，与 TO，NEXT 指令组合使用。
WHILE 一般用于一直循环的场合（死循环），满足条件一直循环，与 WEND 组合使用。
REPEAT 先执行，再判断条件。满足条件结束循环，不满足条件则继续循环，与 UNTIL 联合使用，条件写在 UNTIL 后。





2.3选择
程序执行到 IF 选择语句时，先判断 IF 条件是否成立，条件成立执行 IF 的内容，否则跳过此 IF，再继续往下执行程序。
IF 需要和 THEN、ENDIF 组合使用，形成一个最简单的 IF 结构。有多个条件判断时，使用 ELSE， ELSEIF 语句加入其他条件。


IF 有多个条件的时候，可用 AND 或者 OR 连接。用 AND 时，需要条件都为真才成立：


用 OR 时，并列条件有一个为真便成立：




2.4延时
程序遇到DELAY 延时语句，会停止对应时间后，再继续向下扫描执行，故使用DELAY 语句后可能会出现程序堵塞的情况，时间为 ms 单位。
TICKS 为计数，每个控制器周期自动减一，控制器周期一般为 1ms，此指令不会堵塞程序。


2.5等待
程序遇到 WAIT 等待语句时，会停止在此命令行，直到 WAIT 条件满足，才继续向下执行。
程序除了在执行到 WAIT 等待语句，DELAY 延时语句时可能会阻塞以外，在扫描到运动指令时，如果轴的运动缓冲区满了，此时程序会停在当前运动指令行，直到当前运动完成，缓冲区空出一条，程序就会继续往下扫描执行。


2.6跳转
程序遇到GOTO 指令直接跳转到预先设置好的标号位置，然后往下执行，GOTO 之后到标号之间的一段程序被跳过。
ON GOTO 为条件跳转，满足条件后执行跳转，否则不跳转。语法：ON	a>10	GOTO	label





2.7子程序调用
程序遇到 SUB 时，会直接跳转到 SUB 定义，执行完毕再回到程序跳转位置继续向下执行。



SUB 还支持使用单独的任务去运动，此情况下SUB 运行完不会返回主程序，主程序继续向下扫描，详情参见多任务编程章节说明。




3自定义参数	
自定义参数分为四类：常量、变量、数组、SUB 子函数。
Basic 语法不区分大小写，使用时注意大小写统一。变量名最多支持字符数可通过在“在线命令”窗口?*max 打印控制器硬件参数查看。
参数定义指令有三个：LOCAL 局部调用，DIM 文件调用，GLOBAL 全局调用。
作用范围：LOCAL 局部调用在 SUB 内定义，用于本 SUB 内，SUB 本身传递的参数就是局部范围。 DIM 调用的参数为文件调用范围，文件调用只能在此文件下使用。希望参数全局调用可以使用 GLOBAL 定义，全局调用可以在整个项目中使用。

3.1变量
变量是用户可以自定义的参数，变量用于暂时保存与外部设备的通信数据或任务内部处理需要的数据，换言之，它是用于保存带名称和数据类型等属性的数据，无需指定变量与存储器地址之间的分配。
变量定义指令：分为全局变量（GLOBAL）、文件模块变量（DIM）、局部变量（LOCAL）三种。全局变量（GLOBAL）：可以在项目内的任意文件中使用；
文件模块变量（DIM）：只能在本程序文件内部使用；
局部变量（LOCAL）：主要用在 SUB 中，其他文件无法使用。 变量可以不经过定义直接赋值，此时的变量默认为文件模块变量。


3.2常量
变量的值因代入该变量的数据而异。与之相对的固定不变的值为常数，常量的值一经定义后不能再修改，只可读取。



CONST 定义常量，一次只能定义一个，且定义与赋值必须在一行。常量可定义为全局常量 GLOBAL CONST，全局常量可以在任意文件中使用，不存在 LOCAL CONST 的写法。常数与变量不同，不是保存在存储器中的信息，常见的常量有布尔型、字符串型、时间型、日期型、整形等。




3.3数组
数组指定是指将相同属性的数据集中后对其进行统一定义，并对数据个数进行指定。构成数组的各数据称为“元素”。
数组定义相关指令为 GLOBAL、DIM，不支持 LOCAL 定义。
数组定义时注意数组空间大小的指定，不能使用超出定义范围的空间，否则程序报错数组空间超限。


3.4子函数 SUB
SUB 和 END SUB 组合定义子函数，可以传递参数，传参为局部变量类型，也可以省略，通过 RETURN
可以设置返回值，也可以省略。
注意，SUB 定义一定要放在主程序END 之后，使用时再调用。子函数定义分为文件和全局两类，全局定义使用GLOBAL 指令。





3.5数据类型
数据类型是指对变量表示的值的形式和范围进行特定的规定。声明该变量时，数据类型的大小根据存储器内的数据范围大小而定，存储器内的数据范围越大，可表示的值的范围就越大。
指令的输入或输出变量的数据类型由指令确定。
自定义变量的数据类型属于动态类型，将整数赋值给变量时，变量就是整型；将浮点数赋值给变量，变量就是浮点型。
常用寄存器数据类型表：

寄存器类型	数据类型	取值范围
MODBUS_BIT	布尔型	0 或 1
MODBUS_REG	16 位整型	-32768 到 32767
MODBUS_LONG	
32 位整型	
-2147483648 到 2147483647
VR_INT		
MODBUS_IEEE	

32 位浮点型	

-3.4028235E+38 到-1.401298E-45
VR		
TABLE，自定义数组，变量（ZMC3 系列及之前）		
TABLE，自定义数组，变量（ZMC4 系列及之后）	64 位浮点型	1.7E-308 到 1.7E+308
VRSTRING	字符	一个字符占一个 VR
MODBUS_STRING	字符	一个字符占 8 位
TABLESTRING	字符	一个字符占一个 TABLE
注意：不同类型数据之间的操作要注意数据类型，类型不匹配会导致数据丢失。



4寄存器存储	
控制器寄存器主要有 TABLE、MODBUS、VR 寄存器、FLASH 块，通过自定义参数也能实现存储。将 RTSys 软件与控制器连接后，可通过 RTSys 软件“控制器”-“控制器状态”查看该控制器各寄存器的空间大小，也可以通过“在线命令”窗口输入“?*max”来查看各寄存器的数量，不同的控制器存储空间大小不同。


4.1TABLE
TABLE 是控制器自带的一个超大数组，数据类型为 32 位浮点型（4 系列及以上为 64 位浮点数），掉电不保存。编写程序时，TABLE 数组不需要再定义，可直接使用，索引下标从 0 开始。
Basic 的某些指令可以直接读取 TABLE 内的值作为参数，比如 CAM，CAMBOX，CONNFRAME， CONNREFRAME，MOVE_TURNABS，B_SPLINE，CAN，CRC16，DTSMOOTH，PITCHSET，HW_PSWITCH
等指令。
示波器采样的参数也存储在 TABLE 里。因此在开发应用中要注意多个 TABLE 区域的分配与使用，不要与示波器采样的数据存储区域重合。
1）TABLE 指令读写数据。





2）TSIZE 指令可读取 TABLE 空间大小，还可修改 TABLE 空间大小（不能超出 TABLE 最大空间）。

3）TABLESTRING 指令按照字符串格式打印 TABLE 里的数据。



TABLE 作为参数传递时用法大致相同，以 CAM 凸轮指令为例：
CAM(start point, end point, table multiplier, distance)
start point：起始点 TABLE 编号，存储第一个点的位置
end point：结束点 TABLE 编号
table multiplier：位置乘以这个比例，一般设为脉冲当量值 distance：参考运动的距离






查看 TABLE 内数据的方式有 2 种：
第一种：在在线命令行输入?*TABLE(10,10)查询 TABLE(10)开始，依次 10 个数据。

第二种：在寄存器中查看 DT(TABLE)数据，起始编号从 10 开始，个数 10 个。






4.2MODBUS
MODBUS 寄存器符合 MODBUS 标准通讯协议，分为位寄存器和字寄存器两类。MODBUS 寄存器的数据掉电不保存。
位寄存器：MODBUS_BIT，触摸屏一般称为 MODBUS_0X，布尔型。
字寄存器：MODBUS_REG、MODBUS_LONG、MODBUS_IEEE、MODBUS_STRING，触摸屏一般叫
MODBUS_4X，类型如下图。

	MODBUS_REG		16位INT
				
	
		MODBUS_LONG		32位INT
				
MODBUS_4X共用空间		
		
		MODBUS_IEEE		32位FLOAT
				
	
		MODBUS_STRING		1字节字符串
			

控制器中 MODBUS 字寄存器占用同一个变量空间，其中一个 LONG 占用两个 REG 地址，一个 IEEE
也占用两个 REG 地址，使用时要注意错开字寄存器编号地址。
MODBUS_LONG(0)占用 MODBUS_REG(0)与 MODBUS_REG(1)两个 REG 地址。 MODBUS_LONG(1)占用 MODBUS_REG(1)与 MODBUS_REG(2)两个 REG 地址。 MODBUS_IEEE(0)占用 MODBUS_REG(0)与 MODBUS_REG(1)两个 REG 地址。



MODBUS_IEEE(1)占用 MODBUS_REG(1)与 MODBUS_REG(2)两个 REG 地址。
所以要注意 MODBUS_REG,MODBUS_LONG,MODBUS_IEEE 地址在用户应用程序中不能重叠。
4X 空间示意图：

	LONG/IEEE 1
32BIT	
STRING 0 8BIT	STRING 1 8BIT	STRING 2 8BIT	STRING 3 8BIT	STRING 4 8BIT	STRING 5 8BIT	STRING 6 8BIT	STRING 7 8BIT
REG 0 16BIT	REG 1 16BIT	REG 2 16BIT	REG 3 16BIT
LONG/IEEE 0 32BIT	LONG/IEEE 2 32BIT


在串口设置（SETCOM 参数）过程中，寄存器选择为 VR 时，此时一个 VR 映射到一个 MODBUS_REG，其中 VR 是 32 位浮点型，MODBUS_REG 是 16 位有符号整数型，从 VR 传递数据给 MODBUS_REG 会丢失小数部分，当 VR 数据超过正负 15 位时，MODBUS_REG 数据会改变；MODBUS_REG 传递数据给 VR不会有问题，见如下例程，更多信息参见 SETCOM 指令。


当使用 MODBUS 协议与其他设备通讯时，就需要将数据放在 MODBUS 寄存器内进行传递，比如与触



摸屏通讯。不进行 MODBUS 通讯时，亦可将 MODBUS 寄存器作为控制器本地数组使用。
控制器直接从 MODBUS_BIT 地址 10000 开始与输入 IN 口对应，20000 与输出 OUT 口对应（注意读取的 IO 是原始的状态，INVERT_IN 反转输入指令不起作用），30000 与 PLC 编程的 S 寄存器对应。
MODBUS_IEEE 地址 10000 开始对应轴 DPOS 区间，11000 开始对应轴 MPOS 区间，12000 开始对应轴 VP_SPEED 区间；MODBUS_REG 的 13000 开始对应模拟量 DA 输出区间，14000 开始对应模拟量 AD输入区间。

4.3VR
VR 寄存器具有掉电存储功能，可无限次读写，但数据空间较小，一般只有 1024 或者更少，最新系列控制器的 VR 空间为 8000，用于保存需要不断修改的数据，例如轴参数、坐标等，数据类型为 32 位浮点型。可使用 VR_INT 强制保存为整型，VRSTRING 强制保存为字符串。VR、VR_INT、VRSTRING 共用一
个空间，地址空间是重叠的，VR 和 VR_INT 读写方法相同，VRSTRING 保存 ASCII 码，一个字符占用一个 VR。
VR 的掉电保存原理是控制器内部有缺电存储器，但数据容量较小，所以数据量较大的或需要长久保存的数据最好写到 FLASH 块或导出到 U 盘。



VR 寄存器还可用于 RTEX 控制器传递读写数据，DRIVE_WRITE 参数写入，DRIVE_READ 参数读取，具体使用方法参见第十六章总线相关的 RTEX 总线指令。
使用 CLEAR 指令清除 VR 内的全部数据，CLEAR_BIT 指令将 VR 某个位置 0，READ_BIT 指令读取
VR 寄存器的某个位数据，SET_BIT 指令将 VR 某个位置 1。

4.4FLASH
严格来讲，FLASH 不是寄存器，但它与寄存器密切相关，所以放于此章叙述。
FLASH 具有掉电存储功能，读写次数限制为十万次，长期不上电也不会丢失数据。一般用于存放较大的，不需要频繁读写的数据，比如加工的工艺文件。
读与写时要注意保证要操作的变量，数组等名称和次序高度一致，如果不一致会导致数据错乱。
FLASH 使用时是按块编号，块数 FLASH_SECTES 指令查看，不同的控制器 FLASH 块数与块数据大小都不同，每块数据大小 FLASH_SECTSIZE 指令查看。
可以在“在线命令”窗口发送？*max 打印查看，如下图。


CAN 通讯设置的参数，IP 地址、APP_PASS、LOCK 密码等系统参数存储到 FLASH。注意：FLASH 在读取之前先要写入，否则会提示警报 WARN。
FLASH 使用方法：


5外部输入信号	
5.1数字量 IN
输入/输出接口类型为 NPN 型，高速输入口频率 100kHz，普通输入口频率 5kHz，是否支持高速输入参见硬件手册描述，高速输入接口支持锁存功能。
注意：部分型号控制器 IO 需要接外部电源供电之后才能使用，需要接 IO 电源的控制器带 IOPOWER
指示灯。
使用 SCAN_EVENT 指令扫描外部输入 IN 的电平变化，或使用 IN 指令读取当前输入的电平状态。注意 IN 用在循环中的时候，可能会使循环条件执行多次。
特殊输入信号控制器本身没有做定义，需要用户使用指令去定义，如原点信号 DATUM_IN，正向硬限位 FWD_IN，负向硬限位 REV_IN，报警信号 ALM_IN，使用 ZMC 系列控制器时，由于 ZMC 输入是 OFF有效，故上述映射完成后，还需使用 INVERT_IN 指令反转输入口电平。
特殊输入信号取消需要将输入映射到-1，只删除程序中的代码是无法取消的，再次下载程序时，特殊输入信号仍会生效。


输入口的状态可以通过“工具”-“输入口”窗口读取，无输入显示红色，有输入显示绿色。但特殊输入定义经过反转之后，无输入显示绿色，有输入显示红色。


5.2模拟量 AD
使用 AIN 指令读取外部模拟量输入。控制器模拟量支持的电压范围 0-10V，12 位分辨率对应刻度 0-4095，16 位分辨率对应刻度 0-65536。


5.3编码器脉冲
根据编码器轴号，使用 MPOS 指令读取编码器脉冲输入，MPOS 的值等于编码器反馈脉冲数除以 UNITS
得到。
控制器面板上的脉冲 DB 头内就包含有编码器输入接口，部分控制器有独立的编码器接口。编码器的接线有单端接法和差分接法两种方式。
单端接法：


差分接法：


6控制信号输出	
6.1数字量 OUT
输入/输出接口类型为 NPN 型，光耦和滤波器的作用是防止干扰信号通过信号线和电源线进入控制器。高速输出口频率 400kHz，普通输出口频率 8kHz，是否支持高速输出参见硬件手册描述，高速输出接口支持 PWM、精准输出，PSO 功能。
注意：部分型号控制器 IO 需要接外部电源供电之后才能使用，需要接 IO 电源的控制器带 IOPOWER
指示灯。
使用OP，MOVE_OP，MOVE_OP2，PSWITCH，HW_PSWITCH，HW_PSWITCH2，PWM 等指令操作
数字量信号输出。
通用输出口电流 300mA，DB 头里的输出口电流 50mA。






6.2模拟量 DA
使用 AOUT 指令设置模拟量输出。控制器模拟量支持的电压范围 0-10V，12 位分辨率对应刻度 0-4095。


6.3电机脉冲
根据电机轴号，使用 MOVE 等一系列运动指令控制脉冲输出，运动指令查看《RTBasic 编程手册》相



关运动指令。
控制器支持脉冲模式：脉冲+方向、双脉冲、正交脉冲（4 系列及以上），默认脉冲+方向模式，设置指令：INVERT_STEP
输出脉冲数等于 MOVE 指令的参数乘以 UNITS。 脉冲输出与编码器类似，也有单端和差分两种接法。




7多任务编程	
7.1多任务概念
任务是执行 I/O 刷新和用户程序等一系列指令处理的功能，一个任务是指一个正在运行的程序。如果多个程序模块能够互不干扰的同时运行，则称为多任务，多任务编程在 RTSys 软件上实现。
多任务可以将复杂的程序分成几个部分，分别打开任务来同时执行，每个部分的任务是独立的，这样就可以使设备的复杂运动过程变得简单明了，编程更灵活，没有多任务场合的程序只能顺序执行，这样会使程序的执行效率十分低下。
ZMC 运动控制器支持多任务编程，每个任务都有自己唯一的编号，此编号没有优先级意义，只是标识当前程序属于哪一个任务。
不同型号支持的任务数有所不同，支持的具体任务数量，可连接控制器之后，在 RTSys 软件“控制器”
→ “控制器状态”查看或在线命令发送?*max 指令查看，如下图，表示该控制器最多支持 22 个任务，任务



编号范围为 0-21。




7.2多任务状态查看
任务状态有三种，正在运行、停止和暂停，任务状态查看有如下 3 种方式。


1.任务指令查看
PROC_STATUS：任务状态查看，只读参数。返回值：0-任务停止，1-任务正在运行，3-任务暂停中。

2.任务窗口查看
在菜单栏“调试”→“启动/停止调试”打开任务窗口，如下图。
任务窗口可以查看已经开启的任务的任务编号、运行状态、当前文件和运行行号，看不到未开启的任务。

Basic 的任务在程序扫描完成后，任务变为 Stopped 状态，PLC 主任务由于会循环扫描，所以一直处于 Running 状态。
3.打开菜单栏“工具”→“故障诊断”窗口。
可查看控制器的支持的所有任务编号的状态、所处的文件和运行的行号。此窗口也可以显示各任务报错的故障信息。


7.3多任务启动与停止
多任务主要操作指令如下：

指令	描述
END	当前任务正常结束
STOP	停止指定文件运行的任务
STOPTASK	停止指定任务
HALT	停止所有任务
RUN	启动新任务运行一个文件
RUNTASK	启动新任务运行一个 SUB 或者运行一个带标签的程序
PAUSETASK	暂停指定任务
RESUMETASK	恢复指定任务，恢复后任务从停止处继续往下执行

7.4任务启动
任务启动有三种方式，分别是自动运行任务号设置、RUN 指令和 RUNTASK 指令，使用指令开启任务时，程序扫描执行到该指令后再开启任务。



开启任务时注意任务编号的填写，任务不能重复开启。


1.自动运行任务号：在“文件视图”窗口设置自动运动任务号。控制器上电后首先执行带自动运行任务号的文件，自动运行任务号 Basic 文件可设置多个，PLC 文件和 HMI 文件仅支持一个。自动运行文件为并行运行，上电后同时开启。



2.RUN 指令将文件作为一个任务启动。

3.RUNTASK 指令将 SUB 子程序或带标签程序作为一个任务启动。可跨文件开启全局定义的 SUB 子程序，要开启任务的标签程序只能存在本文件内。


7.4.1任务停止
停止任务指令有 STOPTASK，STOP，HALT 三种。
任务停止再启动就会从头执行任务，不能从停止处开始。
开启任务时，一般先使用 STOPTASK 停止任务，再RUNTASK 开启，避免任务出现重复开启报错。对已经扫描完的程序发送停止任务命令是无效的。

1.STOPTASK 支持停止文件任务、SUB 子程序任务和带标签的任务。

2.STOP 指令支持停止Basic 文件任务，推荐使用 STOPTASK 指令，操作更简单。
3.HALT 指令停止所有任务

快速停止所有任务还可以使用软件菜单栏的“紧急停止”按钮。


发送在线命令：STOPTASK	1

命令发送后如下图，停止任务 1。

再次启动任务可以再次下载程序，或者使用任务指令开启。
注意：上方程序不能使用RUN 指令开启自动运行文件任务 0，因为任务 0 中自动开启的任务 1 仍在运行，若使用指令再次开启任务 0，会导致任务 1 重复开启。若停止的任务 1，则可以使用 RUNTASK 指令单独开启任务 1。
7.4.2任务暂停与恢复
暂停任务使用 PAUSETASK 指令，恢复任务使用 RESUMETASK 指令。恢复后任务从暂停处继续向下执行。暂停的任务支持停止。
暂停只对正在运动的任务有效，对已经运行完停止的任务发送暂停命令无效。


PAUSETASK：暂停指定任务

RESUMETASK：恢复指定任务


	ZMotion	正运动技术	


发送在线命令控制任务的暂停或恢复。在线命令发送：PAUSETASK	0


命令发送后如下图，任务 0 被暂停。

在线命令发送：RESUMETASK	0

命令发送后如下图，任务 0 恢复运行状态。




7.5多任务调用例程
程序下载运行，首先启动带自动运行任务号的文件，自动运行任务号可设置多个，也可以只设置一个，其他的文件任务采用 RUN 指令开启。
根据要调用的程序所处位置，在自动运行的文件中加入 RUN 或RUNTASK 指令调用其他任务执行。程序编程参考框架：编程时按功能进行模块划分，每个模块指定一个任务号运行，模块程序块在需要时
才调用任务执行，减少程序扫描时间，提高控制器的执行效率。
如下图任务窗口，示例程序分为两个 Basic 文件，Main 文件上电自动以任务 0 运行，其他任务在任务
0 中使用指令开启。


示例程序多任务调用的框架如下图。





'Basic 开启任务调用 Basic 文件
RUN "TuXing_001.bas",2	'以任务 2 运行主运动程序
ENDIF


IF	SCAN_EVENT(IN(1))> 0 THEN	'停止
sub_stop	'调用停止子函数
ENDIF


IF	SCAN_EVENT(IN(2))> 0 THEN	'回零
RUNTASK 1,task_home	'以任务 1 启动回零
ENDIF WEND
END	'主程序结束


'''***************************参数定义子函数***************************** GLOBAL SUB GlobalInit()
GLOBAL CONST AXISNUM = 3	'总轴数
GLOBAL g_state	'控制器状态
g_state = 0	'0--初始状态；1--待机；2--回零；3--运行


GLOBAL deal_home	'回零标志 deal_home = 0
END SUB


''' ********************轴参数以及 IO 的定义子函数************************** GLOBAL SUB AxesInit()
BASE(0,1,2) DPOS=0,0,0 MPOS=0,0,0
UNITS = 100,100,100	'脉冲当量
ATYPE = 1,1,1	'步进方式 SPEED=100,100,100
LSPEED=0,0,0	'起始速度
CREEP=10,10,10	'回零反找速度



ACCEL=1000,1000,1000 DECEL=1000,1000,1000
SRAMP = 20,20,20	'S 曲线时间设置


DATUM_IN=8,9,10		'原点输入配置 REV_IN=-1,-1,-1	'负向限位，与原点连到一起
FWD_IN=-1,-1,-1	'正向限位 ALM_IN = -1,-1,-1

'特殊 IO 反转改为常开输入 INVERT_IN(8,ON) INVERT_IN(9,ON) INVERT_IN(10,ON)

MERGE = ON	'缺省配置以主轴 0 进行的为连续插补
CORNER_MODE = 2	'启动拐角减速 DECEL_ANGLE = 15 * (PI/180)			'开始减速的角度 15 度 STOP_ANGLE = 45 * (PI/180)		'降到最低速度的角度 45 度
END SUB


''' ****************************轴回零子函数****************************** GLOBAL SUB task_home()
g_state = 2	'回零中


FOR i = 0 TO AXISNUM - 1
BASE (i)	'选择参与运动的轴
CANCEL(2)	'停止轴 WAIT IDLE
NEXT


FOR i=0 TO AXISNUM-1
SPEED(i)=50	 '回零速度 HOMEWAIT(i)=100	'反找等待时间 DATUM(3) AXIS(i)	'回零方式



示波器采样各轴位置曲线：示波器的使用方法参见软件帮助菜单“RTSys 帮助”。各轴位置曲线：

XY 合成轨迹：

	ZMotion	正运动技术	


8在线命令	
在线命令输入指令立即执行，不干扰程序文件的运行。
在线命令与输出窗口可以查询与输出控制器的各种参数、控制轴运动、打印程序运行结果、打印程序错误信息，软件开发人员在程序中给出的打印输出函数（由?、PRINT、WARN、ERROR、TRACE 等命令输出）。
?为 PRINT 的简写，WARN 为警告信息，ERROR 为错误信息，TRACE 打印信息。其中 WARN、ERROR、TRACE 等命令是否输出由 ERRSWITCH 指令控制。
语法：ERRSWITCH=switch switch：调试输出的开关


连接了控制器或仿真器就可以使用在线命令功能，不受程序运行状态的限制。“清除”按钮用以清空 “命令与输出”窗口的所有内容。

“捕获”开启后弹出“另存为”窗口，默认保存文件类型为 txt 文本文件，捕获开启后，原“捕获”按钮变为“捕获中”，将接下来命令与输出窗口输出的所有内容保存到 txt 文本文件里，直到再次按下“捕获中”按钮停止捕获保存信息。


除了发送指令语法之外，常用的打印查看命令有：

指令	描述
?*SET	打印所有参数值

?*TASK	打印任务信息，任务正常时只打印任务状态，任务出错时还会打印出错误任务号，具体
错误行



?*MAX	打印所有规格参数
?*FILE	打印程序文件信息
?*SETCOM	打印当前串口的配置信息
?*BASE	打印当前任务的 BASE 列表（140123 以后版本支持）
?*数组名	打印数组的所有元素，数组长度不能太长
?*参数名	打印一个所有轴的单个参数
?*ETHERCAT	打印EtherCAT 总线连接设置状态
?*RTEX	打印 RTEX 总线连接设置状态
?*FRAME	打印机械手参数，需要 161022 及以上固件支持
?*SLOT	打印出控制器槽位口信息（RTEX 口，EtherCAT 口）
?*PORT	打印所有 PORT 通讯口
