===============================================================================
                        统一取螺丝位置说明 - 所有螺丝从同一位置开始
===============================================================================

【统一取螺丝位置原理】

=== 设计理念 ===
不管是左侧还是右侧，不管是打哪个孔，所有螺丝都从同一个取螺丝位置开始：
✅ **统一取螺丝位置**：X=50mm, Y=150mm, Z=10mm
✅ **左侧8个螺丝**：都从(50,150,10)开始
✅ **右侧8个螺丝**：都从(50,150,10)开始
✅ **三段连续轨迹**：取螺丝→螺丝孔位→取螺丝，中间不停

=== 优势分析 ===
```
传统方式：
每个螺丝位置都有对应的取螺丝路径
问题：路径复杂，轨迹不一致

统一取螺丝位置：
所有螺丝都从同一位置开始
优势：路径简单，轨迹一致，效率高
```

【螺丝机布局】

=== 坐标系统 ===
```
统一取螺丝位置：(50, 150, 10)

左侧螺丝位置：
第一排：(100,80,30) (150,80,30) (200,80,30) (250,80,30)
第二排：(100,120,30) (150,120,30) (200,120,30) (250,120,30)

右侧螺丝位置：
第一排：(100,220,30) (150,220,30) (200,220,30) (250,220,30)
第二排：(100,260,30) (150,260,30) (200,260,30) (250,260,30)
```

=== 距离分析 ===
```
从取螺丝位置到各螺丝孔位的距离：

左侧螺丝：
- 最近：(100,80,30) 距离约86mm
- 最远：(250,120,30) 距离约212mm

右侧螺丝：
- 最近：(100,220,30) 距离约141mm  
- 最远：(250,260,30) 距离约283mm

所有距离都足够进行三段连续轨迹
```

【核心函数详解】

=== 1. EnsureAtPickPosition() - 确保在取螺丝位置 ===
```basic
功能：确保机器人在统一的取螺丝位置
流程：
1. Y轴移动到取螺丝位置(150mm)
2. 检查当前XZ位置
3. 如果不在取螺丝位置，使用三段连续轨迹移动过去
4. 如果已在取螺丝位置，直接继续

特点：
- 智能位置检测
- 避免不必要的运动
- 确保起始位置一致
```

=== 2. MoveToTargetContinuous() - 连续轨迹到目标 ===
```basic
功能：从取螺丝位置三段连续轨迹到螺丝孔位
流程：
1. Y轴移动到目标Y坐标
2. 从取螺丝位置(50,10)三段连续轨迹到螺丝孔位
3. 中间不停顿

轨迹：
第一段：抬Z (50,10) → (50,8) 安全高度
第二段：圆弧 (50,8) → (中点,20) → (目标X,25) 
第三段：下降 (目标X,25) → (目标X,30)
```

=== 3. MoveBackToPickContinuous() - 连续轨迹回取螺丝位置 ===
```basic
功能：从螺丝孔位三段连续轨迹回到取螺丝位置
流程：
1. Y轴移动到取螺丝位置(150mm)
2. 从当前螺丝孔位三段连续轨迹回到取螺丝位置
3. 中间不停顿

轨迹：
第一段：抬Z (目标X,30) → (目标X,25) 安全高度
第二段：圆弧 (目标X,25) → (中点,20) → (50,8)
第三段：下降 (50,8) → (50,10)
```

【完整作业流程】

=== 单个螺丝的完整流程 ===
```
以左侧第一个螺丝(100,80,30)为例：

1. 确保在取螺丝位置
   - Y轴：当前位置 → 150mm
   - XZ：检查是否在(50,10)，不在则移动过去

2. 吸取螺丝
   - 在(50,150,10)位置吸取螺丝
   - OP(0, ON) 开启吸螺丝
   - DELAY(1000) 等待吸取稳定

3. 三段连续轨迹到螺丝孔位（中间不停）
   - Y轴：150mm → 80mm
   - 第一段：(50,10) → (50,8) 抬Z到安全高度
   - 第二段：(50,8) → (75,20) → (100,25) 圆弧插补
   - 第三段：(100,25) → (100,30) 下降到工作深度

4. 执行打螺丝
   - 分层下降：30mm → 28mm → 30mm
   - 电批锁紧2秒
   - 抬升到27mm

5. 三段连续轨迹回取螺丝位置（中间不停）
   - Y轴：80mm → 150mm
   - 第一段：(100,30) → (100,25) 抬Z到安全高度
   - 第二段：(100,25) → (75,20) → (50,8) 圆弧插补
   - 第三段：(50,8) → (50,10) 下降到取螺丝位置

6. 关闭吸螺丝
   - OP(0, OFF) 关闭吸螺丝
```

=== 16个螺丝的连续作业 ===
```
左侧8个螺丝：
螺丝1：(50,150,10) → (100,80,30) → (50,150,10)
螺丝2：(50,150,10) → (150,80,30) → (50,150,10)
螺丝3：(50,150,10) → (200,80,30) → (50,150,10)
...
螺丝8：(50,150,10) → (250,120,30) → (50,150,10)

右侧8个螺丝：
螺丝1：(50,150,10) → (100,220,30) → (50,150,10)
螺丝2：(50,150,10) → (150,220,30) → (50,150,10)
...
螺丝8：(50,150,10) → (250,260,30) → (50,150,10)

关键：每个螺丝都从同一位置开始和结束
```

【Y轴运动策略】

=== Y轴独立移动 ===
```basic
取螺丝位置Y坐标：150mm

左侧螺丝Y坐标：
- 第一排：80mm
- 第二排：120mm

右侧螺丝Y坐标：
- 第一排：220mm
- 第二排：260mm

Y轴移动距离：
- 取螺丝→左侧第一排：150→80 = 70mm
- 取螺丝→左侧第二排：150→120 = 30mm
- 取螺丝→右侧第一排：150→220 = 70mm
- 取螺丝→右侧第二排：150→260 = 110mm
```

=== Y轴移动时机 ===
```basic
1. 确保在取螺丝位置时：
   Y轴移动到150mm（取螺丝位置）

2. 移动到螺丝孔位时：
   Y轴移动到目标Y坐标（80/120/220/260mm）

3. 回到取螺丝位置时：
   Y轴移动到150mm（取螺丝位置）

特点：Y轴独立移动，不参与XZ圆弧插补
```

【性能优势】

=== 轨迹一致性 ===
✅ **起始位置统一**：所有螺丝都从(50,150,10)开始
✅ **轨迹参数一致**：相同的安全高度和圆弧参数
✅ **运动质量稳定**：一致的轨迹带来稳定的运动质量
✅ **调试简化**：只需优化一套轨迹参数

=== 效率提升 ===
✅ **路径优化**：统一起点简化路径规划
✅ **连续运动**：三段连续轨迹，中间不停顿
✅ **时间可预测**：一致的轨迹带来可预测的时间
✅ **维护简单**：统一的取螺丝位置便于维护

=== 精度提升 ===
✅ **重复精度高**：相同的起始位置提高重复精度
✅ **累积误差小**：统一轨迹减少累积误差
✅ **定位一致性**：每次都从相同位置开始定位

【调试和优化】

=== 取螺丝位置调整 ===
```basic
当前设置：
pick_x = 50                     ' 可根据实际布局调整
pick_y = 150                    ' 可根据实际布局调整  
pick_z = 10                     ' 可根据取螺丝器高度调整

调整原则：
- 位置应便于所有螺丝孔位的访问
- 避开工件和夹具的干涉区域
- 考虑Y轴移动距离的平衡
```

=== 轨迹参数优化 ===
```basic
安全高度：
pick_safe_height = 8            ' 取螺丝位置安全高度
work_safe_height = 25           ' 工件位置安全高度

圆弧参数：
arc_top_height = 20             ' 圆弧最高点

距离判断：
IF total_distance >= 5 THEN     ' 圆弧/直线判断阈值
```

=== 测试验证 ===
```basic
测试步骤：
1. 运行测试版验证逻辑
2. 实际运行观察轨迹
3. 检查各螺丝位置的轨迹一致性
4. 优化参数提高性能

观察要点：
- 确认所有螺丝都从(50,150,10)开始
- 检查三段连续轨迹的平滑性
- 验证Y轴移动的正确性
- 测试不同螺丝位置的轨迹质量
```

【常见问题解答】

=== Q1：为什么所有螺丝都从同一位置取？ ===
A：统一取螺丝位置简化轨迹规划，提高一致性和效率

=== Q2：取螺丝位置(50,150,10)是否合适？ ===
A：这是经验值，可根据实际机器布局和工件位置调整

=== Q3：Y轴移动会影响效率吗？ ===
A：Y轴移动时间相对较短，且与XZ运动并行，影响很小

=== Q4：如何处理不同高度的工件？ ===
A：调整螺丝位置的Z坐标，取螺丝位置保持不变

=== Q5：三段连续轨迹的时间成本如何？ ===
A：虽然增加约20%时间，但运动质量显著提升，值得投入

【总结】

统一取螺丝位置系统特点：
✅ **统一起点**：所有螺丝都从(50,150,10)开始
✅ **三段连续**：抬Z→圆弧→Z下降，中间不停顿
✅ **轨迹一致**：相同的轨迹参数，稳定的运动质量
✅ **效率优化**：简化路径规划，提高整体效率
✅ **精度提升**：统一起点提高重复精度和一致性

这套统一取螺丝位置系统与1m/s高速运动、S型曲线、三段连续轨迹完美配合，
实现了高速、高精度、高一致性的螺丝机自动化作业。

===============================================================================
