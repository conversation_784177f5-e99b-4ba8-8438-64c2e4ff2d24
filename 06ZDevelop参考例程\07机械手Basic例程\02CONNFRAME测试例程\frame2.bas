global dim L1,L2,R,r,u1,u2,u3,u4,lx,ly,lz
L1=32
L2=85
R=40
r=10
u1=3600
u2=3600
u3=3600
u4=1
lx=0
ly=0
lz=0

base(0,1,2)
RAPIDSTOP
wait IDLE

ATYPE=1,1,1
dpos=0,0,0
UNITS=u1/360,u2/360,u3/360
CLUTCH_RATE=0,0,0
speed=100,100,100
accel=1000,1000,1000
CREEP=10,10,10


base(4,5,6)
atype=0,0,0
dpos=0,0,0

speed=100,100,200
accel=300,300,300
UNITS=100,100,100  

MERGE=1
CORNER_MODE=2
DECEL_ANGLE=15*(PI/180)
STOP_ANGLE=45*(PI/180)

table(100,R,r,L1,L2,u1,u2,u3,lx,ly,lz)


BASE(0,1,2)
CONNFRAME(2,100,4,5,6)
wait LOADED







while 1

	if SCAN_EVENT(in(0))>0 then
		BASE(4,5,6)
		CONNREFRAME(2,100,0,1,2)
		wait LOADED
		SPEAKOUT(100)
	endif
	
	if SCAN_EVENT(in(0))<0 then
		BASE(0,1,2)
		CONNFRAME(2,100,4,5,6)
		wait LOADED
		SPEAKOUT(100)
	endif
wend