?2

   
'       While 1
'             global dim c
'              c = scan_event(in(0))
'
'              If c  > 0 then
'			          ?c
'
'                     Print " in(0)  on"
'					 delay(1000)
'					 ?c					 
'
'              Elseif c <0 then
'
'                     Print "in(0) off"
'					 delay(1000)
'
'              End if
'              ?(c+1)
'       Wend
	   
   while 1

     if in_scan(0,23) then

         if in_event(0) > 0 then
           print "in0 up", in_buff(0)
         elseif in_event(0) < 0 then
           print "in0 down", in_buff(0)
        endif

     endif

   wend

