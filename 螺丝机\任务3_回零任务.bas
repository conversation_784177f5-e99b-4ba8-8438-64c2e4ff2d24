'=============================================================================
' 任务3：回零任务
' 由主控制任务通过RUNTASK 3, HomeTask启动
' 独立运行，不阻塞主控制任务的输入扫描
'=============================================================================

'任务3主程序
CALL HomeTask()
END

'================ 回零任务 ================
GLOBAL SUB HomeTask()
    sys_status = 2                  ' 设置为回零状态
    PRINT "开始四轴回零..."
    
    '按Z-Y-X顺序回零，避免碰撞
    FOR i = 3 TO 0 STEP -1
        PRINT "开始轴", i, "回零"
        axis_home(i) = 1            ' 设置为回零中
        
        BASE(i)
        DATUM(0) AXIS(i)            ' 清除错误状态
        DELAY(10)
        DATUM(3)                    ' 正向找原点回零
        
        WAIT UNTIL IDLE(i) = -1
        DELAY(10)
        
        IF AXISSTATUS(i) = 0 THEN
            axis_home(i) = 2        ' 回零成功
            PRINT "轴", i, "回零成功"
        ELSE
            axis_home(i) = 3        ' 回零失败
            PRINT "轴", i, "回零失败，状态：", HEX(AXISSTATUS(i))
            RETURN                  ' 回零失败，任务结束
        ENDIF
        
        DELAY(500)                  ' 轴间延时
    NEXT
    
    '回零完成后，双Y轴移动到用户位置
    PRINT "双Y轴移动到用户位置..."
    CALL LeftSlideToUser()
    CALL RightSlideToUser()
    
    PRINT "所有轴回零完成，双Y轴在用户位置"
    '任务结束，sys_status将在主控制任务的UpdateTaskStatus中设置为0
END SUB

'================ 双Y轴滑轨控制 ================
GLOBAL SUB LeftSlideToUser()
    IF left_slide_status <> 0 THEN
        PRINT "左Y轴移动到用户位置..."
        left_slide_status = 2       ' 设置为移动中
        
        BASE(1)
        MOVEABS(left_user_pos) AXIS(1)
        WAIT IDLE(1)
        
        left_slide_status = 0       ' 设置为用户侧
        PRINT "左Y轴已到达用户位置：", left_user_pos, "mm"
    ENDIF
END SUB

GLOBAL SUB RightSlideToUser()
    IF right_slide_status <> 0 THEN
        PRINT "右Y轴移动到用户位置..."
        right_slide_status = 2      ' 设置为移动中
        
        BASE(2)
        MOVEABS(right_user_pos) AXIS(2)
        WAIT IDLE(2)
        
        right_slide_status = 0      ' 设置为用户侧
        PRINT "右Y轴已到达用户位置：", right_user_pos, "mm"
    ENDIF
END SUB
