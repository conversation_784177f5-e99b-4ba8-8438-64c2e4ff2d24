'=============================================================================
' 螺丝机增强版（增加滑轨控制和队列功能）
' 新增功能：
' 1. 滑轨控制：空闲时在用户侧，作业时在电批侧
' 2. 队列控制：支持电批忙碌时排队等待
' 3. 滑轨螺距：10mm/圈，电机转一圈滑块移动10mm
'=============================================================================

'全局变量定义
GLOBAL sys_status                  ' 系统状态：0-待机，1-运行中，2-回零中
GLOBAL axis_home(4)                ' 各轴回零状态：0-未回零，1-回零中，2-已回零，3-失败
GLOBAL left_screw_num              ' 左侧螺丝点位数量
GLOBAL right_screw_num             ' 右侧螺丝点位数量
GLOBAL cur_screw                   ' 当前打螺丝序号

'滑轨控制变量
GLOBAL slide_axis = 4               ' 滑轨轴号（第5轴）
GLOBAL slide_user_pos = 0           ' 用户侧位置（原点位置）
GLOBAL slide_work_pos = 100         ' 工作侧位置（电批侧，100mm）
GLOBAL slide_status = 0             ' 滑轨状态：0-用户侧，1-工作侧，2-移动中

'队列控制变量
GLOBAL left_queue = 0               ' 左侧队列状态：0-无任务，1-等待中，2-执行中
GLOBAL right_queue = 0              ' 右侧队列状态：0-无任务，1-等待中，2-执行中
GLOBAL screwdriver_busy = 0         ' 电批忙碌状态：0-空闲，1-忙碌

'螺丝位置数据存储
GLOBAL left_start = 0               ' 左侧螺丝位置数据起始地址
GLOBAL right_start = 100            ' 右侧螺丝位置数据起始地址

'吸螺丝位置
GLOBAL pick_x = 50                  ' 吸螺丝位置X
GLOBAL pick_y = 150                 ' 吸螺丝位置Y  
GLOBAL pick_z = 5                   ' 吸螺丝位置Z

'主程序
CALL InitSystem()
CALL SetupAxis()
CALL SetupData()

PRINT "=== 螺丝机增强版启动 ==="
PRINT "新增功能："
PRINT "1. 滑轨自动控制（用户侧<->电批侧）"
PRINT "2. 队列控制（支持排队等待）"
PRINT ""
PRINT "操作说明："
PRINT "IN0 - 左侧开始（滑块自动移动到电批侧）"
PRINT "IN1 - 右侧开始（滑块自动移动到电批侧）"
PRINT "IN2 - 系统回零"
PRINT "IN3 - 急停"
PRINT "IN4 - 手动滑块到用户侧"
PRINT "IN5 - 手动滑块到电批侧"
PRINT "OP0 - 吸螺丝控制"

'主循环
WHILE 1
    CALL ScanInput()
    CALL ProcessQueue()
    CALL UpdateStatus()
    DELAY(50)
WEND
END

'================ 系统初始化 ================
GLOBAL SUB InitSystem()
    sys_status = 0
    left_screw_num = 4              ' 默认左侧4个螺丝
    right_screw_num = 4             ' 默认右侧4个螺丝
    cur_screw = 0
    
    '初始化回零状态（包含滑轨轴）
    FOR i = 0 TO 4
        axis_home(i) = 0
    NEXT
    
    '初始化队列状态
    left_queue = 0
    right_queue = 0
    screwdriver_busy = 0
    slide_status = 0
    
    PRINT "系统初始化完成"
END SUB

'================ 轴参数设置 ================
GLOBAL SUB SetupAxis()
    '五轴配置：X轴(0), Y1轴(1), Y2轴(2), Z轴(3), 滑轨轴(4)
    BASE(0, 1, 2, 3, 4)
    ATYPE = 1, 1, 1, 1, 1           ' 步进电机开环脉冲控制
    UNITS = 1000, 1000, 1000, 1000, 100  ' 脉冲当量，滑轨100脉冲/mm（10mm螺距）
    SPEED = 100, 100, 100, 50, 80   ' 运动速度，滑轨80mm/s
    ACCEL = 1000, 1000, 1000, 500, 800   ' 加速度
    DECEL = 1000, 1000, 1000, 500, 800   ' 减速度
    CREEP = 10, 10, 10, 5, 10       ' 回零爬行速度
    
    '限位和原点信号配置
    FWD_IN = 8, 9, 10, 11, 12      ' 正限位 IN8-IN12
    REV_IN = 13, 14, 15, 16, 17    ' 负限位 IN13-IN17
    DATUM_IN = 18, 19, 20, 21, 22  ' 原点开关 IN18-IN22
    
    '信号反转(根据实际硬件调整)
    FOR i = 8 TO 22
        INVERT_IN(i, ON)            ' 常开信号反转
    NEXT
    
    '连续插补设置
    MERGE = ON                      ' 开启连续插补
    CORNER_MODE = 2                 ' 拐角减速模式
    
    PRINT "轴参数设置完成（包含滑轨轴）"
END SUB

'================ 数据设置 ================
GLOBAL SUB SetupData()
    '左侧螺丝位置数据(2x2阵列示例)
    TABLE(0) = 100
    TABLE(1) = 80
    TABLE(2) = 0
    TABLE(3) = 150
    TABLE(4) = 80
    TABLE(5) = 0
    TABLE(6) = 100
    TABLE(7) = 120
    TABLE(8) = 0
    TABLE(9) = 150
    TABLE(10) = 120
    TABLE(11) = 0
    
    '右侧螺丝位置数据(2x2阵列示例)
    TABLE(100) = 100
    TABLE(101) = 220
    TABLE(102) = 0
    TABLE(103) = 150
    TABLE(104) = 220
    TABLE(105) = 0
    TABLE(106) = 100
    TABLE(107) = 260
    TABLE(108) = 0
    TABLE(109) = 150
    TABLE(110) = 260
    TABLE(111) = 0
    
    PRINT "数据设置完成"
    PRINT "左侧螺丝数量：", left_screw_num
    PRINT "右侧螺丝数量：", right_screw_num
    PRINT "滑轨用户侧位置：", slide_user_pos, "mm"
    PRINT "滑轨工作侧位置：", slide_work_pos, "mm"
END SUB

'================ 输入扫描 ================
GLOBAL SUB ScanInput()
    '左侧开始按键
    IF SCAN_EVENT(IN(0)) > 0 THEN
        IF CheckHome() = 1 THEN
            PRINT "左侧作业请求"
            IF left_queue = 0 THEN
                left_queue = 1          ' 设置为等待状态
                PRINT "左侧任务加入队列"
                CALL SlideToWork()      ' 滑块移动到工作侧
            ELSE
                PRINT "左侧任务已在队列中"
            ENDIF
        ELSE
            PRINT "系统未回零，无法开始作业"
        ENDIF
    ENDIF
    
    '右侧开始按键  
    IF SCAN_EVENT(IN(1)) > 0 THEN
        IF CheckHome() = 1 THEN
            PRINT "右侧作业请求"
            IF right_queue = 0 THEN
                right_queue = 1         ' 设置为等待状态
                PRINT "右侧任务加入队列"
                CALL SlideToWork()      ' 滑块移动到工作侧
            ELSE
                PRINT "右侧任务已在队列中"
            ENDIF
        ELSE
            PRINT "系统未回零，无法开始作业"
        ENDIF
    ENDIF
    
    '系统回零
    IF SCAN_EVENT(IN(2)) > 0 THEN
        IF sys_status = 0 THEN
            PRINT "开始系统回零（包含滑轨）"
            CALL WorkHome()
        ELSE
            PRINT "系统运行中，无法回零"
        ENDIF
    ENDIF
    
    '手动滑块到用户侧
    IF SCAN_EVENT(IN(4)) > 0 THEN
        IF sys_status = 0 THEN
            PRINT "手动滑块到用户侧"
            CALL SlideToUser()
        ELSE
            PRINT "系统运行中，无法手动控制滑块"
        ENDIF
    ENDIF
    
    '手动滑块到电批侧
    IF SCAN_EVENT(IN(5)) > 0 THEN
        IF sys_status = 0 THEN
            PRINT "手动滑块到电批侧"
            CALL SlideToWork()
        ELSE
            PRINT "系统运行中，无法手动控制滑块"
        ENDIF
    ENDIF
    
    '急停
    IF SCAN_EVENT(IN(3)) > 0 THEN
        PRINT "急停触发！"
        RAPIDSTOP(2)
        sys_status = 0
        left_queue = 0
        right_queue = 0
        screwdriver_busy = 0
        OP(0, OFF)                  ' 关闭吸螺丝
    ENDIF
END SUB

'================ 队列处理 ================
GLOBAL SUB ProcessQueue()
    '检查电批是否空闲
    IF screwdriver_busy = 0 THEN
        '电批空闲，检查是否有等待的任务
        IF left_queue = 1 AND slide_status = 1 THEN
            '左侧任务等待中且滑块在工作侧
            PRINT "开始执行左侧任务"
            left_queue = 2          ' 设置为执行中
            screwdriver_busy = 1    ' 电批设为忙碌
            CALL WorkLeft()
        ELSEIF right_queue = 1 AND slide_status = 1 THEN
            '右侧任务等待中且滑块在工作侧
            PRINT "开始执行右侧任务"
            right_queue = 2         ' 设置为执行中
            screwdriver_busy = 1    ' 电批设为忙碌
            CALL WorkRight()
        ENDIF
    ENDIF
END SUB

'================ 滑轨控制 ================
GLOBAL SUB SlideToUser()
    IF slide_status <> 0 THEN
        PRINT "滑块移动到用户侧..."
        slide_status = 2            ' 设置为移动中
        
        BASE(slide_axis)
        MOVEABS(slide_user_pos) AXIS(slide_axis)
        WAIT IDLE(slide_axis)
        
        slide_status = 0            ' 设置为用户侧
        PRINT "滑块已到达用户侧"
    ENDIF
END SUB

GLOBAL SUB SlideToWork()
    IF slide_status <> 1 THEN
        PRINT "滑块移动到工作侧..."
        slide_status = 2            ' 设置为移动中
        
        BASE(slide_axis)
        MOVEABS(slide_work_pos) AXIS(slide_axis)
        WAIT IDLE(slide_axis)
        
        slide_status = 1            ' 设置为工作侧
        PRINT "滑块已到达工作侧"
    ENDIF
END SUB

'================ 检查轴回零状态 ================
GLOBAL SUB CheckHome()
    FOR i = 0 TO 4                  ' 包含滑轨轴
        IF axis_home(i) <> 2 THEN
            RETURN 0                ' 有轴未回零
        ENDIF
    NEXT
    RETURN 1                        ' 所有轴已回零
END SUB

'================ 状态更新 ================
GLOBAL SUB UpdateStatus()
    '更新Modbus寄存器供HMI显示
    MODBUS_REG(0) = sys_status
    MODBUS_REG(1) = left_screw_num
    MODBUS_REG(2) = right_screw_num
    MODBUS_REG(3) = cur_screw
    MODBUS_REG(4) = slide_status
    MODBUS_REG(5) = left_queue
    MODBUS_REG(6) = right_queue
    MODBUS_REG(7) = screwdriver_busy

    '更新各轴回零状态
    FOR i = 0 TO 4
        MODBUS_REG(10 + i) = axis_home(i)
    NEXT

    '更新各轴当前位置
    FOR i = 0 TO 4
        MODBUS_IEEE(20 + i) = DPOS(i)
    NEXT
END SUB

'================ 回零作业 ================
GLOBAL SUB WorkHome()
    sys_status = 2                  ' 设置为回零状态
    PRINT "开始五轴回零..."

    '按Z-滑轨-Y-X顺序回零，避免碰撞
    DIM home_order(5)
    home_order(0) = 3               ' Z轴先回零
    home_order(1) = 4               ' 滑轨轴
    home_order(2) = 2               ' Y2轴
    home_order(3) = 1               ' Y1轴
    home_order(4) = 0               ' X轴

    FOR idx = 0 TO 4
        DIM i
        i = home_order(idx)
        PRINT "开始轴", i, "回零"
        axis_home(i) = 1            ' 设置为回零中

        BASE(i)
        DATUM(0) AXIS(i)            ' 清除错误状态
        DELAY(10)
        DATUM(3)                    ' 正向找原点回零

        WAIT UNTIL IDLE(i) = -1
        DELAY(10)

        IF AXISSTATUS(i) = 0 THEN
            axis_home(i) = 2        ' 回零成功
            PRINT "轴", i, "回零成功"
        ELSE
            axis_home(i) = 3        ' 回零失败
            PRINT "轴", i, "回零失败，状态：", HEX(AXISSTATUS(i))
            sys_status = 0
            RETURN
        ENDIF

        DELAY(500)                  ' 轴间延时
    NEXT

    '回零完成后，滑块移动到用户侧
    slide_status = 0                ' 设置滑块在用户侧
    PRINT "所有轴回零完成，滑块在用户侧"
    sys_status = 0                  ' 回到待机状态
END SUB

'================ 左侧作业 ================
GLOBAL SUB WorkLeft()
    PRINT "执行左侧打螺丝任务"

    FOR screw_idx = 0 TO left_screw_num - 1
        cur_screw = screw_idx + 1
        PRINT "开始打第", cur_screw, "个螺丝（左侧）"

        '获取螺丝位置
        DIM screw_x, screw_y, screw_z
        screw_x = TABLE(left_start + screw_idx * 3)
        screw_y = TABLE(left_start + screw_idx * 3 + 1)
        screw_z = TABLE(left_start + screw_idx * 3 + 2)

        '执行打螺丝流程
        CALL WorkScrew(screw_x, screw_y, screw_z, 1)  ' 1表示左侧Y1轴

        DELAY(500)                  ' 螺丝间延时
    NEXT

    PRINT "左侧打螺丝任务完成"

    '任务完成后的处理
    left_queue = 0                  ' 清除队列状态
    screwdriver_busy = 0            ' 电批设为空闲
    cur_screw = 0

    '滑块回到用户侧
    CALL SlideToUser()
    PRINT "左侧任务完成，滑块已回到用户侧"
END SUB

'================ 右侧作业 ================
GLOBAL SUB WorkRight()
    PRINT "执行右侧打螺丝任务"

    FOR screw_idx = 0 TO right_screw_num - 1
        cur_screw = screw_idx + 1
        PRINT "开始打第", cur_screw, "个螺丝（右侧）"

        '获取螺丝位置
        DIM screw_x, screw_y, screw_z
        screw_x = TABLE(right_start + screw_idx * 3)
        screw_y = TABLE(right_start + screw_idx * 3 + 1)
        screw_z = TABLE(right_start + screw_idx * 3 + 2)

        '执行打螺丝流程
        CALL WorkScrew(screw_x, screw_y, screw_z, 2)  ' 2表示右侧Y2轴

        DELAY(500)                  ' 螺丝间延时
    NEXT

    PRINT "右侧打螺丝任务完成"

    '任务完成后的处理
    right_queue = 0                 ' 清除队列状态
    screwdriver_busy = 0            ' 电批设为空闲
    cur_screw = 0

    '滑块回到用户侧
    CALL SlideToUser()
    PRINT "右侧任务完成，滑块已回到用户侧"
END SUB

'================ 打螺丝作业流程 ================
GLOBAL SUB WorkScrew(target_x, target_y, target_z, y_axis)
    PRINT "目标位置：X=", target_x, " Y=", target_y, " Z=", target_z

    '1. 移动到吸螺丝位置
    PRINT "移动到吸螺丝位置"
    CALL MoveToPick(y_axis)

    '2. 吸取螺丝
    PRINT "吸取螺丝"
    OP(0, ON)                       ' 开启吸螺丝
    DELAY(1000)                     ' 等待吸取稳定

    '3. 直线运动到螺丝孔位
    PRINT "直线运动到螺丝孔位"
    CALL LinearMoveToTarget(target_x, target_y, target_z, y_axis)

    '4. 执行打螺丝
    PRINT "执行打螺丝"
    CALL DoScrew(target_z)

    '5. 直线运动回到吸螺丝位置
    PRINT "直线运动回到吸螺丝位置"
    CALL LinearMoveBack(y_axis)

    '6. 关闭吸螺丝
    OP(0, OFF)                      ' 关闭吸螺丝

    PRINT "螺丝完成"
END SUB

'================ 移动到吸螺丝位置 ================
GLOBAL SUB MoveToPick(y_axis)
    BASE(0, y_axis, 3)              ' X, Y, Z轴

    '先抬升Z轴到安全高度
    MOVEABS(20) AXIS(3)
    WAIT IDLE(3)

    '移动到吸螺丝位置（分别移动）
    MOVEABS(pick_x) AXIS(0)
    MOVEABS(pick_y) AXIS(y_axis)
    WAIT IDLE(0)
    WAIT IDLE(y_axis)

    '下降到吸螺丝高度
    MOVEABS(pick_z) AXIS(3)
    WAIT IDLE(3)
END SUB

'================ 直线运动到目标位置 ================
GLOBAL SUB LinearMoveToTarget(target_x, target_y, target_z, y_axis)
    BASE(0, y_axis, 3)              ' X, Y, Z轴

    '先抬升到安全高度
    MOVEABS(20) AXIS(3)
    WAIT IDLE(3)

    '移动到目标XY位置
    MOVEABS(target_x) AXIS(0)
    MOVEABS(target_y) AXIS(y_axis)
    WAIT IDLE(0)
    WAIT IDLE(y_axis)

    '下降到螺丝孔位高度
    MOVEABS(target_z) AXIS(3)
    WAIT IDLE(3)
END SUB

'================ 直线运动回到吸螺丝位置 ================
GLOBAL SUB LinearMoveBack(y_axis)
    BASE(0, y_axis, 3)              ' X, Y, Z轴

    '先抬升到安全高度
    MOVEABS(20) AXIS(3)
    WAIT IDLE(3)

    '移动回吸螺丝位置
    MOVEABS(pick_x) AXIS(0)
    MOVEABS(pick_y) AXIS(y_axis)
    WAIT IDLE(0)
    WAIT IDLE(y_axis)

    '下降到吸螺丝高度
    MOVEABS(pick_z) AXIS(3)
    WAIT IDLE(3)
END SUB

'================ 执行打螺丝 ================
GLOBAL SUB DoScrew(target_z)
    '下降到接触位置
    MOVEABS(target_z - 5) AXIS(3)   ' 下降到螺丝孔上方5mm
    WAIT IDLE(3)

    '模拟电批锁紧
    PRINT "模拟电批锁紧..."
    DELAY(2000)                     ' 模拟锁紧时间
    PRINT "电批锁紧完成"

    '抬升Z轴
    MOVEABS(target_z + 10) AXIS(3)
    WAIT IDLE(3)
END SUB

'================ 显示系统状态 ================
GLOBAL SUB ShowStatus()
    PRINT "=== 系统状态 ==="
    PRINT "系统状态：", sys_status, " (0-待机,1-运行,2-回零)"
    PRINT "当前螺丝：", cur_screw
    PRINT "左侧螺丝数量：", left_screw_num
    PRINT "右侧螺丝数量：", right_screw_num

    PRINT "=== 滑轨状态 ==="
    PRINT "滑轨状态：", slide_status, " (0-用户侧,1-工作侧,2-移动中)"
    PRINT "滑轨位置：", DPOS(slide_axis), " mm"

    PRINT "=== 队列状态 ==="
    PRINT "左侧队列：", left_queue, " (0-无任务,1-等待,2-执行中)"
    PRINT "右侧队列：", right_queue, " (0-无任务,1-等待,2-执行中)"
    PRINT "电批状态：", screwdriver_busy, " (0-空闲,1-忙碌)"

    PRINT "=== 轴回零状态 ==="
    FOR i = 0 TO 4
        PRINT "轴", i, "：", axis_home(i), " (0-未回零,1-回零中,2-已回零,3-失败)"
    NEXT

    PRINT "=== 轴当前位置 ==="
    FOR i = 0 TO 4
        PRINT "轴", i, "：", DPOS(i), " mm"
    NEXT
END SUB

'================ 设置滑轨位置 ================
GLOBAL SUB SetSlidePos(user_pos, work_pos)
    slide_user_pos = user_pos
    slide_work_pos = work_pos
    PRINT "设置滑轨位置："
    PRINT "用户侧：", slide_user_pos, " mm"
    PRINT "工作侧：", slide_work_pos, " mm"
END SUB

'================ 测试滑轨 ================
GLOBAL SUB TestSlide()
    IF CheckHome() = 0 THEN
        PRINT "错误：轴未回零，无法测试滑轨"
        RETURN
    ENDIF

    PRINT "测试滑轨运动"

    PRINT "滑轨移动到工作侧..."
    CALL SlideToWork()
    DELAY(2000)

    PRINT "滑轨移动到用户侧..."
    CALL SlideToUser()
    DELAY(2000)

    PRINT "滑轨测试完成"
END SUB

'================ 测试队列功能 ================
GLOBAL SUB TestQueue()
    PRINT "测试队列功能"

    '模拟左侧任务请求
    IF left_queue = 0 THEN
        left_queue = 1
        PRINT "模拟左侧任务加入队列"
        CALL SlideToWork()
    ENDIF

    '模拟右侧任务请求
    IF right_queue = 0 THEN
        right_queue = 1
        PRINT "模拟右侧任务加入队列"
    ENDIF

    PRINT "队列测试完成，等待ProcessQueue()处理"
END SUB

'================ 清除队列 ================
GLOBAL SUB ClearQueue()
    left_queue = 0
    right_queue = 0
    screwdriver_busy = 0
    PRINT "所有队列已清除"
END SUB

'================ 设置螺丝位置 ================
GLOBAL SUB SetScrewPos(side, screw_num, pos_x, pos_y, pos_z)
    DIM base_addr, index

    IF side = 1 THEN
        base_addr = left_start
    ELSEIF side = 2 THEN
        base_addr = right_start
    ELSE
        PRINT "错误：无效的侧边参数"
        RETURN
    ENDIF

    index = base_addr + (screw_num - 1) * 3
    TABLE(index) = pos_x
    TABLE(index + 1) = pos_y
    TABLE(index + 2) = pos_z

    PRINT "设置螺丝位置：侧边=", side, " 编号=", screw_num
    PRINT "位置：X=", pos_x, " Y=", pos_y, " Z=", pos_z
END SUB
