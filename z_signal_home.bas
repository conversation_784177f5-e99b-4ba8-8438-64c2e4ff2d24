'=============================================================================
' Z信号回零专用示例程序
' 适用于带Z信号的编码器回零
' 支持模式1、2、5、6四种Z信号回零模式
'=============================================================================

' 全局变量
GLOBAL total_axis            ' 轴数量
GLOBAL z_home_status(3)      ' 各轴Z信号回零状态

' 主程序
total_axis = 3
CALL InitZSignalHome()

PRINT "=== Z信号回零专用程序 ==="
PRINT "直接Z信号回零："
PRINT "IN0 - 模式1单轴回零（轴0）"
PRINT "IN1 - 模式1多轴回零"
PRINT ""
PRINT "原点+Z信号回零（推荐）："
PRINT "IN2 - 模式5单轴回零（轴0）"
PRINT "IN3 - 模式5多轴回零"
PRINT ""
PRINT "系统功能："
PRINT "IN6 - 状态检查"
PRINT "IN7 - Z信号测试"

WHILE 1
    ' 模式1：直接Z信号回零
    IF SCAN_EVENT(IN(0)) > 0 THEN
        CALL DoZSignalHome(0, 1)
    ENDIF

    IF SCAN_EVENT(IN(1)) > 0 THEN
        CALL DoMultiZHome(1)
    ENDIF

    ' 模式5：原点+Z信号回零（推荐）
    IF SCAN_EVENT(IN(2)) > 0 THEN
        CALL DoZSignalHome(0, 5)
    ENDIF

    IF SCAN_EVENT(IN(3)) > 0 THEN
        CALL DoMultiZHome(5)
    ENDIF

    ' 系统功能
    IF SCAN_EVENT(IN(6)) > 0 THEN
        CALL ShowZHomeStatus()
    ENDIF

    IF SCAN_EVENT(IN(7)) > 0 THEN
        CALL TestZSignal(0)
    ENDIF

    DELAY(50)
WEND
END

' Z信号回零系统初始化
GLOBAL SUB InitZSignalHome()
    FOR i = 0 TO total_axis - 1
        BASE(i)
        ATYPE = 4                ' 必须：脉冲+编码器（Z信号回零要求）
        UNITS = 1000             ' 脉冲当量（建议1000以上）
        SPEED = 100              ' 回零速度（不宜过快）
        CREEP = 10               ' 爬行速度（Z信号寻找速度）
        ACCEL = 1000             ' 加速度
        DECEL = 1000             ' 减速度
        HOMEWAIT = 20            ' 反找等待时间
        
        ' 信号映射
        DATUM_IN = 8 + i         ' 原点开关（模式5/6使用）
        FWD_IN = 11 + i          ' 正向限位（安全保护）
        REV_IN = 14 + i          ' 负向限位（安全保护）
        
        ' 信号反转（ZMC系列）
        INVERT_IN(8 + i, ON)     ' 原点开关信号反转
        INVERT_IN(11 + i, ON)    ' 限位信号反转
        INVERT_IN(14 + i, ON)
        
        z_home_status(i) = 0     ' 初始化为未回零
    NEXT
    
    PRINT "Z信号回零系统初始化完成"
    PRINT "ATYPE=4: 脉冲+编码器（支持Z信号）"
    PRINT "确保编码器具有Z相信号输出"
END SUB

' Z信号回零（支持不同模式）
GLOBAL SUB DoZSignalHome(axis_num, home_mode)
    IF home_mode = 1 THEN
        PRINT "=== 开始轴", axis_num, "直接Z信号回零（模式1）==="
        PRINT "运动过程：CREEP正向→Z信号→停止"
        PRINT "精度：±1个编码器脉冲"
    ELSEIF home_mode = 5 THEN
        PRINT "=== 开始轴", axis_num, "原点+Z信号回零（模式5）==="
        PRINT "运动过程：SPEED正向→原点→CREEP反向→离开→CREEP反向→Z信号"
        PRINT "精度：±0.25个编码器脉冲（最高精度）"
    ENDIF
    
    BASE(axis_num)
    z_home_status(axis_num) = 1  ' 设置为回零中
    
    ' 记录回零前状态
    PRINT "回零前状态："
    PRINT "  DPOS：", DPOS(axis_num)
    PRINT "  MPOS：", MPOS(axis_num)
    PRINT "  ATYPE：", ATYPE(axis_num)
    
    IF home_mode = 5 THEN
        PRINT "  原点信号：", IN(8 + axis_num)
    ENDIF
    
    ' 检查ATYPE设置
    IF ATYPE(axis_num) <> 4 AND ATYPE(axis_num) <> 7 THEN
        PRINT "错误：Z信号回零必须配置ATYPE=4或7"
        PRINT "当前ATYPE：", ATYPE(axis_num)
        z_home_status(axis_num) = 3
        RETURN
    ENDIF
    
    ' 清除轴错误状态
    DATUM(0) AXIS(axis_num)
    DELAY(10)
    
    ' 执行Z信号回零
    PRINT "执行DATUM(", home_mode, ")Z信号回零..."
    DATUM(home_mode)
    WAIT UNTIL IDLE(axis_num) = -1
    DELAY(10)                    ' 状态稳定延时
    
    ' 检查结果
    IF AXISSTATUS(axis_num) = 0 THEN
        ' Z信号回零成功 - DATUM指令已自动清零DPOS并纠正MPOS
        z_home_status(axis_num) = 2  ' 设置为已回零
        PRINT "✓ 轴", axis_num, "Z信号回零成功！"
        PRINT "回零后状态："
        PRINT "  DPOS：", DPOS(axis_num), "（已自动清零）"
        PRINT "  MPOS：", MPOS(axis_num), "（已自动纠正）"
        
        IF home_mode = 1 THEN
            PRINT "  精度：±1个编码器脉冲"
        ELSEIF home_mode = 5 THEN
            PRINT "  精度：±0.25个编码器脉冲（最高精度）"
        ENDIF
    ELSE
        z_home_status(axis_num) = 3  ' 设置为回零失败
        PRINT "✗ 轴", axis_num, "Z信号回零失败！"
        PRINT "错误状态：", HEX(AXISSTATUS(axis_num))
        CALL AnalyzeZError(axis_num)
    ENDIF
    
    PRINT "=== 轴", axis_num, "Z信号回零完成 ==="
END SUB

' 多轴Z信号回零
GLOBAL SUB DoMultiZHome(home_mode)
    IF home_mode = 1 THEN
        PRINT "=== 开始多轴直接Z信号回零（模式1）==="
    ELSEIF home_mode = 5 THEN
        PRINT "=== 开始多轴原点+Z信号回零（模式5）==="
    ENDIF
    
    FOR i = 0 TO total_axis - 1
        PRINT "--- 开始轴", i, "Z信号回零 ---"
        CALL DoZSignalHome(i, home_mode)
        IF z_home_status(i) <> 2 THEN
            PRINT "✗ 多轴Z信号回零失败，停止在轴", i
            RETURN
        ENDIF
        DELAY(500)  ' 轴间延时
    NEXT
    
    PRINT "✓ 所有轴Z信号回零成功！"
END SUB

' Z信号回零状态检查
GLOBAL SUB ShowZHomeStatus()
    PRINT "=== Z信号回零状态检查 ==="
    
    FOR i = 0 TO total_axis - 1
        PRINT "轴", i, "详细状态："
        PRINT "  回零状态：", z_home_status(i), "(0-未回零,1-回零中,2-已回零,3-失败)"
        PRINT "  ATYPE：", ATYPE(i), "(必须4或7)"
        PRINT "  DPOS位置：", DPOS(i)
        PRINT "  MPOS位置：", MPOS(i)
        PRINT "  轴状态：", HEX(AXISSTATUS(i))
        PRINT "  编码器值：", ENCODER(i)
        
        ' 检查ATYPE设置
        IF ATYPE(i) = 4 OR ATYPE(i) = 7 THEN
            PRINT "  ✓ ATYPE设置正确，支持Z信号回零"
        ELSE
            PRINT "  ✗ ATYPE设置错误，无法进行Z信号回零"
        ENDIF
        
        PRINT ""
    NEXT
END SUB

' Z信号测试
GLOBAL SUB TestZSignal(axis_num)
    PRINT "=== 轴", axis_num, "Z信号测试 ==="
    
    BASE(axis_num)
    
    ' 检查ATYPE
    IF ATYPE(axis_num) <> 4 AND ATYPE(axis_num) <> 7 THEN
        PRINT "错误：当前ATYPE=", ATYPE(axis_num), "，无法测试Z信号"
        PRINT "请设置ATYPE=4或7"
        RETURN
    ENDIF
    
    PRINT "请手动转动编码器，观察Z信号变化..."
    PRINT "测试时间：10秒"
    
    DIM last_encoder, z_count
    last_encoder = ENCODER(axis_num)
    z_count = 0
    
    FOR i = 1 TO 100
        DIM current_encoder
        current_encoder = ENCODER(axis_num)
        
        ' 检测Z信号（编码器值突变）
        IF ABS(current_encoder - last_encoder) > 500 THEN
            z_count = z_count + 1
            PRINT "检测到Z信号 #", z_count, " 编码器值：", current_encoder
        ENDIF
        
        last_encoder = current_encoder
        DELAY(100)
    NEXT
    
    PRINT "Z信号测试完成，共检测到", z_count, "个Z信号"
    
    IF z_count > 0 THEN
        PRINT "✓ Z信号正常，可以进行Z信号回零"
    ELSE
        PRINT "✗ 未检测到Z信号，请检查："
        PRINT "  1. 编码器是否具有Z相输出"
        PRINT "  2. Z信号线是否正确连接"
        PRINT "  3. 编码器供电是否正常"
    ENDIF
END SUB

' Z信号回零错误分析
GLOBAL SUB AnalyzeZError(axis_num)
    DIM status_val
    status_val = AXISSTATUS(axis_num)
    
    PRINT "轴", axis_num, "Z信号回零错误分析："
    
    IF status_val AND 1 THEN
        PRINT "  ✗ 轴错误标志置位"
    ENDIF
    
    IF status_val AND 2 THEN
        PRINT "  ✗ 碰到正向硬限位"
    ENDIF
    
    IF status_val AND 4 THEN
        PRINT "  ✗ 碰到负向硬限位"
    ENDIF
    
    IF status_val AND 8 THEN
        PRINT "  ✗ 急停信号有效"
    ENDIF
    
    IF status_val AND 16 THEN
        PRINT "  ✗ 驱动器报警"
    ENDIF
    
    PRINT "Z信号回零故障排除建议："
    PRINT "  1. 确认ATYPE=4或7（当前：", ATYPE(axis_num), "）"
    PRINT "  2. 检查编码器Z信号连接"
    PRINT "  3. 检查编码器供电（5V/24V）"
    PRINT "  4. 确认编码器具有Z相输出"
    PRINT "  5. 检查Z信号质量，避免抖动"
    PRINT "  6. 适当降低CREEP速度"
END SUB
