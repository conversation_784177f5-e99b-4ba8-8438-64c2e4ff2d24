'=============================================================================
' 螺丝机控制程序
' 功能：四轴控制(X,Y1,Y2,Z) + 电批Modbus通讯 + 圆弧插补运动
' 硬件：步进电机开环脉冲控制 + RS485 Modbus电批
' 作者：正运动技术支持
' 日期：2025-07-30
'=============================================================================

'全局变量定义
GLOBAL gv_SystemStatus              ' 系统状态：0-待机，1-运行中，2-回零中
GLOBAL gv_HomeStatus(4)             ' 各轴回零状态：0-未回零，1-回零中，2-已回零，3-失败
GLOBAL gv_ScrewCount_Left           ' 左侧螺丝点位数量
GLOBAL gv_ScrewCount_Right          ' 右侧螺丝点位数量
GLOBAL gv_CurrentScrew             ' 当前打螺丝序号

'螺丝位置数据存储(使用TABLE数组)
'TABLE(0-99): 左侧螺丝位置 X,Y,Z坐标 (每个螺丝3个数据)
'TABLE(100-199): 右侧螺丝位置 X,Y,Z坐标
GLOBAL gv_ScrewPos_Left_Start = 0   ' 左侧螺丝位置数据起始地址
GLOBAL gv_ScrewPos_Right_Start = 100 ' 右侧螺丝位置数据起始地址

'吸螺丝位置
GLOBAL gv_PickPos_X = 0             ' 吸螺丝位置X
GLOBAL gv_PickPos_Y = 0             ' 吸螺丝位置Y  
GLOBAL gv_PickPos_Z = 0             ' 吸螺丝位置Z

'电批Modbus通讯参数
GLOBAL gv_ScrewDriver_Addr = 10     ' 电批设备地址
GLOBAL gv_ScrewDriver_Status = 0    ' 电批状态

'主程序
CALL SystemInit()
CALL AxisInit()
CALL ModbusInit()
CALL ScrewDataInit()

PRINT "=== 螺丝机控制系统启动 ==="
PRINT "功能说明："
PRINT "IN0 - 左侧开始打螺丝"
PRINT "IN1 - 右侧开始打螺丝"
PRINT "IN2 - 系统回零"
PRINT "IN3 - 急停"
PRINT "OP0 - 吸螺丝控制(低电平有效)"

'主循环
WHILE 1
    CALL ScanInputs()
    CALL UpdateStatus()
    DELAY(50)
WEND
END

'================ 系统初始化 ================
GLOBAL SUB SystemInit()
    gv_SystemStatus = 0
    gv_ScrewCount_Left = 5          ' 默认左侧5个螺丝
    gv_ScrewCount_Right = 5         ' 默认右侧5个螺丝
    gv_CurrentScrew = 0
    
    '初始化回零状态
    FOR i = 0 TO 3
        gv_HomeStatus(i) = 0
    NEXT
    
    '设置吸螺丝位置(可根据实际调整)
    gv_PickPos_X = 100
    gv_PickPos_Y = 50
    gv_PickPos_Z = 10
    
    PRINT "系统初始化完成"
END SUB

'================ 轴参数初始化 ================
GLOBAL SUB AxisInit()
    '四轴配置：X轴(0), Y1轴(1), Y2轴(2), Z轴(3)
    BASE(0, 1, 2, 3)
    ATYPE = 1, 1, 1, 1              ' 步进电机开环脉冲控制
    UNITS = 1000, 1000, 1000, 1000  ' 脉冲当量 1000脉冲/mm
    SPEED = 100, 100, 100, 50       ' 运动速度 mm/s
    ACCEL = 1000, 1000, 1000, 500   ' 加速度 mm/s²
    DECEL = 1000, 1000, 1000, 500   ' 减速度 mm/s²
    CREEP = 10, 10, 10, 5           ' 回零爬行速度
    
    '限位和原点信号配置
    FWD_IN = 8, 9, 10, 11           ' 正限位 IN8-IN11
    REV_IN = 12, 13, 14, 15         ' 负限位 IN12-IN15
    DATUM_IN = 16, 17, 18, 19       ' 原点开关 IN16-IN19
    
    '信号反转(根据实际硬件调整)
    FOR i = 8 TO 19
        INVERT_IN(i, ON)            ' 常开信号反转
    NEXT
    
    '连续插补设置
    MERGE = ON                      ' 开启连续插补
    CORNER_MODE = 2                 ' 拐角减速模式
    
    PRINT "轴参数初始化完成"
END SUB

'================ Modbus通讯初始化 ================
GLOBAL SUB ModbusInit()
    '配置RS485串口为Modbus主站
    SETCOM(115200, 8, 1, 0, 0, 14)  ' 115200波特率，Modbus主站模式
    ADDRESS = 1                      ' 控制器地址
    
    '设置Modbus连接
    MODBUSM_DES(gv_ScrewDriver_Addr, 1000, 1) ' 连接电批，超时1000ms
    
    PRINT "Modbus通讯初始化完成"
END SUB

'================ 螺丝位置数据初始化 ================
GLOBAL SUB ScrewDataInit()
    '左侧螺丝位置数据(示例)
    '螺丝1: X=50, Y=100, Z=0
    TABLE(0) = 50   : TABLE(1) = 100  : TABLE(2) = 0
    '螺丝2: X=100, Y=100, Z=0  
    TABLE(3) = 100  : TABLE(4) = 100  : TABLE(5) = 0
    '螺丝3: X=150, Y=100, Z=0
    TABLE(6) = 150  : TABLE(7) = 100  : TABLE(8) = 0
    '螺丝4: X=200, Y=100, Z=0
    TABLE(9) = 200  : TABLE(10) = 100 : TABLE(11) = 0
    '螺丝5: X=250, Y=100, Z=0
    TABLE(12) = 250 : TABLE(13) = 100 : TABLE(14) = 0
    
    '右侧螺丝位置数据(示例)
    '螺丝1: X=50, Y=200, Z=0
    TABLE(100) = 50   : TABLE(101) = 200  : TABLE(102) = 0
    '螺丝2: X=100, Y=200, Z=0
    TABLE(103) = 100  : TABLE(104) = 200  : TABLE(105) = 0
    '螺丝3: X=150, Y=200, Z=0
    TABLE(106) = 150  : TABLE(107) = 200  : TABLE(108) = 0
    '螺丝4: X=200, Y=200, Z=0
    TABLE(109) = 200  : TABLE(110) = 200  : TABLE(111) = 0
    '螺丝5: X=250, Y=200, Z=0
    TABLE(112) = 250  : TABLE(113) = 200  : TABLE(114) = 0
    
    PRINT "螺丝位置数据初始化完成"
    PRINT "左侧螺丝数量：", gv_ScrewCount_Left
    PRINT "右侧螺丝数量：", gv_ScrewCount_Right
END SUB

'================ 输入信号扫描 ================
GLOBAL SUB ScanInputs()
    '左侧开始按键
    IF SCAN_EVENT(IN(0)) > 0 THEN
        IF gv_SystemStatus = 0 AND CheckAllAxisHomed() = 1 THEN
            PRINT "开始左侧打螺丝作业"
            STOPTASK 2
            RUN "LeftScrewTask", 2
        ELSE
            PRINT "系统未就绪或未回零，无法开始作业"
        ENDIF
    ENDIF
    
    '右侧开始按键  
    IF SCAN_EVENT(IN(1)) > 0 THEN
        IF gv_SystemStatus = 0 AND CheckAllAxisHomed() = 1 THEN
            PRINT "开始右侧打螺丝作业"
            STOPTASK 2
            RUN "RightScrewTask", 2
        ELSE
            PRINT "系统未就绪或未回零，无法开始作业"
        ENDIF
    ENDIF
    
    '系统回零
    IF SCAN_EVENT(IN(2)) > 0 THEN
        IF gv_SystemStatus = 0 THEN
            PRINT "开始系统回零"
            STOPTASK 2
            RUN "HomeTask", 2
        ELSE
            PRINT "系统运行中，无法回零"
        ENDIF
    ENDIF
    
    '急停
    IF SCAN_EVENT(IN(3)) > 0 THEN
        PRINT "急停触发！"
        RAPIDSTOP(2)
        STOPTASK 2
        gv_SystemStatus = 0
        OP(0, OFF)                  ' 关闭吸螺丝
    ENDIF
END SUB

'================ 检查所有轴是否已回零 ================
GLOBAL SUB CheckAllAxisHomed()
    FOR i = 0 TO 3
        IF gv_HomeStatus(i) <> 2 THEN
            RETURN 0                ' 有轴未回零
        ENDIF
    NEXT
    RETURN 1                        ' 所有轴已回零
END SUB

'================ 状态更新 ================
GLOBAL SUB UpdateStatus()
    '更新Modbus寄存器供HMI显示
    MODBUS_REG(0) = gv_SystemStatus
    MODBUS_REG(1) = gv_ScrewCount_Left
    MODBUS_REG(2) = gv_ScrewCount_Right
    MODBUS_REG(3) = gv_CurrentScrew
    
    '更新各轴回零状态
    FOR i = 0 TO 3
        MODBUS_REG(10 + i) = gv_HomeStatus(i)
    NEXT
    
    '更新各轴当前位置
    FOR i = 0 TO 3
        MODBUS_IEEE(20 + i) = DPOS(i)
    NEXT
END SUB

'================ 吸螺丝控制 ================
GLOBAL SUB PickScrew(pick_on)
    IF pick_on = 1 THEN
        OP(0, ON)                   ' 输出低电平吸螺丝
        PRINT "吸螺丝 ON"
    ELSE
        OP(0, OFF)                  ' 关闭吸螺丝
        PRINT "吸螺丝 OFF"
    ENDIF
END SUB

'================ 电批控制 ================
GLOBAL SUB ScrewDriverControl(action)
    DIM result
    
    IF action = 1 THEN
        '执行螺丝锁紧
        result = MODBUSM_REGSET(9222, 1, 1)    ' 写入组合命令寄存器，执行锁紧
        IF result = 0 THEN
            PRINT "电批锁紧命令发送成功"
        ELSE
            PRINT "电批通讯失败"
        ENDIF
    ELSEIF action = 2 THEN
        '执行螺丝松开
        result = MODBUSM_REGSET(9222, 1, 4)    ' 写入组合命令寄存器，执行松开
        IF result = 0 THEN
            PRINT "电批松开命令发送成功"
        ELSE
            PRINT "电批通讯失败"
        ENDIF
    ENDIF
END SUB

'================ 检查电批状态 ================
GLOBAL SUB CheckScrewDriverStatus()
    DIM status_flag, complete_flag
    
    '读取电批状态
    MODBUSM_REGGET(9728, 1, 100)   ' 读取状态标志到MODBUS_REG(100)
    status_flag = MODBUS_REG(100)
    
    '读取完成标志
    MODBUSM_REGGET(9743, 1, 101)   ' 读取完成标志到MODBUS_REG(101)  
    complete_flag = MODBUS_REG(101)
    
    gv_ScrewDriver_Status = status_flag
    
    RETURN complete_flag            ' 返回完成状态
END SUB

'================ 系统回零任务 ================
GLOBAL SUB HomeTask()
    gv_SystemStatus = 2             ' 设置为回零状态
    PRINT "开始四轴回零..."

    '按Z-Y-X顺序回零，避免碰撞
    FOR i = 3 TO 0 STEP -1
        PRINT "开始轴", i, "回零"
        gv_HomeStatus(i) = 1        ' 设置为回零中

        BASE(i)
        DATUM(0) AXIS(i)            ' 清除错误状态
        DELAY(10)
        DATUM(3)                    ' 正向找原点回零

        WAIT UNTIL IDLE(i) = -1
        DELAY(10)

        IF AXISSTATUS(i) = 0 THEN
            gv_HomeStatus(i) = 2    ' 回零成功
            PRINT "轴", i, "回零成功"
        ELSE
            gv_HomeStatus(i) = 3    ' 回零失败
            PRINT "轴", i, "回零失败，状态：", HEX(AXISSTATUS(i))
            gv_SystemStatus = 0
            RETURN
        ENDIF

        DELAY(500)                  ' 轴间延时
    NEXT

    PRINT "所有轴回零完成"
    gv_SystemStatus = 0             ' 回到待机状态
END SUB

'================ 左侧打螺丝任务 ================
GLOBAL SUB LeftScrewTask()
    gv_SystemStatus = 1             ' 设置为运行状态
    PRINT "执行左侧打螺丝任务"

    FOR screw_idx = 0 TO gv_ScrewCount_Left - 1
        gv_CurrentScrew = screw_idx + 1
        PRINT "开始打第", gv_CurrentScrew, "个螺丝（左侧）"

        '获取螺丝位置
        DIM screw_x, screw_y, screw_z
        screw_x = TABLE(gv_ScrewPos_Left_Start + screw_idx * 3)
        screw_y = TABLE(gv_ScrewPos_Left_Start + screw_idx * 3 + 1)
        screw_z = TABLE(gv_ScrewPos_Left_Start + screw_idx * 3 + 2)

        '执行打螺丝流程
        CALL ScrewProcess(screw_x, screw_y, screw_z, 1)  ' 1表示左侧Y1轴

        DELAY(500)                  ' 螺丝间延时
    NEXT

    PRINT "左侧打螺丝任务完成"
    gv_SystemStatus = 0             ' 回到待机状态
    gv_CurrentScrew = 0
END SUB

'================ 右侧打螺丝任务 ================
GLOBAL SUB RightScrewTask()
    gv_SystemStatus = 1             ' 设置为运行状态
    PRINT "执行右侧打螺丝任务"

    FOR screw_idx = 0 TO gv_ScrewCount_Right - 1
        gv_CurrentScrew = screw_idx + 1
        PRINT "开始打第", gv_CurrentScrew, "个螺丝（右侧）"

        '获取螺丝位置
        DIM screw_x, screw_y, screw_z
        screw_x = TABLE(gv_ScrewPos_Right_Start + screw_idx * 3)
        screw_y = TABLE(gv_ScrewPos_Right_Start + screw_idx * 3 + 1)
        screw_z = TABLE(gv_ScrewPos_Right_Start + screw_idx * 3 + 2)

        '执行打螺丝流程
        CALL ScrewProcess(screw_x, screw_y, screw_z, 2)  ' 2表示右侧Y2轴

        DELAY(500)                  ' 螺丝间延时
    NEXT

    PRINT "右侧打螺丝任务完成"
    gv_SystemStatus = 0             ' 回到待机状态
    gv_CurrentScrew = 0
END SUB

'================ 打螺丝流程 ================
GLOBAL SUB ScrewProcess(target_x, target_y, target_z, y_axis)
    PRINT "目标位置：X=", target_x, " Y=", target_y, " Z=", target_z

    '1. 移动到吸螺丝位置
    PRINT "移动到吸螺丝位置"
    CALL MoveToPickPosition(y_axis)

    '2. 吸取螺丝
    PRINT "吸取螺丝"
    CALL PickScrew(1)               ' 开启吸螺丝
    DELAY(1000)                     ' 等待吸取稳定

    '3. 圆弧插补运动到螺丝孔位
    PRINT "圆弧运动到螺丝孔位"
    CALL ArcMoveToScrew(target_x, target_y, target_z, y_axis)

    '4. 执行打螺丝
    PRINT "执行打螺丝"
    CALL ExecuteScrew(target_z)

    '5. 圆弧插补运动回到吸螺丝位置
    PRINT "圆弧运动回到吸螺丝位置"
    CALL ArcMoveToPickPosition(target_x, target_y, target_z, y_axis)

    '6. 关闭吸螺丝
    CALL PickScrew(0)               ' 关闭吸螺丝

    PRINT "螺丝完成"
END SUB

'================ 移动到吸螺丝位置 ================
GLOBAL SUB MoveToPickPosition(y_axis)
    BASE(0, y_axis, 3)              ' X, Y, Z轴

    '先抬升Z轴到安全高度
    MOVEABS(20) AXIS(3)
    WAIT IDLE(3)

    '移动到吸螺丝位置
    MOVEABS(gv_PickPos_X, gv_PickPos_Y) AXIS(0, y_axis)
    WAIT IDLE(0)
    WAIT IDLE(y_axis)

    '下降到吸螺丝高度
    MOVEABS(gv_PickPos_Z) AXIS(3)
    WAIT IDLE(3)
END SUB

'================ 圆弧插补运动到螺丝孔位 ================
GLOBAL SUB ArcMoveToScrew(target_x, target_y, target_z, y_axis)
    BASE(0, y_axis, 3)              ' X, Y, Z轴插补

    '先抬升到安全高度
    MOVEABS(20) AXIS(3)
    WAIT IDLE(3)

    '计算圆弧中间点（简单的弧形路径）
    DIM mid_x, mid_y, cur_x, cur_y
    cur_x = DPOS(0)
    cur_y = DPOS(y_axis)

    mid_x = (cur_x + target_x) / 2
    mid_y = (cur_y + target_y) / 2 + 30  ' 抬高30mm形成弧形

    '三点圆弧插补：当前点 -> 中间点 -> 目标点
    MOVECIRC2(target_x, target_y, mid_x, mid_y, 0)
    WAIT IDLE(0)
    WAIT IDLE(y_axis)

    '下降到螺丝孔位高度
    MOVEABS(target_z) AXIS(3)
    WAIT IDLE(3)
END SUB

'================ 圆弧插补运动回到吸螺丝位置 ================
GLOBAL SUB ArcMoveToPickPosition(current_x, current_y, current_z, y_axis)
    BASE(0, y_axis, 3)              ' X, Y, Z轴插补

    '先抬升到安全高度
    MOVEABS(20) AXIS(3)
    WAIT IDLE(3)

    '计算圆弧中间点
    DIM mid_x, mid_y
    mid_x = (current_x + gv_PickPos_X) / 2
    mid_y = (current_y + gv_PickPos_Y) / 2 + 30  ' 抬高30mm形成弧形

    '三点圆弧插补：当前点 -> 中间点 -> 吸螺丝位置
    MOVECIRC2(gv_PickPos_X, gv_PickPos_Y, mid_x, mid_y, 0)
    WAIT IDLE(0)
    WAIT IDLE(y_axis)

    '下降到吸螺丝高度
    MOVEABS(gv_PickPos_Z) AXIS(3)
    WAIT IDLE(3)
END SUB

'================ 执行打螺丝 ================
GLOBAL SUB ExecuteScrew(target_z)
    '下降到接触位置
    MOVEABS(target_z - 5) AXIS(3)   ' 下降到螺丝孔上方5mm
    WAIT IDLE(3)

    '启动电批锁紧
    CALL ScrewDriverControl(1)

    '等待电批完成
    DIM timeout_count, complete_status
    timeout_count = 0

    WHILE timeout_count < 100       ' 10秒超时
        complete_status = CheckScrewDriverStatus()
        IF complete_status = 1 THEN
            PRINT "电批锁紧完成"
            GOTO screw_complete
        ENDIF
        DELAY(100)
        timeout_count = timeout_count + 1
    WEND

    screw_complete:

    IF timeout_count >= 100 THEN
        PRINT "电批锁紧超时"
    ENDIF

    '抬升Z轴
    MOVEABS(target_z + 10) AXIS(3)
    WAIT IDLE(3)
END SUB

'================ 参数设置功能 ================
GLOBAL SUB SetScrewPosition(side, screw_num, pos_x, pos_y, pos_z)
    '设置螺丝位置
    'side: 1-左侧, 2-右侧
    'screw_num: 螺丝编号(1开始)

    DIM base_addr, index

    IF side = 1 THEN
        base_addr = gv_ScrewPos_Left_Start
        IF screw_num > gv_ScrewCount_Left THEN
            PRINT "错误：左侧螺丝编号超出范围"
            RETURN
        ENDIF
    ELSEIF side = 2 THEN
        base_addr = gv_ScrewPos_Right_Start
        IF screw_num > gv_ScrewCount_Right THEN
            PRINT "错误：右侧螺丝编号超出范围"
            RETURN
        ENDIF
    ELSE
        PRINT "错误：无效的侧边参数"
        RETURN
    ENDIF

    index = base_addr + (screw_num - 1) * 3
    TABLE(index) = pos_x
    TABLE(index + 1) = pos_y
    TABLE(index + 2) = pos_z

    PRINT "设置螺丝位置：侧边=", side, " 编号=", screw_num
    PRINT "位置：X=", pos_x, " Y=", pos_y, " Z=", pos_z
END SUB

'================ 获取螺丝位置 ================
GLOBAL SUB GetScrewPosition(side, screw_num)
    DIM base_addr, index

    IF side = 1 THEN
        base_addr = gv_ScrewPos_Left_Start
    ELSEIF side = 2 THEN
        base_addr = gv_ScrewPos_Right_Start
    ELSE
        PRINT "错误：无效的侧边参数"
        RETURN
    ENDIF

    index = base_addr + (screw_num - 1) * 3
    PRINT "螺丝位置：侧边=", side, " 编号=", screw_num
    PRINT "X=", TABLE(index), " Y=", TABLE(index + 1), " Z=", TABLE(index + 2)
END SUB

'================ 设置螺丝数量 ================
GLOBAL SUB SetScrewCount(side, count)
    IF side = 1 THEN
        IF count <= 33 THEN         ' 左侧最多33个螺丝(TABLE 0-99)
            gv_ScrewCount_Left = count
            PRINT "设置左侧螺丝数量：", count
        ELSE
            PRINT "错误：左侧螺丝数量超出限制(最多33个)"
        ENDIF
    ELSEIF side = 2 THEN
        IF count <= 33 THEN         ' 右侧最多33个螺丝(TABLE 100-199)
            gv_ScrewCount_Right = count
            PRINT "设置右侧螺丝数量：", count
        ELSE
            PRINT "错误：右侧螺丝数量超出限制(最多33个)"
        ENDIF
    ELSE
        PRINT "错误：无效的侧边参数"
    ENDIF
END SUB

'================ 设置吸螺丝位置 ================
GLOBAL SUB SetPickPosition(pos_x, pos_y, pos_z)
    gv_PickPos_X = pos_x
    gv_PickPos_Y = pos_y
    gv_PickPos_Z = pos_z
    PRINT "设置吸螺丝位置：X=", pos_x, " Y=", pos_y, " Z=", pos_z
END SUB

'================ 手动测试功能 ================
GLOBAL SUB TestPickScrew()
    '测试吸螺丝功能
    PRINT "测试吸螺丝功能"
    CALL PickScrew(1)
    DELAY(2000)
    CALL PickScrew(0)
    PRINT "吸螺丝测试完成"
END SUB

GLOBAL SUB TestScrewDriver()
    '测试电批功能
    PRINT "测试电批锁紧功能"
    CALL ScrewDriverControl(1)
    DELAY(3000)

    PRINT "测试电批松开功能"
    CALL ScrewDriverControl(2)
    DELAY(3000)

    PRINT "电批测试完成"
END SUB

GLOBAL SUB TestMovement(test_x, test_y, test_z)
    '测试运动功能
    IF CheckAllAxisHomed() = 0 THEN
        PRINT "错误：轴未回零，无法测试运动"
        RETURN
    ENDIF

    PRINT "测试运动到位置：X=", test_x, " Y=", test_y, " Z=", test_z

    BASE(0, 1, 3)                   ' 使用Y1轴测试

    '先抬升Z轴
    MOVEABS(20) AXIS(3)
    WAIT IDLE(3)

    '移动到测试位置
    MOVEABS(test_x, test_y) AXIS(0, 1)
    WAIT IDLE(0)
    WAIT IDLE(1)

    '下降到指定高度
    MOVEABS(test_z) AXIS(3)
    WAIT IDLE(3)

    PRINT "运动测试完成"
END SUB

'================ 状态查询功能 ================
GLOBAL SUB ShowSystemStatus()
    PRINT "=== 系统状态 ==="
    PRINT "系统状态：", gv_SystemStatus, " (0-待机,1-运行,2-回零)"
    PRINT "当前螺丝：", gv_CurrentScrew
    PRINT "左侧螺丝数量：", gv_ScrewCount_Left
    PRINT "右侧螺丝数量：", gv_ScrewCount_Right

    PRINT "=== 轴回零状态 ==="
    FOR i = 0 TO 3
        PRINT "轴", i, "：", gv_HomeStatus(i), " (0-未回零,1-回零中,2-已回零,3-失败)"
    NEXT

    PRINT "=== 轴当前位置 ==="
    FOR i = 0 TO 3
        PRINT "轴", i, "：", DPOS(i), " mm"
    NEXT

    PRINT "=== 吸螺丝位置 ==="
    PRINT "X=", gv_PickPos_X, " Y=", gv_PickPos_Y, " Z=", gv_PickPos_Z

    PRINT "=== 电批状态 ==="
    PRINT "电批状态：", gv_ScrewDriver_Status
END SUB

'================ 急停恢复功能 ================
GLOBAL SUB EmergencyReset()
    PRINT "急停恢复"
    gv_SystemStatus = 0
    gv_CurrentScrew = 0

    '关闭所有输出
    OP(0, OFF)                      ' 关闭吸螺丝

    '清除轴错误状态
    FOR i = 0 TO 3
        DATUM(0) AXIS(i)
    NEXT

    PRINT "急停恢复完成，请重新回零"
END SUB

'================ 使用说明 ================
GLOBAL SUB ShowHelp()
    PRINT "=== 螺丝机控制系统使用说明 ==="
    PRINT ""
    PRINT "【输入信号】"
    PRINT "IN0 - 左侧开始打螺丝"
    PRINT "IN1 - 右侧开始打螺丝"
    PRINT "IN2 - 系统回零"
    PRINT "IN3 - 急停"
    PRINT ""
    PRINT "【输出信号】"
    PRINT "OP0 - 吸螺丝控制(低电平有效)"
    PRINT ""
    PRINT "【主要函数】"
    PRINT "SetScrewPosition(side, num, x, y, z) - 设置螺丝位置"
    PRINT "SetScrewCount(side, count) - 设置螺丝数量"
    PRINT "SetPickPosition(x, y, z) - 设置吸螺丝位置"
    PRINT "ShowSystemStatus() - 显示系统状态"
    PRINT "TestPickScrew() - 测试吸螺丝"
    PRINT "TestScrewDriver() - 测试电批"
    PRINT "TestMovement(x, y, z) - 测试运动"
    PRINT "EmergencyReset() - 急停恢复"
    PRINT ""
    PRINT "【使用流程】"
    PRINT "1. 系统上电后先执行回零(IN2)"
    PRINT "2. 设置螺丝位置和数量"
    PRINT "3. 按下开始按键执行打螺丝作业"
    PRINT ""
    PRINT "【注意事项】"
    PRINT "1. 必须先回零才能执行作业"
    PRINT "2. 急停后需要重新回零"
    PRINT "3. 电批通讯地址默认为10"
    PRINT "4. 圆弧插补需要至少两轴参与"
END SUB
