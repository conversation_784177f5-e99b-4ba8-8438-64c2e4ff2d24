'=============================================================================
' 任务1：左侧打螺丝任务
' 由主控制任务通过RUNTASK 1, LeftScrewTask启动
' 独立运行，不阻塞主控制任务的输入扫描
'=============================================================================

'任务1主程序
CALL LeftScrewTask()
END

'================ 左侧打螺丝任务 ================
GLOBAL SUB LeftScrewTask()
    PRINT "执行左侧打螺丝任务"
    
    FOR screw_idx = 0 TO left_screw_num - 1
        cur_screw = screw_idx + 1
        PRINT "开始打第", cur_screw, "个螺丝（左侧）"
        
        '获取螺丝位置
        DIM screw_x, screw_y, screw_z
        screw_x = TABLE(left_start + screw_idx * 3)
        screw_y = TABLE(left_start + screw_idx * 3 + 1)
        screw_z = TABLE(left_start + screw_idx * 3 + 2)
        
        '执行打螺丝流程
        CALL WorkScrew(screw_x, screw_y, screw_z, 1)  ' 1表示左侧Y1轴
        
        DELAY(500)                  ' 螺丝间延时
    NEXT
    
    PRINT "左侧打螺丝任务完成"
    cur_screw = 0
    '任务结束，状态将在主控制任务的UpdateTaskStatus中处理
END SUB

'================ 打螺丝作业流程 ================
GLOBAL SUB WorkScrew(target_x, target_y, target_z, y_axis)
    PRINT "目标位置：X=", target_x, " Y=", target_y, " Z=", target_z
    
    '1. 移动到吸螺丝位置
    PRINT "移动到吸螺丝位置"
    CALL MoveToPick(y_axis)
    
    '2. 吸取螺丝
    PRINT "吸取螺丝"
    OP(0, ON)                       ' 开启吸螺丝
    DELAY(1000)                     ' 等待吸取稳定
    
    '3. 直线运动到螺丝孔位
    PRINT "直线运动到螺丝孔位"
    CALL LinearMoveToTarget(target_x, target_y, target_z, y_axis)
    
    '4. 执行打螺丝
    PRINT "执行打螺丝"
    CALL DoScrew(target_z)
    
    '5. 直线运动回到吸螺丝位置
    PRINT "直线运动回到吸螺丝位置"
    CALL LinearMoveBack(y_axis)
    
    '6. 关闭吸螺丝
    OP(0, OFF)                      ' 关闭吸螺丝
    
    PRINT "螺丝完成"
END SUB

'================ 移动到吸螺丝位置 ================
GLOBAL SUB MoveToPick(y_axis)
    BASE(0, y_axis, 3)              ' X, Y, Z轴
    
    '先抬升Z轴到安全高度
    MOVEABS(20) AXIS(3)
    WAIT IDLE(3)
    
    '移动到吸螺丝位置（分别移动）
    MOVEABS(pick_x) AXIS(0)
    MOVEABS(pick_y) AXIS(y_axis)
    WAIT IDLE(0)
    WAIT IDLE(y_axis)
    
    '下降到吸螺丝高度
    MOVEABS(pick_z) AXIS(3)
    WAIT IDLE(3)
END SUB

'================ 直线运动到目标位置 ================
GLOBAL SUB LinearMoveToTarget(target_x, target_y, target_z, y_axis)
    BASE(0, y_axis, 3)              ' X, Y, Z轴
    
    '先抬升到安全高度
    MOVEABS(20) AXIS(3)
    WAIT IDLE(3)
    
    '移动到目标XY位置
    MOVEABS(target_x) AXIS(0)
    MOVEABS(target_y) AXIS(y_axis)
    WAIT IDLE(0)
    WAIT IDLE(y_axis)
    
    '下降到螺丝孔位高度
    MOVEABS(target_z) AXIS(3)
    WAIT IDLE(3)
END SUB

'================ 直线运动回到吸螺丝位置 ================
GLOBAL SUB LinearMoveBack(y_axis)
    BASE(0, y_axis, 3)              ' X, Y, Z轴
    
    '先抬升到安全高度
    MOVEABS(20) AXIS(3)
    WAIT IDLE(3)
    
    '移动回吸螺丝位置
    MOVEABS(pick_x) AXIS(0)
    MOVEABS(pick_y) AXIS(y_axis)
    WAIT IDLE(0)
    WAIT IDLE(y_axis)
    
    '下降到吸螺丝高度
    MOVEABS(pick_z) AXIS(3)
    WAIT IDLE(3)
END SUB

'================ 执行打螺丝 ================
GLOBAL SUB DoScrew(target_z)
    '下降到接触位置
    MOVEABS(target_z - 5) AXIS(3)   ' 下降到螺丝孔上方5mm
    WAIT IDLE(3)
    
    '模拟电批锁紧
    PRINT "模拟电批锁紧..."
    DELAY(2000)                     ' 模拟锁紧时间
    PRINT "电批锁紧完成"
    
    '抬升Z轴
    MOVEABS(target_z + 10) AXIS(3)
    WAIT IDLE(3)
END SUB
