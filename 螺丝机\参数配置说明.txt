===============================================================================
                        螺丝机参数配置说明 - 高速高精度版本
===============================================================================

【最新参数配置】

=== 螺丝数量配置 ===
左侧螺丝数量：8个（2x4阵列）
右侧螺丝数量：8个（2x4阵列）
总螺丝数量：16个

螺丝阵列布局：
```
左侧阵列（2行4列）：
第一排：螺丝1  螺丝2  螺丝3  螺丝4
       X=100  X=150  X=200  X=250
       Y=80   Y=80   Y=80   Y=80

第二排：螺丝5  螺丝6  螺丝7  螺丝8
       X=100  X=150  X=200  X=250
       Y=120  Y=120  Y=120  Y=120

右侧阵列（2行4列）：
第一排：螺丝1  螺丝2  螺丝3  螺丝4
       X=100  X=150  X=200  X=250
       Y=220  Y=220  Y=220  Y=220

第二排：螺丝5  螺丝6  螺丝7  螺丝8
       X=100  X=150  X=200  X=250
       Y=260  Y=260  Y=260  Y=260
```

=== 运动参数配置 ===
运动速度：1000mm/s (1m/s)
- X轴：1000mm/s
- Y1轴：1000mm/s  
- Y2轴：1000mm/s
- Z轴：500mm/s（较慢，保证精度）

加速度：1000mm/s² (1m/s²)
- 所有轴：1000mm/s²

减速度：1000mm/s² (1m/s²)
- 所有轴：1000mm/s²

轨迹类型：SS型轨迹（超平滑）
- 所有轴：PROFILE = 2（S型基础）
- VP_MODE = 7（SS型曲线模式）
- S型时间常数：50ms（0-250ms范围）

脉冲当量：
- X轴：1000脉冲/mm
- Y1轴：100脉冲/mm（10mm螺距）
- Y2轴：100脉冲/mm（10mm螺距）
- Z轴：1000脉冲/mm

=== 双Y轴滑轨位置 ===
左侧Y1轴：
- 用户位置：50mm（方便放置工件）
- 工作位置：80mm（第一个螺丝Y坐标）

右侧Y2轴：
- 用户位置：200mm（方便放置工件）
- 工作位置：220mm（第一个螺丝Y坐标）

=== 吸螺丝位置 ===
吸螺丝位置：
- X坐标：50mm
- Y坐标：150mm
- Z坐标：5mm

【参数优势】

=== 高速运动 ===
✅ 运动速度提升到1m/s，比原来的80mm/s快12.5倍
✅ 加速度1m/s²，快速启停
✅ SS型轨迹，运动超平滑，最大程度减少振动和抖动
✅ 大幅提高生产效率

=== 高精度控制 ===
✅ SS型轨迹减少冲击和振动，加减速更柔和
✅ Z轴速度适中（500mm/s），保证定位精度
✅ 合理的加减速参数，避免失步
✅ 脉冲当量匹配实际机械结构

=== 大批量生产 ===
✅ 螺丝数量从4个增加到8个，产能翻倍
✅ 2x4阵列布局，充分利用工作空间
✅ 左右两侧各8个，总共16个螺丝
✅ 适合大批量生产需求

【性能提升对比】

=== 速度提升 ===
原参数：80mm/s → 新参数：1000mm/s
提升倍数：12.5倍
单次移动时间大幅缩短

=== 产能提升 ===
原螺丝数：4个/侧 → 新螺丝数：8个/侧
产能提升：100%（翻倍）
总螺丝数：从8个增加到16个

=== 轨迹优化 ===
原轨迹：梯形轨迹 → 新轨迹：SS型轨迹
优势：超平滑，最大程度减少振动和抖动，提高精度

【时间估算】

=== 单个螺丝作业时间 ===
基于1m/s运动速度估算：
1. 移动到吸螺丝位置：~0.2秒
2. 吸取螺丝：1秒（固定）
3. 移动到螺丝孔位：~0.3秒
4. 电批锁紧：2秒（固定）
5. 移动回吸螺丝位置：~0.3秒
6. 螺丝间延时：0.5秒

单个螺丝总时间：约4.3秒

=== 完整作业时间 ===
左侧8个螺丝：8 × 4.3 = 34.4秒
右侧8个螺丝：8 × 4.3 = 34.4秒
双侧并行：最长34.4秒（如果交替进行）

相比原来4个螺丝×5.5秒=22秒
虽然螺丝数量翻倍，但由于速度提升，时间增加有限

【参数调整方法】

=== 螺丝数量调整 ===
修改全局变量：
```basic
left_screw_num = 8      ' 左侧螺丝数量
right_screw_num = 8     ' 右侧螺丝数量
```

=== 运动速度调整 ===
修改SPEED参数：
```basic
SPEED = 1000, 1000, 1000, 500   ' X,Y1,Y2,Z轴速度(mm/s)
```

=== 加减速调整 ===
修改ACCEL和DECEL参数：
```basic
ACCEL = 1000, 1000, 1000, 1000  ' 加速度(mm/s²)
DECEL = 1000, 1000, 1000, 1000  ' 减速度(mm/s²)
```

=== 轨迹类型调整 ===
修改PROFILE和VP_MODE参数：
```basic
PROFILE = 2, 2, 2, 2        ' 所有轴S型轨迹基础
SRAMP = 50, 50, 50, 50      ' S型时间常数50ms（0-250ms范围）
VP_MODE = 7, 7, 7, 7        ' 所有轴SS型曲线（模式7）
```

SS型轨迹优势：
- 加加速参数随加减速阶段变化
- 速度曲线比S型更平滑
- 加减速开始和结束阶段更柔和
- 最大程度减少轴的抖动

=== 螺丝位置调整 ===
修改TABLE数组：
```basic
' 左侧螺丝位置（索引0-23，8个螺丝×3坐标）
TABLE(0) = 100      ' 螺丝1 X坐标
TABLE(1) = 80       ' 螺丝1 Y坐标
TABLE(2) = 0        ' 螺丝1 Z坐标
...

' 右侧螺丝位置（索引100-123，8个螺丝×3坐标）
TABLE(100) = 100    ' 螺丝1 X坐标
TABLE(101) = 220    ' 螺丝1 Y坐标
TABLE(102) = 0      ' 螺丝1 Z坐标
...
```

【注意事项】

=== 安全考虑 ===
1. 高速运动需要确保机械结构足够刚性
2. 检查限位开关和急停功能
3. 逐步提升速度，先测试低速再提升
4. 确保电机功率足够支持高加速度

=== 调试建议 ===
1. 先使用测试版验证逻辑正确性
2. 实际硬件测试时先降低速度
3. 逐步提升到目标速度
4. 观察运动平滑性和定位精度

=== 优化空间 ===
1. 可根据实际需求调整螺丝阵列布局
2. 可优化运动路径减少空行程
3. 可调整S型轨迹参数优化平滑性
4. 可根据螺丝类型调整锁紧时间

【版本兼容性】
- 螺丝机多线程简化版.bas：已更新所有参数
- 螺丝机测试版.bas：已更新螺丝数量
- 所有子任务文件：无需修改
- 使用说明文档：保持兼容

===============================================================================
