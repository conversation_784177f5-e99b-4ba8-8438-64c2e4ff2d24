===============================================================================
                        VP_SPEED速度监控优化说明 - 正确理解和使用VP_SPEED
===============================================================================

【VP_SPEED的工作机制】

=== VP_SPEED的返回值规则 ===
```
官方手册说明：
VP_SPEED -- 当前运动速度
- 返回轴当前运动的速度，单位为 units/s
- 当多轴运动时，主轴返回的是插补运动的速度，不是主轴的分速度
- 非主轴返回的是相应的分速度，与 MSPEED 效果一致
- VP_SPEED 在默认情况下是为显示多轴合成速度设计的，是没有负值的

关键理解：
✅ 主轴（第一个轴）：返回插补合成速度
✅ 非主轴：返回各自的分速度
✅ 默认无负值，显示合成速度
```

=== 螺丝机的轴配置分析 ===
```
当前轴配置：BASE(0, 3)
- 轴0（X轴）：主轴
- 轴3（Z轴）：非主轴

VP_SPEED返回值：
VP_SPEED(0)：X轴和Z轴的插补合成速度（不是X轴分速度）
VP_SPEED(3)：Z轴的分速度（与MSPEED(3)一致）

问题：
❌ VP_SPEED(0)显示的是合成速度，不是X轴的真实分速度
❌ 无法直接看到X轴的真实速度变化
❌ 可能误解速度曲线的含义
```

【解决方案】

=== 方案1：使用MSPEED监控各轴分速度 ===
```
推荐监控方式：
MSPEED(0)：X轴的真实分速度
MSPEED(3)：Z轴的真实分速度

优势：
✅ 直接显示各轴的真实速度
✅ 可以看到各轴的独立速度变化
✅ 便于分析三段轨迹中各轴的速度衔接
✅ 与VP_SPEED(3)效果一致，但更清晰

示波器设置建议：
MSPEED(0) 垂直刻度 100，偏移 0
MSPEED(3) 垂直刻度 100，偏移 -50
```

=== 方案2：修改SYSTEM_ZSET显示单轴命令速度 ===
```
如果需要VP_SPEED显示单轴速度：
SYSTEM_ZSET的bit0设置为0

效果：
- VP_SPEED可以显示单轴的命令速度
- 可正可负
- 更适合单轴速度分析

设置方法：
DIM current_zset
current_zset = SYSTEM_ZSET
SYSTEM_ZSET = current_zset AND (NOT 1)  ' 清除bit0

恢复方法：
SYSTEM_ZSET = current_zset OR 1         ' 设置bit0
```

=== 方案3：同时监控合成速度和分速度 ===
```
综合监控方案：
VP_SPEED(0)：插补合成速度（整体运动速度）
MSPEED(0)：X轴分速度
MSPEED(3)：Z轴分速度

分析价值：
✅ 合成速度：看整体运动的连续性
✅ X轴分速度：看X轴的速度变化和衔接
✅ Z轴分速度：看Z轴的速度变化和衔接
✅ 全面了解三段轨迹的速度特性
```

【三段轨迹的速度分析】

=== 理想的速度曲线特征 ===
```
第一段：抬Z（X不动，Z上升）
VP_SPEED(0)：合成速度 = Z轴速度
MSPEED(0)：X轴速度 = 0
MSPEED(3)：Z轴速度 = 合成速度

第二段：圆弧或直线（X和Z同时运动）
VP_SPEED(0)：合成速度 = √(X轴速度² + Z轴速度²)
MSPEED(0)：X轴分速度
MSPEED(3)：Z轴分速度

第三段：下Z（X不动，Z下降）
VP_SPEED(0)：合成速度 = Z轴速度
MSPEED(0)：X轴速度 = 0
MSPEED(3)：Z轴速度 = 合成速度
```

=== 连续插补的验证要点 ===
```
检查合成速度连续性：
✅ VP_SPEED(0)在三段之间不应降到0
✅ 衔接处应该平滑过渡
✅ 整体曲线应该连续

检查各轴分速度连续性：
✅ MSPEED(0)在第二段前后应该平滑衔接
✅ MSPEED(3)在三段之间应该连续变化
✅ 各轴速度变化应该协调一致

问题诊断：
❌ 如果VP_SPEED(0)在衔接处降到0：连续插补失败
❌ 如果MSPEED有突变：轴间协调有问题
❌ 如果速度曲线有尖峰：平滑参数需要调整
```

【示波器监控设置】

=== 推荐的监控配置 ===
```
监控信号：
1. VP_SPEED(0)  - 插补合成速度
2. MSPEED(0)    - X轴分速度  
3. MSPEED(3)    - Z轴分速度

显示设置：
VP_SPEED(0)  垂直刻度 100，偏移 0     （蓝色）
MSPEED(0)    垂直刻度 100，偏移 -60   （红色）
MSPEED(3)    垂直刻度 100，偏移 -120  （绿色）

触发设置：
TRIGGER      自动触发示波器
时间刻度：   根据运动时间调整
```

=== 监控代码示例 ===
```basic
'开始监控
TRIGGER                         ' 自动触发示波器

'执行连续轨迹
CALL BeginContinuousPath()
CALL PushThreeSegment(50, 10, 100, 30, 25, 25, 75, 50)
CALL EndContinuousPath()

'监控结果分析：
'VP_SPEED(0)：应该显示连续的合成速度曲线
'MSPEED(0)：应该显示X轴在第二段的速度变化
'MSPEED(3)：应该显示Z轴在三段中的连续变化
```

【速度曲线分析方法】

=== 正常的连续插补曲线 ===
```
VP_SPEED(0) - 合成速度：
 ^
 |    /~~~~~~~~\
 |   /          \
 |  /            \
 | /              \
 |/                \
 +-----|-----|-----|---> 时间
      第1段 第2段 第3段

MSPEED(0) - X轴分速度：
 ^
 |      /~~\
 |     /    \
 |    /      \
 |   /        \
 |  /          \
 | /            \
 +-----|-----|-----|---> 时间
      第1段 第2段 第3段
      (0)   (有值) (0)

MSPEED(3) - Z轴分速度：
 ^
 |  /\      /\
 | /  \    /  \
 |/    \  /    \
 |      \/      \
 +-----|-----|-----|---> 时间
      第1段 第2段 第3段
      (上升)(变化)(下降)
```

=== 问题诊断的曲线特征 ===
```
如果VP_SPEED(0)有断点：
 ^
 |  /\    /\    /\
 | /  \  /  \  /  \
 |/    \/    \/    \
 +-----|-----|-----|---> 时间
      断点  断点  断点
问题：连续插补失败，有中间停车

如果MSPEED有突变：
 ^
 |    /|    |\
 |   / |    | \
 |  /  |    |  \
 | /   |    |   \
 |/    |    |    \
 +-----|-----|-----|---> 时间
      突变  突变
问题：平滑参数不足，需要调整
```

【优化建议】

=== 监控方法优化 ===
```
1. 主要使用MSPEED监控：
   - 更直观地显示各轴真实速度
   - 便于分析轴间协调
   - 易于发现问题

2. 辅助使用VP_SPEED(0)：
   - 监控整体运动连续性
   - 验证插补效果
   - 检查合成速度平滑性

3. 对比分析：
   - 合成速度与分速度的关系
   - 各轴速度的协调性
   - 三段之间的衔接效果
```

=== 参数调整指导 ===
```
如果合成速度不连续：
- 检查MERGE设置
- 确认无WAIT IDLE干扰
- 验证CORNER_MODE设置

如果分速度有突变：
- 增大SRAMP时间
- 增大ZSMOOTH半径
- 调整VP_MODE设置

如果轴间不协调：
- 检查ACCEL/DECEL设置
- 确认轴参数匹配
- 验证BASE设置正确
```

【实际应用】

=== 螺丝机速度监控流程 ===
```
1. 设置监控：
   TRIGGER
   
2. 执行测试轨迹：
   CALL BeginContinuousPath()
   CALL PushThreeSegment(...)
   CALL EndContinuousPath()
   
3. 分析曲线：
   - VP_SPEED(0)：检查整体连续性
   - MSPEED(0)：检查X轴速度变化
   - MSPEED(3)：检查Z轴速度变化
   
4. 优化参数：
   - 根据曲线特征调整参数
   - 重复测试验证效果
   - 达到最佳平滑性
```

=== 预期的理想效果 ===
```
连续插补成功的标志：
✅ VP_SPEED(0)完全连续，无断点
✅ MSPEED(0)在第二段平滑变化
✅ MSPEED(3)三段连续，无突变
✅ 各轴速度协调一致
✅ 整体运动平滑高效
```

【总结】

VP_SPEED速度监控的关键：
✅ **理解VP_SPEED机制**：主轴返回合成速度，非主轴返回分速度
✅ **推荐使用MSPEED**：直接显示各轴真实分速度，更直观
✅ **综合监控方案**：VP_SPEED(0)+MSPEED(0)+MSPEED(3)全面分析
✅ **正确分析曲线**：区分合成速度和分速度的含义
✅ **问题诊断方法**：根据曲线特征判断连续插补效果
✅ **参数优化指导**：基于速度曲线分析调整平滑参数

通过正确使用VP_SPEED和MSPEED的组合监控，
可以全面了解三段轨迹的速度特性，
确保连续插补的效果达到最佳状态。

===============================================================================
