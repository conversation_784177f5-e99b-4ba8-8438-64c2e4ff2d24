'=============================================================================
' Zbasic 综合演示程序
' 深圳市正运动技术有限公司
' 演示内容：基本语法、轴控制、IO操作、数据存储等
'=============================================================================

'变量定义
DIM demo_state        '演示状态：0-初始化，1-待机，2-运行中，3-停止
DIM current_step      '当前步骤
DIM error_count       '错误计数
DIM cycle_count       '循环计数

'常量定义
CONST DEMO_SPEED = 200        '演示速度
CONST AXIS_COUNT = 2          '轴数量
CONST DEMO_DISTANCE = 100     '演示运动距离

'数组定义
DIM axis_positions(2)     '轴位置数组
DIM io_states(8)          'IO状态数组

'=============================================================================
' 主程序
'=============================================================================
PRINT "正运动Zbasic演示程序 V1.0"

demo_state = 0
current_step = 0
error_count = 0
cycle_count = 0

'调用初始化子程序
CALL SystemInit()

'简单演示循环
FOR demo_loop = 1 TO 3
    PRINT "演示循环第", demo_loop, "次"

    '单轴运动演示
    CALL SingleAxisDemo()

    '双轴插补演示
    CALL TwoAxisDemo()

    'IO操作演示
    CALL IODemo()

    cycle_count = cycle_count + 1
    DELAY(2000)
NEXT

PRINT "演示程序完成，总循环次数：", cycle_count
END

'=============================================================================
' 系统初始化子程序
'=============================================================================
SUB SystemInit()
    PRINT "初始化系统参数..."

    '轴参数设置
    BASE(0, 1)                    '选择轴0和轴1
    ATYPE = 1, 1                  '设置为脉冲轴
    UNITS = 100, 100              '脉冲当量：每单位100脉冲
    SPEED = DEMO_SPEED, DEMO_SPEED '运动速度
    ACCEL = 1000, 1000            '加速度
    DECEL = 1000, 1000            '减速度
    SRAMP = 50, 50                'S曲线时间
    DPOS = 0, 0                   '位置清零

    '插补设置
    MERGE = ON                    '开启连续插补

    '初始化IO状态
    FOR i = 0 TO 7
        io_states(i) = 0
        OP(i, OFF)                '关闭所有输出
    NEXT

    '初始化TABLE数据
    TABLE(0, 50, 100, 150, 200)   '存储一些测试数据

    '清除VR寄存器
    FOR i = 0 TO 10
        VR(i) = 0
    NEXT

    demo_state = 1                '设置为待机状态
    PRINT "系统初始化完成"
END SUB

'=============================================================================
' 单轴运动演示
'=============================================================================
SUB SingleAxisDemo()
    PRINT "单轴运动演示"
    current_step = 1

    BASE(0)
    MOVE(DEMO_DISTANCE)
    WAIT IDLE
    axis_positions(0) = DPOS(0)
    PRINT "轴0位置：", axis_positions(0)

    '返回原点
    MOVE(-DEMO_DISTANCE)
    WAIT IDLE
    PRINT "轴0返回原点：", DPOS(0)
END SUB

'=============================================================================
' 双轴插补演示
'=============================================================================
SUB TwoAxisDemo()
    PRINT "双轴插补演示"
    current_step = 2

    BASE(0, 1)
    MOVE(50, 50)                  '直线插补
    WAIT IDLE
    PRINT "插补位置：X=", DPOS(0), " Y=", DPOS(1)

    '返回原点
    MOVEABS(0, 0)
    WAIT IDLE
END SUB

'=============================================================================
' IO操作演示
'=============================================================================
SUB IODemo()
    PRINT "IO操作演示"
    current_step = 3

    FOR i = 2 TO 5
        OP(i, ON)
        DELAY(200)
        OP(i, OFF)
        DELAY(200)
    NEXT

    '数据处理演示
    LOCAL sum, average
    sum = 0
    FOR i = 0 TO 4
        sum = sum + TABLE(i)
    NEXT
    average = sum / 5
    PRINT "TABLE平均值：", average

    '保存数据到VR寄存器
    VR(20) = average
    VR(21) = cycle_count
END SUB

'=============================================================================
' 状态显示子程序
'=============================================================================
SUB DisplayStatus()
    PRINT "========== 系统状态 =========="
    PRINT "演示状态：", demo_state
    PRINT "当前步骤：", current_step
    PRINT "循环次数：", cycle_count
    PRINT "轴0位置：", DPOS(0)
    PRINT "轴1位置：", DPOS(1)

    '显示部分IO状态
    PRINT "输入IN0状态：", IN(0)
    PRINT "输入IN1状态：", IN(1)
    PRINT "=========================="
END SUB
