'=============================================================================
' Zbasic 运动控制专项演示程序
' 展示各种运动控制功能：单轴、多轴插补、回零、同步运动等
'=============================================================================

'全局变量
DIM demo_mode              '演示模式：1-单轴，2-插补，3-回零，4-同步
DIM axis_ready             '轴准备状态

'运动参数常量
CONST DEMO_SPEED = 200     '演示速度
CONST DEMO_ACCEL = 1000    '演示加速度
CONST MOVE_DIST = 100      '运动距离

'=============================================================================
' 主程序
'=============================================================================
PRINT "=== Zbasic运动控制演示程序 ==="
PRINT "1. 单轴运动演示"
PRINT "2. 多轴插补演示" 
PRINT "3. 回零功能演示"
PRINT "4. 同步运动演示"
PRINT "请通过输入信号选择演示模式"

'系统初始化
CALL SystemInit()

'主循环
WHILE 1
    '检查输入信号选择演示模式
    IF SCAN_EVENT(IN(0)) > 0 THEN
        demo_mode = 1
        CALL SingleAxisDemo()
    ENDIF
    
    IF SCAN_EVENT(IN(1)) > 0 THEN
        demo_mode = 2
        CALL InterpolationDemo()
    ENDIF
    
    IF SCAN_EVENT(IN(2)) > 0 THEN
        demo_mode = 3
        CALL HomingDemo()
    ENDIF
    
    IF SCAN_EVENT(IN(3)) > 0 THEN
        demo_mode = 4
        CALL SyncMotionDemo()
    ENDIF
    
    '紧急停止
    IF IN(7) = ON THEN
        RAPIDSTOP(2)
        PRINT "紧急停止！"
        DELAY(1000)
    ENDIF
    
    DELAY(50)
WEND
END

'=============================================================================
' 系统初始化
'=============================================================================
SUB SystemInit()
    PRINT "初始化运动控制系统..."
    
    '轴0设置（X轴）
    BASE(0)
    ATYPE = 1                   '脉冲轴
    UNITS = 100                 '脉冲当量
    SPEED = DEMO_SPEED          '速度
    ACCEL = DEMO_ACCEL          '加速度
    DECEL = DEMO_ACCEL          '减速度
    SRAMP = 50                  'S曲线
    DPOS = 0                    '位置清零
    
    '轴1设置（Y轴）
    BASE(1)
    ATYPE = 1
    UNITS = 100
    SPEED = DEMO_SPEED
    ACCEL = DEMO_ACCEL
    DECEL = DEMO_ACCEL
    SRAMP = 50
    DPOS = 0
    
    '轴2设置（Z轴）
    BASE(2)
    ATYPE = 1
    UNITS = 100
    SPEED = DEMO_SPEED/2        'Z轴速度稍慢
    ACCEL = DEMO_ACCEL
    DECEL = DEMO_ACCEL
    SRAMP = 50
    DPOS = 0
    
    '限位设置
    FWD_IN = 8, 9, 10          '正向限位
    REV_IN = 11, 12, 13        '负向限位
    
    '反转限位信号（ZMC系列OFF有效）
    FOR i = 8 TO 13
        INVERT_IN(i, ON)
    NEXT
    
    '插补设置
    MERGE = ON                  '开启连续插补
    CORNER_MODE = 2             '拐角减速
    DECEL_ANGLE = 15 * (PI/180) '减速角度
    STOP_ANGLE = 45 * (PI/180)  '停止角度
    
    axis_ready = 1
    PRINT "系统初始化完成"
END SUB

'=============================================================================
' 单轴运动演示
'=============================================================================
SUB SingleAxisDemo()
    PRINT "=== 单轴运动演示 ==="
    
    '演示1：基本直线运动
    PRINT "1. 基本直线运动"
    BASE(0)
    MOVE(MOVE_DIST)
    WAIT IDLE
    PRINT "X轴位置：", DPOS(0)
    
    DELAY(500)
    MOVE(-MOVE_DIST)
    WAIT IDLE
    PRINT "返回原点，X轴位置：", DPOS(0)
    
    '演示2：持续运动
    PRINT "2. 持续运动演示（3秒后停止）"
    BASE(0)
    VMOVE(1)                    '正向持续运动
    DELAY(3000)
    CANCEL                      '停止运动
    WAIT IDLE
    PRINT "持续运动后位置：", DPOS(0)
    
    '演示3：不同速度运动
    PRINT "3. 变速运动演示"
    BASE(0)
    SPEED = 50                  '低速
    MOVE(50)
    WAIT IDLE
    
    SPEED = 300                 '高速
    MOVE(50)
    WAIT IDLE
    
    SPEED = DEMO_SPEED          '恢复默认速度
    MOVEABS(0)                  '回到原点
    WAIT IDLE
    
    PRINT "单轴演示完成"
END SUB

'=============================================================================
' 多轴插补演示
'=============================================================================
SUB InterpolationDemo()
    PRINT "=== 多轴插补演示 ==="
    
    '确保从原点开始
    BASE(0, 1)
    MOVEABS(0, 0)
    WAIT IDLE
    
    '演示1：直线插补
    PRINT "1. 直线插补 - 画正方形"
    BASE(0, 1)
    MOVE(100, 0)                '右
    MOVE(0, 100)                '上
    MOVE(-100, 0)               '左
    MOVE(0, -100)               '下
    WAIT IDLE
    PRINT "正方形绘制完成"
    
    '演示2：圆弧插补
    PRINT "2. 圆弧插补 - 画圆"
    BASE(0, 1)
    MOVEABS(50, 0)              '移动到起始点
    WAIT IDLE
    
    '画完整圆（4个90度圆弧）
    MOVECIRC(50, 100, 0, 50, 0)     '第一象限
    MOVECIRC(-50, 100, 0, 50, 0)    '第二象限
    MOVECIRC(-50, 0, 0, 50, 0)      '第三象限
    MOVECIRC(50, 0, 0, 50, 0)       '第四象限
    WAIT IDLE
    PRINT "圆形绘制完成"
    
    '演示3：三轴螺旋运动
    PRINT "3. 三轴螺旋运动"
    BASE(0, 1, 2)
    MOVEABS(0, 0, 0)            '回到原点
    WAIT IDLE
    
    '螺旋上升
    MHELICAL(50, 0, 25, 0, 1, 50, 1)  '螺旋插补
    WAIT IDLE
    PRINT "螺旋运动完成，Z轴高度：", DPOS(2)
    
    '返回原点
    MOVEABS(0, 0, 0)
    WAIT IDLE
    
    PRINT "插补演示完成"
END SUB

'=============================================================================
' 回零功能演示
'=============================================================================
SUB HomingDemo()
    PRINT "=== 回零功能演示 ==="
    
    '设置原点输入
    DATUM_IN = 14, 15, 16       '原点输入信号
    
    '反转原点信号
    INVERT_IN(14, ON)
    INVERT_IN(15, ON)
    INVERT_IN(16, ON)
    
    '设置回零参数
    BASE(0, 1, 2)
    SPEED = 100, 100, 50        '回零速度
    CREEP = 20, 20, 10          '爬行速度
    HOMEWAIT = 100, 100, 100    '反找等待时间
    
    '执行回零
    PRINT "开始X轴回零..."
    BASE(0)
    DATUM(3)                    '回零模式3
    WAIT IDLE
    IF AXISSTATUS(0) AND 1 = 0 THEN
        PRINT "X轴回零成功，位置：", DPOS(0)
    ELSE
        PRINT "X轴回零失败"
    ENDIF
    
    PRINT "开始Y轴回零..."
    BASE(1)
    DATUM(3)
    WAIT IDLE
    IF AXISSTATUS(1) AND 1 = 0 THEN
        PRINT "Y轴回零成功，位置：", DPOS(1)
    ELSE
        PRINT "Y轴回零失败"
    ENDIF
    
    PRINT "开始Z轴回零..."
    BASE(2)
    DATUM(3)
    WAIT IDLE
    IF AXISSTATUS(2) AND 1 = 0 THEN
        PRINT "Z轴回零成功，位置：", DPOS(2)
    ELSE
        PRINT "Z轴回零失败"
    ENDIF
    
    PRINT "回零演示完成"
END SUB

'=============================================================================
' 同步运动演示
'=============================================================================
SUB SyncMotionDemo()
    PRINT "=== 同步运动演示 ==="
    
    '演示1：电子齿轮
    PRINT "1. 电子齿轮演示"
    BASE(0)                     '主轴
    BASE(1)                     '从轴
    
    '建立1:2的齿轮关系
    CONNECT(0, 1, 2)            '轴1跟随轴0，比例2:1
    
    '主轴运动
    BASE(0)
    MOVE(100)
    WAIT IDLE
    
    PRINT "主轴位置：", DPOS(0)
    PRINT "从轴位置：", DPOS(1), "（应该是主轴的2倍）"
    
    '取消连接
    BASE(1)
    CANCEL(2)
    WAIT IDLE
    
    '演示2：凸轮运动
    PRINT "2. 凸轮运动演示"
    
    '准备凸轮数据（正弦曲线）
    FOR i = 0 TO 359
        TABLE(i, SIN(i * PI / 180) * 50)
    NEXT
    
    '执行凸轮运动
    BASE(1)                     '从轴
    CAM(0, 359, 1, 360)         '凸轮表运动
    
    '主轴运动驱动凸轮
    BASE(0)
    MOVE(360)
    WAIT IDLE
    
    PRINT "凸轮运动完成"
    PRINT "主轴位置：", DPOS(0)
    PRINT "从轴位置：", DPOS(1)
    
    '返回原点
    BASE(0, 1)
    MOVEABS(0, 0)
    WAIT IDLE
    
    PRINT "同步运动演示完成"
END SUB

'=============================================================================
' 状态监控子程序
'=============================================================================
SUB StatusMonitor()
    PRINT "=== 系统状态监控 ==="
    PRINT "轴0状态：", HEX(AXISSTATUS(0))
    PRINT "轴1状态：", HEX(AXISSTATUS(1))
    PRINT "轴2状态：", HEX(AXISSTATUS(2))
    
    PRINT "轴0位置：", DPOS(0), " 速度：", VP_SPEED(0)
    PRINT "轴1位置：", DPOS(1), " 速度：", VP_SPEED(1)
    PRINT "轴2位置：", DPOS(2), " 速度：", VP_SPEED(2)
    
    '检查运动状态
    IF IDLE(0) = -1 AND IDLE(1) = -1 AND IDLE(2) = -1 THEN
        PRINT "所有轴停止"
    ELSE
        PRINT "有轴在运动中"
    ENDIF
END SUB
