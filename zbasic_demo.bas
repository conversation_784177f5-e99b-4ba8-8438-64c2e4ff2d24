'=============================================================================
' Zbasic 综合演示程序
' 深圳市正运动技术有限公司
' 演示内容：基本语法、轴控制、IO操作、多任务、数据存储等
'=============================================================================

'全局变量定义
GLOBAL demo_state        '演示状态：0-初始化，1-待机，2-运行中，3-停止
GLOBAL current_step       '当前步骤
GLOBAL error_count        '错误计数
GLOBAL cycle_count        '循环计数

'常量定义
GLOBAL CONST MAX_SPEED = 1000     '最大速度
GLOBAL CONST AXIS_COUNT = 2       '轴数量
GLOBAL CONST DEMO_DISTANCE = 100  '演示运动距离

'数组定义
DIM axis_positions(2)     '轴位置数组
DIM io_states(8)          'IO状态数组

'=============================================================================
' 主程序 - 自动运行任务0
'=============================================================================
demo_state = 0
current_step = 0
error_count = 0
cycle_count = 0

'调用初始化子程序
CALL SystemInit()

'主循环
WHILE 1
    '检查输入信号
    IF SCAN_EVENT(IN(0)) > 0 THEN      'IN0上升沿 - 开始演示
        IF demo_state = 1 THEN
            demo_state = 2
            PRINT "开始运行演示程序..."
            RUNTASK 1, DemoTask         '启动演示任务
        ENDIF
    ENDIF
    
    IF SCAN_EVENT(IN(1)) > 0 THEN      'IN1上升沿 - 停止演示
        demo_state = 3
        STOPTASK 1                      '停止演示任务
        RAPIDSTOP(2)                    '紧急停止所有轴
        PRINT "演示程序已停止"
        demo_state = 1                  '返回待机状态
    ENDIF
    
    IF SCAN_EVENT(IN(2)) > 0 THEN      'IN2上升沿 - 回零
        IF demo_state = 1 THEN
            RUNTASK 2, HomeTask         '启动回零任务
        ENDIF
    ENDIF
    
    '状态指示灯控制
    SELECT CASE demo_state
        CASE 0  'INIT
            OP(0, ON)   '红灯亮
            OP(1, OFF)  '绿灯灭
        CASE 1  'READY
            OP(0, OFF)  '红灯灭
            OP(1, ON)   '绿灯亮
        CASE 2  'RUNNING
            OP(0, OFF)  '红灯灭
            OP(1, SCAN_EVENT(TICKS) AND 1)  '绿灯闪烁
        CASE 3  'ERROR
            OP(0, SCAN_EVENT(TICKS) AND 1)  '红灯闪烁
            OP(1, OFF)  '绿灯灭
    END SELECT
    
    '数据记录到VR寄存器
    VR(0) = demo_state
    VR(1) = current_step
    VR(2) = cycle_count
    VR(3) = error_count
    
    DELAY(50)  '延时50ms
WEND
END

'=============================================================================
' 系统初始化子程序
'=============================================================================
SUB SystemInit()
    PRINT "正运动Zbasic演示程序 V1.0"
    PRINT "初始化系统参数..."
    
    '轴参数设置
    BASE(0, 1)                    '选择轴0和轴1
    ATYPE = 1, 1                  '设置为脉冲轴
    UNITS = 100, 100              '脉冲当量：每单位100脉冲
    SPEED = 200, 200              '运动速度
    ACCEL = 1000, 1000            '加速度
    DECEL = 1000, 1000            '减速度
    SRAMP = 50, 50                'S曲线时间
    DPOS = 0, 0                   '位置清零
    MPOS = 0, 0                   '编码器位置清零
    
    '限位设置
    FWD_IN = 8, 9                 '正向限位输入
    REV_IN = 10, 11               '负向限位输入
    INVERT_IN(8, ON)              '反转限位信号
    INVERT_IN(9, ON)
    INVERT_IN(10, ON)
    INVERT_IN(11, ON)
    
    '插补设置
    MERGE = ON                    '开启连续插补
    CORNER_MODE = 2               '启动拐角减速
    DECEL_ANGLE = 15 * (PI/180)   '开始减速角度15度
    STOP_ANGLE = 45 * (PI/180)    '停止减速角度45度
    
    '初始化IO状态
    FOR i = 0 TO 7
        io_states(i) = 0
        OP(i, OFF)                '关闭所有输出
    NEXT
    
    '初始化TABLE数据
    TABLE(0, 50, 100, 150, 200)   '存储一些测试数据
    
    '清除VR寄存器
    FOR i = 0 TO 10
        VR(i) = 0
    NEXT
    
    demo_state = 1                '设置为待机状态
    PRINT "系统初始化完成，等待开始信号..."
END SUB

'=============================================================================
' 演示任务 - 任务1
'=============================================================================
DemoTask:
    PRINT "演示任务开始执行"
    current_step = 1
    
    '步骤1：单轴运动演示
    PRINT "步骤1：单轴运动演示"
    BASE(0)
    MOVE(DEMO_DISTANCE)
    WAIT IDLE
    axis_positions(0) = DPOS(0)
    PRINT "轴0位置：", axis_positions(0)
    
    current_step = 2
    '步骤2：双轴插补运动
    PRINT "步骤2：双轴插补运动"
    BASE(0, 1)
    MOVE(50, 50)                  '直线插补
    MOVECIRC(100, 0, 75, 25, 1)   '圆弧插补
    MOVE(-50, -50)                '返回
    WAIT IDLE
    
    current_step = 3
    '步骤3：IO操作演示
    PRINT "步骤3：IO操作演示"
    FOR i = 2 TO 5
        OP(i, ON)
        DELAY(200)
        OP(i, OFF)
        io_states(i) = IN(i+10)   '读取对应输入状态
    NEXT
    
    current_step = 4
    '步骤4：数据处理演示
    PRINT "步骤4：数据处理演示"
    LOCAL sum, average
    sum = 0
    FOR i = 0 TO 4
        sum = sum + TABLE(i)
    NEXT
    average = sum / 5
    PRINT "TABLE平均值：", average
    
    '保存数据到FLASH
    FLASH_WRITE(0, average, cycle_count, demo_state)
    
    current_step = 5
    '步骤5：模拟量操作
    PRINT "步骤5：模拟量操作"
    LOCAL analog_value
    analog_value = AIN(0)         '读取模拟量输入
    AOUT(0, analog_value * 0.8)   '输出到模拟量通道
    PRINT "模拟量输入：", analog_value
    
    cycle_count = cycle_count + 1
    current_step = 0
    demo_state = 1                '返回待机状态
    PRINT "演示任务完成，循环次数：", cycle_count
END

'=============================================================================
' 回零任务 - 任务2
'=============================================================================
HomeTask:
    PRINT "开始回零操作..."
    demo_state = 2
    
    '轴0回零
    BASE(0)
    DATUM_IN = 12                 '设置原点输入
    INVERT_IN(12, ON)             '反转原点信号
    SPEED = 100                   '回零速度
    CREEP = 20                    '爬行速度
    DATUM(3)                      '执行回零
    WAIT IDLE
    
    '轴1回零
    BASE(1)
    DATUM_IN = 13
    INVERT_IN(13, ON)
    SPEED = 100
    CREEP = 20
    DATUM(3)
    WAIT IDLE
    
    PRINT "回零操作完成"
    demo_state = 1
END

'=============================================================================
' 错误处理子程序
'=============================================================================
SUB ErrorHandler(error_code)
    error_count = error_count + 1
    demo_state = 3
    
    PRINT "错误发生，错误代码：", error_code
    PRINT "错误次数：", error_count
    
    '记录错误到VR
    VR(10) = error_code
    
    '停止所有运动
    RAPIDSTOP(2)
    
    '错误指示
    FOR i = 1 TO 5
        OP(0, ON)
        DELAY(200)
        OP(0, OFF)
        DELAY(200)
    NEXT
END SUB

'=============================================================================
' 数据显示子程序
'=============================================================================
SUB DisplayStatus()
    PRINT "========== 系统状态 =========="
    PRINT "演示状态：", demo_state
    PRINT "当前步骤：", current_step
    PRINT "循环次数：", cycle_count
    PRINT "错误次数：", error_count
    PRINT "轴0位置：", DPOS(0)
    PRINT "轴1位置：", DPOS(1)
    PRINT "轴0速度：", VP_SPEED(0)
    PRINT "轴1速度：", VP_SPEED(1)
    
    '显示IO状态
    PRINT "输入状态："
    FOR i = 0 TO 7
        PRINT "IN", i, ":", IN(i)
    NEXT
    
    PRINT "输出状态："
    FOR i = 0 TO 7
        PRINT "OUT", i, ":", io_states(i)
    NEXT
    PRINT "=========================="
END SUB
