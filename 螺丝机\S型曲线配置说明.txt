===============================================================================
                        S型曲线配置说明 - 平滑运动控制
===============================================================================

【S型曲线原理】

=== 什么是S型曲线 ===
S型曲线是一种平滑的加减速控制方式，相比传统的梯形轨迹：
✅ 加速度变化更平滑，减少机械冲击
✅ 降低振动和噪音
✅ 提高定位精度
✅ 延长机械寿命

=== 速度曲线对比 ===
```
梯形轨迹：
速度 ^
    |    ____
    |   /    \
    |  /      \
    | /        \
    |/          \
    +-------------> 时间
    加速度突变，有冲击

S型曲线：
速度 ^
    |     ___
    |   /     \
    |  /       \
    | /         \
    |/           \
    +-------------> 时间
    加速度平滑变化，无冲击
```

【SRAMP参数详解】

=== 参数定义 ===
SRAMP：S型曲线平滑时间（单位：毫秒）
- 范围：0-250ms
- 作用：控制加减速过程的平滑程度
- 设置：全局参数，对所有轴生效

=== 参数效果 ===
```
SRAMP = 0：   关闭S型曲线，使用梯形轨迹
SRAMP = 50：  轻度平滑，适合高速运动
SRAMP = 100： 中度平滑，平衡速度和平滑性
SRAMP = 150： 高度平滑，适合精密定位
SRAMP = 250： 最大平滑，适合重载或超精密场合
```

=== 时间影响 ===
设置SRAMP后，加减速过程会延长相应的时间：
- 总加速时间 = 原加速时间 + SRAMP时间
- 总减速时间 = 原减速时间 + SRAMP时间
- 运动总时间会相应增加

【螺丝机中的应用】

=== 当前配置 ===
```basic
SRAMP = 100                     ' 100ms平滑时间
SPEED = 1000, 1000, 1000, 500   ' 1m/s高速运动
ACCEL = 1000, 1000, 1000, 1000  ' 1m/s²加速度
DECEL = 1000, 1000, 1000, 1000  ' 1m/s²减速度
```

=== 优势分析 ===
✅ **减少振动**：高速运动时减少机械振动
✅ **提高精度**：平滑的加减速提高定位精度
✅ **降低噪音**：减少启停时的冲击噪音
✅ **保护机械**：减少对导轨、丝杠等的冲击

=== 实际效果 ===
在1m/s高速运动下：
- 加速过程：更平滑，减少冲击
- 匀速过程：稳定运行
- 减速过程：平滑停止，提高定位精度
- 整体运动：更加平稳可靠

【参数调优指南】

=== 选择原则 ===
1. **高速运动**：选择较小的SRAMP值（50-100ms）
2. **精密定位**：选择较大的SRAMP值（100-150ms）
3. **重载应用**：选择更大的SRAMP值（150-250ms）
4. **平衡考虑**：在速度和平滑性之间找平衡

=== 推荐设置 ===
```
螺丝机应用推荐：
SRAMP = 100     ' 平衡速度和平滑性

其他应用参考：
SRAMP = 50      ' 高速包装机械
SRAMP = 100     ' 一般自动化设备
SRAMP = 150     ' 精密加工设备
SRAMP = 200     ' 重载搬运设备
```

=== 调试方法 ===
1. **从小开始**：先设置SRAMP = 50
2. **观察效果**：检查运动平滑性和振动情况
3. **逐步增加**：如需更平滑，逐步增加到100、150
4. **性能平衡**：在平滑性和速度之间找到最佳平衡

【实际测试建议】

=== 测试步骤 ===
1. **基础测试**：
   ```basic
   SRAMP = 50
   SPEED = 500     ' 先用较低速度测试
   ```

2. **逐步提升**：
   ```basic
   SRAMP = 100
   SPEED = 1000    ' 提升到目标速度
   ```

3. **精细调整**：
   ```basic
   根据实际效果调整SRAMP值
   ```

=== 观察要点 ===
✅ **振动情况**：启停时是否有明显振动
✅ **噪音水平**：运动噪音是否降低
✅ **定位精度**：停止位置是否准确
✅ **运动时间**：总运动时间是否可接受

【常见问题解答】

=== Q1：SRAMP设置过大会怎样？ ===
A：加减速时间过长，影响整体效率，但运动更平滑

=== Q2：SRAMP设置过小会怎样？ ===
A：平滑效果不明显，可能仍有振动和冲击

=== Q3：不同轴可以设置不同的SRAMP吗？ ===
A：SRAMP是全局参数，对所有轴生效，无法单独设置

=== Q4：如何判断SRAMP设置是否合适？ ===
A：观察运动平滑性、定位精度和总运动时间的平衡

=== Q5：S型曲线会影响插补运动吗？ ===
A：会影响，多轴插补时所有轴都使用S型曲线

【与其他参数的配合】

=== 速度参数 ===
```basic
SPEED = 1000    ' 高速度配合适中的SRAMP
SRAMP = 100     ' 平衡速度和平滑性
```

=== 加速度参数 ===
```basic
ACCEL = 1000    ' 高加速度需要更大的SRAMP
SRAMP = 100     ' 平滑高加速度的冲击
```

=== 插补参数 ===
```basic
MERGE = ON      ' 连续插补
CORNER_MODE = 2 ' 拐角减速
SRAMP = 100     ' S型曲线平滑
```

【性能对比】

=== 运动时间对比 ===
```
100mm移动距离，1m/s速度，1m/s²加速度：

SRAMP = 0：   总时间约0.2秒
SRAMP = 50：  总时间约0.25秒
SRAMP = 100： 总时间约0.3秒
SRAMP = 150： 总时间约0.35秒
```

=== 平滑性对比 ===
```
SRAMP = 0：   ★☆☆☆☆ （梯形轨迹）
SRAMP = 50：  ★★★☆☆ （轻度平滑）
SRAMP = 100： ★★★★☆ （中度平滑）
SRAMP = 150： ★★★★★ （高度平滑）
```

【总结】

螺丝机采用SRAMP = 100的S型曲线配置：
✅ **平衡性好**：在速度和平滑性之间取得良好平衡
✅ **适合高速**：支持1m/s高速运动
✅ **减少振动**：显著降低机械振动和冲击
✅ **提高精度**：改善定位精度和重复性
✅ **延长寿命**：减少机械磨损，延长设备寿命

这个配置适合螺丝机的高速高精度要求，既保证了生产效率，又确保了运动质量。

===============================================================================
