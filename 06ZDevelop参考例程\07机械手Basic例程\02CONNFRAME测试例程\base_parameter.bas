
global sub base_parameter()
while 1

MODBUS_REG(450)=10
MODBUS_IEEE(900)=dpos(0)
MODBUS_IEEE(902)=dpos(1)
MODBUS_IEEE(904)=dpos(2)
MODBUS_IEEE(906)=dpos(3)
MODBUS_IEEE(908)=dpos(4)
MODBUS_IEEE(910)=dpos(5)


MODBUS_IEEE(950)=mpos(0)
MODBUS_IEEE(952)=mpos(1)
MODBUS_IEEE(954)=mpos(2)
MODBUS_IEEE(956)=mpos(3)
MODBUS_IEEE(958)=mpos(4)
MODBUS_IEEE(960)=mpos(5)

MODBUS_IEEE(1000)=VP_SPEED(0)
MODBUS_IEEE(1002)=VP_SPEED(1)
MODBUS_IEEE(1004)=VP_SPEED(2)
MODBUS_IEEE(1006)=VP_SPEED(3)
MODBUS_IEEE(1008)=VP_SPEED(4)
MODBUS_IEEE(1010)=VP_SPEED(5)


MODBUS_IEEE(1050)=MSPEED(0)
MODBUS_IEEE(1052)=MSPEED(1)
MODBUS_IEEE(1054)=MSPEED(2)
MODBUS_IEEE(1056)=MSPEED(3)
MODBUS_IEEE(1058)=MSPEED(4)
MODBUS_IEEE(1060)=MSPEED(5)

MODBUS_LONG(2000)=ATYPE(0)
MODBUS_LONG(2002)=ATYPE(1)
MODBUS_LONG(2004)=ATYPE(2)
MODBUS_LONG(2006)=ATYPE(3)
MODBUS_LONG(2008)=ATYPE(4)
MODBUS_LONG(2010)=ATYPE(5)

MODBUS_LONG(800)=MOVES_BUFFERED(0)
MODBUS_LONG(802)=MOVES_BUFFERED(1)
MODBUS_LONG(804)=MOVES_BUFFERED(2)
MODBUS_LONG(806)=MOVES_BUFFERED(3)
MODBUS_LONG(808)=MOVES_BUFFERED(4)
MODBUS_LONG(810)=MOVES_BUFFERED(5)

MODBUS_LONG(820)=REMAIN_BUFFER(0)
MODBUS_LONG(822)=REMAIN_BUFFER(1)
MODBUS_LONG(824)=REMAIN_BUFFER(2)
MODBUS_LONG(826)=REMAIN_BUFFER(3)
MODBUS_LONG(828)=REMAIN_BUFFER(4)
MODBUS_LONG(830)=REMAIN_BUFFER(5)

MODBUS_LONG(850)=MTYPE(0)
MODBUS_LONG(852)=MTYPE(1)
MODBUS_LONG(854)=MTYPE(2)
MODBUS_LONG(856)=MTYPE(3)
MODBUS_LONG(858)=MTYPE(4)
MODBUS_LONG(860)=MTYPE(5)


MODBUS_LONG(880)=NTYPE(0)
MODBUS_LONG(882)=NTYPE(1)
MODBUS_LONG(884)=NTYPE(2)
MODBUS_LONG(886)=NTYPE(3)
MODBUS_LONG(888)=NTYPE(4)
MODBUS_LONG(880)=NTYPE(5)



MODBUS_LONG(700)=MOVE_MARK(0)
MODBUS_LONG(702)=MOVE_MARK(1)
MODBUS_LONG(704)=MOVE_MARK(2)
MODBUS_LONG(706)=MOVE_MARK(3)
MODBUS_LONG(708)=MOVE_MARK(4)
MODBUS_LONG(710)=MOVE_MARK(5)



MODBUS_LONG(730)=MOVE_CURMARK(0)
MODBUS_LONG(732)=MOVE_CURMARK(1)
MODBUS_LONG(734)=MOVE_CURMARK(2)
MODBUS_LONG(736)=MOVE_CURMARK(3)
MODBUS_LONG(738)=MOVE_CURMARK(4)
MODBUS_LONG(740)=MOVE_CURMARK(5)
wend

end sub