'===============================================================================
'                        简单输入测试程序 - 基于官方例程
'                        参考sample_输入检测例程，测试输入电平功能
'===============================================================================

'错误处理设置
ERRSWITCH = 3

'设置VP_SPEED显示单轴速度
SYSTEM_ZSET = SYSTEM_ZSET AND (NOT 1)   ' 清除bit0，VP_SPEED使用单轴速度

'设置基础轴组
BASE(0, 1)                              ' X轴为主轴，Y轴为从轴
DPOS = 0, 0                             ' 坐标清零
UNITS = 100, 100                        ' 脉冲当量100
SPEED = 100, 100                        ' 主轴速度100units/s
ACCEL = 1000, 1000                      ' 加速度1000units/s²
DECEL = 1000, 1000                      ' 减速度1000units/s²
SRAMP = 50, 50                          ' S曲线时间50ms
VP_MODE = 7, 7                          ' SS曲线

PRINT "==============================================================================="
PRINT "                        简单输入测试程序"
PRINT "==============================================================================="
PRINT "程序功能：测试输入电平检测和基本插补功能"
PRINT "输入分配："
PRINT "IN0 - 直线插补测试"
PRINT "IN1 - 圆弧插补测试"
PRINT "IN2 - 连续插补测试"
PRINT "IN3 - 显示状态信息"
PRINT "==============================================================================="
PRINT "程序开始监控输入，请给输入引脚施加24V信号..."
PRINT ""

'主循环 - 参考官方sample_输入检测例程
WHILE 1
    '使用官方推荐的输入检测方式
    IF IN_SCAN(0, 7) THEN               ' 只有IO有变动的时候才进循环
        FOR i = 0 TO 7
            IF IN_EVENT(i) > 0 THEN     ' 检测上升沿
                PRINT "*** 检测到IN", i, "上升沿触发 ***"
                
                '设置示波器
                TRIGGER
                PRINT "示波器已触发，监控VP_SPEED(0)和VP_SPEED(1)"
                
                IF i = 0 THEN
                    '测试1：直线插补
                    PRINT "执行直线插补测试：MOVE(50, 50)"
                    DPOS = 0, 0
                    FORCE_SPEED = 80
                    MOVE(50, 50)
                    WAIT IDLE
                    PRINT "直线插补完成，位置：(", DPOS(0), ", ", DPOS(1), ")"
                    
                ELSEIF i = 1 THEN
                    '测试2：圆弧插补
                    PRINT "执行圆弧插补测试"
                    MOVEABS(50, 50)
                    WAIT IDLE
                    FORCE_SPEED = 60
                    MOVECIRC2ABS(25, 75, 0, 50)
                    WAIT IDLE
                    PRINT "圆弧插补完成，位置：(", DPOS(0), ", ", DPOS(1), ")"
                    
                ELSEIF i = 2 THEN
                    '测试3：连续插补
                    PRINT "执行连续插补测试（矩形轨迹）"
                    DPOS = 0, 0
                    MERGE = ON
                    CORNER_MODE = 32
                    ZSMOOTH = 5
                    FORCE_SPEED = 80
                    
                    MOVE(40, 0)
                    MOVE(0, 40)
                    MOVE(-40, 0)
                    MOVE(0, -40)
                    
                    WAIT IDLE
                    MERGE = OFF
                    CORNER_MODE = 0
                    PRINT "连续插补完成，位置：(", DPOS(0), ", ", DPOS(1), ")"
                    
                ELSEIF i = 3 THEN
                    '显示状态
                    PRINT "=== 系统状态信息 ==="
                    PRINT "控制器型号：", ?*model
                    PRINT "固件版本：", ?*version
                    PRINT "当前位置：(", DPOS(0), ", ", DPOS(1), ")"
                    PRINT "轴状态：AXIS(0)=", AXIS(0), " AXIS(1)=", AXIS(1)
                    PRINT "SYSTEM_ZSET=", SYSTEM_ZSET
                    PRINT "输入状态：IN0=", IN(0), " IN1=", IN(1), " IN2=", IN(2), " IN3=", IN(3)
                    
                ELSE
                    '其他输入
                    PRINT "IN", i, "触发，执行简单响应"
                    OUT(0) = 1
                    DELAY 500
                    OUT(0) = 0
                ENDIF
                
                PRINT "测试完成，继续监控输入..."
                PRINT ""
            ENDIF
        NEXT i
    ENDIF
    
    DELAY 100                           ' 延时100ms
WEND

END
