'=============================================================================
' Zbasic 简单入门演示程序
' 适合初学者学习Zbasic基本语法和运动控制
'=============================================================================

'变量定义示例
DIM counter                '定义一个变量
DIM position_x, position_y '定义两个位置变量

'常量定义示例
CONST MOVE_DISTANCE = 100  '运动距离常量
CONST AXIS_SPEED = 200     '轴速度常量

'=============================================================================
' 主程序开始
'=============================================================================
PRINT "欢迎使用Zbasic编程！"
PRINT "这是一个简单的入门演示程序"

'初始化计数器
counter = 0

'调用轴初始化子程序
CALL AxisInit()

'主循环演示
WHILE counter < 5
    counter = counter + 1
    PRINT "循环次数：", counter
    
    '演示基本运动
    CALL SimpleMove()
    
    '演示IO操作
    CALL IODemo()
    
    '延时1秒
    DELAY(1000)
WEND

PRINT "演示程序结束"
END

'=============================================================================
' 轴初始化子程序
'=============================================================================
SUB AxisInit()
    PRINT "初始化轴参数..."
    
    '选择轴0
    BASE(0)
    
    '设置轴类型为脉冲轴
    ATYPE = 1
    
    '设置脉冲当量（每单位100个脉冲）
    UNITS = 100
    
    '设置运动速度
    SPEED = AXIS_SPEED
    
    '设置加速度和减速度
    ACCEL = 1000
    DECEL = 1000
    
    '设置S曲线时间
    SRAMP = 50
    
    '位置清零
    DPOS = 0
    
    PRINT "轴初始化完成"
END SUB

'=============================================================================
' 简单运动演示
'=============================================================================
SUB SimpleMove()
    PRINT "开始运动演示..."
    
    '选择轴0
    BASE(0)
    
    '正向运动
    PRINT "正向运动", MOVE_DISTANCE, "个单位"
    MOVE(MOVE_DISTANCE)
    WAIT IDLE                '等待运动完成
    
    '获取当前位置
    position_x = DPOS(0)
    PRINT "当前位置：", position_x
    
    '延时500ms
    DELAY(500)
    
    '负向运动回到原点
    PRINT "返回原点"
    MOVE(-MOVE_DISTANCE)
    WAIT IDLE
    
    position_x = DPOS(0)
    PRINT "返回后位置：", position_x
    
    PRINT "运动演示完成"
END SUB

'=============================================================================
' IO操作演示
'=============================================================================
SUB IODemo()
    PRINT "IO操作演示..."
    
    '输出控制演示
    PRINT "点亮输出口0"
    OP(0, ON)               '打开输出口0
    DELAY(300)
    
    PRINT "关闭输出口0"
    OP(0, OFF)              '关闭输出口0
    DELAY(300)
    
    '输出口闪烁演示
    PRINT "输出口1闪烁3次"
    FOR i = 1 TO 3
        OP(1, ON)
        DELAY(200)
        OP(1, OFF)
        DELAY(200)
    NEXT
    
    '读取输入状态
    LOCAL input_state
    input_state = IN(0)     '读取输入口0状态
    PRINT "输入口0状态：", input_state
    
    '条件判断示例
    IF input_state = ON THEN
        PRINT "输入口0有信号"
        OP(2, ON)           '输入有信号时点亮输出口2
    ELSE
        PRINT "输入口0无信号"
        OP(2, OFF)
    ENDIF
    
    PRINT "IO演示完成"
END SUB

'=============================================================================
' 双轴插补运动演示（可选）
'=============================================================================
SUB TwoAxisDemo()
    PRINT "双轴插补演示..."
    
    '选择轴0和轴1
    BASE(0, 1)
    
    '设置两轴参数
    ATYPE = 1, 1            '都设为脉冲轴
    UNITS = 100, 100        '脉冲当量
    SPEED = 150, 150        '速度
    ACCEL = 800, 800        '加速度
    DECEL = 800, 800        '减速度
    DPOS = 0, 0             '位置清零
    
    '开启连续插补
    MERGE = ON
    
    '直线插补运动
    PRINT "执行直线插补"
    MOVE(50, 50)            '两轴同时运动到(50,50)
    WAIT IDLE
    
    '圆弧插补运动
    PRINT "执行圆弧插补"
    MOVECIRC(100, 0, 75, 25, 1)  '画圆弧
    WAIT IDLE
    
    '返回原点
    PRINT "返回原点"
    MOVEABS(0, 0)           '绝对位置运动到原点
    WAIT IDLE
    
    '获取最终位置
    position_x = DPOS(0)
    position_y = DPOS(1)
    PRINT "最终位置：X=", position_x, " Y=", position_y
    
    PRINT "双轴演示完成"
END SUB

'=============================================================================
' 数据处理演示
'=============================================================================
SUB DataDemo()
    PRINT "数据处理演示..."
    
    '数组操作示例
    DIM test_array(5)
    
    '给数组赋值
    FOR i = 0 TO 4
        test_array(i) = i * 10
        PRINT "数组[", i, "] = ", test_array(i)
    NEXT
    
    '计算数组和
    LOCAL sum
    sum = 0
    FOR i = 0 TO 4
        sum = sum + test_array(i)
    NEXT
    PRINT "数组元素总和：", sum
    
    '使用TABLE存储数据
    TABLE(0, 10, 20, 30, 40, 50)
    PRINT "TABLE数据已存储"
    
    '读取TABLE数据
    FOR i = 0 TO 4
        PRINT "TABLE[", i, "] = ", TABLE(i)
    NEXT
    
    '数学运算示例
    LOCAL result
    result = SIN(3.14159/4)      '计算sin(45度)
    PRINT "sin(45°) = ", result

    result = 4       '计算平方根（简化）
    PRINT "sqrt(16) = ", result
    
    PRINT "数据处理演示完成"
END SUB

'=============================================================================
' 条件判断和循环演示
'=============================================================================
SUB ControlDemo()
    PRINT "程序控制结构演示..."
    
    '条件判断演示
    LOCAL test_value
    test_value = 15
    
    IF test_value > 10 THEN
        PRINT "值大于10"
    ELSEIF test_value = 10 THEN
        PRINT "值等于10"
    ELSE
        PRINT "值小于10"
    ENDIF
    
    '多条件判断演示
    IF test_value = 10 THEN
        PRINT "值是10"
    ELSEIF test_value = 15 THEN
        PRINT "值是15"
    ELSE
        PRINT "其他值"
    ENDIF
    
    'FOR循环演示
    PRINT "FOR循环计数："
    FOR i = 1 TO 5
        PRINT "计数：", i
    NEXT
    
    'WHILE循环演示
    PRINT "WHILE循环演示："
    LOCAL count
    count = 1
    WHILE count <= 3
        PRINT "WHILE计数：", count
        count = count + 1
    WEND
    
    'REPEAT循环演示
    PRINT "REPEAT循环演示："
    count = 1
    REPEAT
        PRINT "REPEAT计数：", count
        count = count + 1
    UNTIL count > 3
    
    PRINT "控制结构演示完成"
END SUB