'=============================================================================
' 螺丝机测试版（无需实际硬件）
' 专门用于测试多线程架构和逻辑，无需连接实际硬件
' 所有运动和回零都是模拟的
'=============================================================================

'全局变量定义
GLOBAL sys_status                  ' 系统状态：0-待机，1-运行中，2-回零中
GLOBAL axis_home(5)                ' 各轴回零状态：0-未回零，1-回零中，2-已回零，3-失败
GLOBAL left_screw_num              ' 左侧螺丝点位数量
GLOBAL right_screw_num             ' 右侧螺丝点位数量
GLOBAL cur_screw                   ' 当前打螺丝序号

'双Y轴滑轨控制变量
GLOBAL left_user_pos = 50           ' 左侧用户位置
GLOBAL left_work_pos = 80           ' 左侧工作位置
GLOBAL right_user_pos = 200         ' 右侧用户位置
GLOBAL right_work_pos = 220         ' 右侧工作位置

GLOBAL left_slide_status = 0        ' 左侧滑轨状态：0-用户侧，1-工作侧，2-移动中
GLOBAL right_slide_status = 0       ' 右侧滑轨状态：0-用户侧，1-工作侧，2-移动中

'队列控制变量
GLOBAL left_queue = 0               ' 左侧队列状态：0-无任务，1-等待中，2-执行中
GLOBAL right_queue = 0              ' 右侧队列状态：0-无任务，1-等待中，2-执行中
GLOBAL screwdriver_busy = 0         ' 电批忙碌状态：0-空闲，1-忙碌

'任务状态变量
GLOBAL task_home_running = 0        ' 回零任务运行状态
GLOBAL task_screw_running = 0       ' 打螺丝任务运行状态
GLOBAL screw_task_stop = 0          ' 打螺丝任务停止标志

'测试模式变量
GLOBAL test_mode = 1                ' 测试模式：1-启用，0-禁用

'主程序（任务0）
CALL InitSystem()
CALL SetupData()

PRINT "=== 螺丝机测试版启动 ==="
PRINT "测试模式：所有运动和回零都是模拟的"
PRINT "多任务架构："
PRINT "任务0：主控制（输入扫描、状态管理）"
PRINT "任务1：回零任务（模拟回零）"
PRINT "任务2：打螺丝任务（模拟打螺丝）"
PRINT ""
PRINT "操作说明："
PRINT "IN0 - 左侧开始（可在任何时候按下）"
PRINT "IN1 - 右侧开始（可在任何时候按下）"
PRINT "IN2 - 系统回零（模拟）"
PRINT "IN3 - 急停"
PRINT "IN4 - 手动左Y轴到用户侧"
PRINT "IN5 - 手动右Y轴到用户侧"

'启动打螺丝任务
PRINT "启动打螺丝任务..."
screw_task_stop = 0
RUNTASK 2, TestScrewTask
task_screw_running = 1

'主循环
WHILE 1
    CALL ScanInput()
    CALL UpdateTaskStatus()
    CALL UpdateStatus()
    DELAY(50)
WEND
END

'================ 系统初始化 ================
GLOBAL SUB InitSystem()
    sys_status = 0
    left_screw_num = 4
    right_screw_num = 4
    cur_screw = 0
    
    '初始化回零状态
    FOR i = 0 TO 3
        axis_home(i) = 0
    NEXT
    
    '初始化队列状态
    left_queue = 0
    right_queue = 0
    screwdriver_busy = 0
    left_slide_status = 0
    right_slide_status = 0
    
    '初始化任务状态
    task_home_running = 0
    task_screw_running = 0
    screw_task_stop = 0
    
    PRINT "系统初始化完成（测试模式）"
END SUB

'================ 数据设置 ================
GLOBAL SUB SetupData()
    PRINT "数据设置完成（测试模式）"
    PRINT "左侧螺丝数量：", left_screw_num
    PRINT "右侧螺丝数量：", right_screw_num
    PRINT "左侧用户位置：", left_user_pos, "mm，工作位置：", left_work_pos, "mm"
    PRINT "右侧用户位置：", right_user_pos, "mm，工作位置：", right_work_pos, "mm"
END SUB

'================ 输入扫描 ================
GLOBAL SUB ScanInput()
    '左侧开始按键
    IF SCAN_EVENT(IN(0)) > 0 THEN
        IF CheckHome() = 1 THEN
            PRINT "左侧作业请求"
            IF left_queue = 0 THEN
                left_queue = 1
                PRINT "左侧任务加入队列"
                CALL LeftSlideToWork()
            ELSE
                PRINT "左侧任务已在队列中"
            ENDIF
        ELSE
            PRINT "系统未回零，无法开始作业"
        ENDIF
    ENDIF
    
    '右侧开始按键  
    IF SCAN_EVENT(IN(1)) > 0 THEN
        IF CheckHome() = 1 THEN
            PRINT "右侧作业请求"
            IF right_queue = 0 THEN
                right_queue = 1
                PRINT "右侧任务加入队列"
                CALL RightSlideToWork()
            ELSE
                PRINT "右侧任务已在队列中"
            ENDIF
        ELSE
            PRINT "系统未回零，无法开始作业"
        ENDIF
    ENDIF
    
    '系统回零
    IF SCAN_EVENT(IN(2)) > 0 THEN
        IF task_home_running = 0 THEN
            PRINT "开始系统回零（模拟）"
            STOPTASK 1
            RUNTASK 1, TestHomeTask
            task_home_running = 1
        ELSE
            PRINT "回零任务正在运行中"
        ENDIF
    ENDIF
    
    '手动控制
    IF SCAN_EVENT(IN(4)) > 0 THEN
        IF left_queue = 0 THEN
            PRINT "手动左Y轴到用户侧（模拟）"
            CALL LeftSlideToUser()
        ENDIF
    ENDIF
    
    IF SCAN_EVENT(IN(5)) > 0 THEN
        IF right_queue = 0 THEN
            PRINT "手动右Y轴到用户侧（模拟）"
            CALL RightSlideToUser()
        ENDIF
    ENDIF
    
    '急停
    IF SCAN_EVENT(IN(3)) > 0 THEN
        PRINT "急停触发！（模拟）"
        STOPTASK 1
        screw_task_stop = 1
        
        sys_status = 0
        left_queue = 0
        right_queue = 0
        screwdriver_busy = 0
        task_home_running = 0
        
        PRINT "所有任务已停止"
        
        DELAY(1000)
        screw_task_stop = 0
        STOPTASK 2
        RUNTASK 2, TestScrewTask
        task_screw_running = 1
        PRINT "打螺丝任务已重启"
    ENDIF
END SUB

'================ 更新任务状态 ================
GLOBAL SUB UpdateTaskStatus()
    IF task_home_running = 1 THEN
        IF PROC_STATUS(1) = 0 THEN
            PRINT "回零任务完成"
            task_home_running = 0
            sys_status = 0
        ENDIF
    ENDIF
    
    IF task_screw_running = 1 THEN
        IF PROC_STATUS(2) = 0 THEN
            PRINT "打螺丝任务异常停止，重新启动"
            screw_task_stop = 0
            RUNTASK 2, TestScrewTask
            task_screw_running = 1
        ENDIF
    ENDIF
END SUB

'================ 模拟滑轨控制 ================
GLOBAL SUB LeftSlideToUser()
    IF left_slide_status <> 0 THEN
        PRINT "左Y轴移动到用户位置（模拟）：", left_user_pos, "mm"
        left_slide_status = 2
        DELAY(1000)  ' 模拟移动时间
        left_slide_status = 0
        PRINT "左Y轴已到达用户位置"
    ENDIF
END SUB

GLOBAL SUB LeftSlideToWork()
    IF left_slide_status <> 1 THEN
        PRINT "左Y轴移动到工作位置（模拟）：", left_work_pos, "mm"
        left_slide_status = 2
        DELAY(1000)  ' 模拟移动时间
        left_slide_status = 1
        PRINT "左Y轴已到达工作位置"
    ENDIF
END SUB

GLOBAL SUB RightSlideToUser()
    IF right_slide_status <> 0 THEN
        PRINT "右Y轴移动到用户位置（模拟）：", right_user_pos, "mm"
        right_slide_status = 2
        DELAY(1000)  ' 模拟移动时间
        right_slide_status = 0
        PRINT "右Y轴已到达用户位置"
    ENDIF
END SUB

GLOBAL SUB RightSlideToWork()
    IF right_slide_status <> 1 THEN
        PRINT "右Y轴移动到工作位置（模拟）：", right_work_pos, "mm"
        right_slide_status = 2
        DELAY(1000)  ' 模拟移动时间
        right_slide_status = 1
        PRINT "右Y轴已到达工作位置"
    ENDIF
END SUB

'================ 检查回零状态 ================
GLOBAL SUB CheckHome()
    FOR i = 0 TO 3
        IF axis_home(i) <> 2 THEN
            RETURN 0
        ENDIF
    NEXT
    RETURN 1
END SUB

'================ 状态更新 ================
GLOBAL SUB UpdateStatus()
    '更新Modbus寄存器
    MODBUS_REG(0) = sys_status
    MODBUS_REG(1) = left_screw_num
    MODBUS_REG(2) = right_screw_num
    MODBUS_REG(3) = cur_screw
    MODBUS_REG(4) = left_slide_status
    MODBUS_REG(5) = right_slide_status
    MODBUS_REG(6) = left_queue
    MODBUS_REG(7) = right_queue
    MODBUS_REG(8) = screwdriver_busy
    MODBUS_REG(9) = task_home_running
    MODBUS_REG(10) = task_screw_running
    MODBUS_REG(11) = screw_task_stop
    
    FOR i = 0 TO 3
        MODBUS_REG(20 + i) = axis_home(i)
    NEXT
END SUB

'================ 显示系统状态 ================
GLOBAL SUB ShowStatus()
    PRINT "=== 系统状态（测试模式）==="
    PRINT "系统状态：", sys_status
    PRINT "当前螺丝：", cur_screw
    PRINT "左侧螺丝数量：", left_screw_num
    PRINT "右侧螺丝数量：", right_screw_num
    
    PRINT "=== 双Y轴滑轨状态 ==="
    PRINT "左Y轴状态：", left_slide_status
    PRINT "右Y轴状态：", right_slide_status
    
    PRINT "=== 队列状态 ==="
    PRINT "左侧队列：", left_queue
    PRINT "右侧队列：", right_queue
    PRINT "电批状态：", screwdriver_busy
    
    PRINT "=== 任务运行状态 ==="
    PRINT "回零任务：", task_home_running
    PRINT "打螺丝任务：", task_screw_running
    PRINT "任务停止标志：", screw_task_stop
    
    PRINT "=== 轴回零状态 ==="
    FOR i = 0 TO 3
        PRINT "轴", i, "：", axis_home(i)
    NEXT
END SUB

'================ 测试回零任务 ================
GLOBAL SUB TestHomeTask()
    sys_status = 2
    PRINT "开始四轴回零（模拟）..."
    
    PRINT "模拟轴3 (Z轴) 回零..."
    axis_home(3) = 1
    DELAY(500)
    axis_home(3) = 2
    PRINT "轴3回零成功（模拟）"
    
    PRINT "模拟轴2 (Y2轴) 回零..."
    axis_home(2) = 1
    DELAY(500)
    axis_home(2) = 2
    PRINT "轴2回零成功（模拟）"
    
    PRINT "模拟轴1 (Y1轴) 回零..."
    axis_home(1) = 1
    DELAY(500)
    axis_home(1) = 2
    PRINT "轴1回零成功（模拟）"
    
    PRINT "模拟轴0 (X轴) 回零..."
    axis_home(0) = 1
    DELAY(500)
    axis_home(0) = 2
    PRINT "轴0回零成功（模拟）"
    
    PRINT "双Y轴移动到用户位置（模拟）..."
    CALL LeftSlideToUser()
    CALL RightSlideToUser()
    
    PRINT "所有轴回零完成（模拟），双Y轴在用户位置"
END SUB

'================ 测试打螺丝任务 ================
GLOBAL SUB TestScrewTask()
    PRINT "打螺丝任务启动，开始监控左右两侧队列（模拟）"
    
    WHILE screw_task_stop = 0
        IF screwdriver_busy = 0 THEN
            IF left_queue = 1 AND left_slide_status = 1 THEN
                PRINT "开始执行左侧打螺丝（模拟）"
                left_queue = 2
                screwdriver_busy = 1
                CALL TestExecuteLeftScrew()
                
            ELSEIF right_queue = 1 AND right_slide_status = 1 THEN
                PRINT "开始执行右侧打螺丝（模拟）"
                right_queue = 2
                screwdriver_busy = 1
                CALL TestExecuteRightScrew()
            ENDIF
        ENDIF
        
        DELAY(100)
    WEND
    
    PRINT "打螺丝任务停止"
END SUB

'================ 模拟执行左侧打螺丝 ================
GLOBAL SUB TestExecuteLeftScrew()
    PRINT "执行左侧打螺丝任务（模拟）"
    
    FOR screw_idx = 0 TO left_screw_num - 1
        IF screw_task_stop = 1 THEN
            PRINT "收到停止信号，左侧任务中断"
            GOTO left_task_end
        ENDIF
        
        cur_screw = screw_idx + 1
        PRINT "开始打第", cur_screw, "个螺丝（左侧，模拟）"
        
        PRINT "模拟确保在统一取螺丝位置(50,150,10)..."
        DELAY(300)
        PRINT "模拟吸取螺丝..."
        DELAY(500)
        PRINT "模拟Y轴移动到目标位置+三段连续轨迹到螺丝孔位（MOVECIRC2ABS真圆弧，中间不停）..."
        DELAY(500)
        PRINT "模拟电批锁紧到30mm深度..."
        DELAY(1000)
        PRINT "模拟Y轴移动+三段连续轨迹回统一取螺丝位置（中间不停）..."
        DELAY(500)
        PRINT "螺丝完成（模拟）- 统一取螺丝位置，三段连续轨迹"
        
        DELAY(500)
    NEXT
    
    left_task_end:
    PRINT "左侧打螺丝任务完成（模拟）"
    
    left_queue = 0
    screwdriver_busy = 0
    cur_screw = 0
    
    CALL LeftSlideToUser()
    PRINT "左侧任务完成，左Y轴已回到用户位置"
END SUB

'================ 模拟执行右侧打螺丝 ================
GLOBAL SUB TestExecuteRightScrew()
    PRINT "执行右侧打螺丝任务（模拟）"
    
    FOR screw_idx = 0 TO right_screw_num - 1
        IF screw_task_stop = 1 THEN
            PRINT "收到停止信号，右侧任务中断"
            GOTO right_task_end
        ENDIF
        
        cur_screw = screw_idx + 1
        PRINT "开始打第", cur_screw, "个螺丝（右侧，模拟）"
        
        PRINT "模拟确保在统一取螺丝位置(50,150,10)..."
        DELAY(300)
        PRINT "模拟吸取螺丝..."
        DELAY(500)
        PRINT "模拟Y轴移动到目标位置+三段连续轨迹到螺丝孔位（MOVECIRC2ABS真圆弧，中间不停）..."
        DELAY(500)
        PRINT "模拟电批锁紧到30mm深度..."
        DELAY(1000)
        PRINT "模拟Y轴移动+三段连续轨迹回统一取螺丝位置（中间不停）..."
        DELAY(500)
        PRINT "螺丝完成（模拟）- 统一取螺丝位置，三段连续轨迹"
        
        DELAY(500)
    NEXT
    
    right_task_end:
    PRINT "右侧打螺丝任务完成（模拟）"
    
    right_queue = 0
    screwdriver_busy = 0
    cur_screw = 0
    
    CALL RightSlideToUser()
    PRINT "右侧任务完成，右Y轴已回到用户位置"
END SUB
