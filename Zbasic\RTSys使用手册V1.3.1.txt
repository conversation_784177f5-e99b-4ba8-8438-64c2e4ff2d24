RTSys 使用手册
Version 1.3.1



版权说明



为了更好的服务于广大受众，深圳市正运动技术有限公司，对所发布的信息（含文字、数据、图片等）作出以下声明：
本手册版权归深圳市正运动技术有限公司所有，严禁任何媒体、网站、个人或组织以任何形式或出于任何目的在未经本公司书面授权的情况下抄袭、转载、摘编、修改本手册内容，或链接、转贴或以其他方式复制用于商业目的或发行，或稍作修改后作为他用，前述行为均将构成对本公司手册版权之侵犯，本司将依法追究其法律责任。
涉及运动控制器软件每个指令和函数的介绍和范例，请参阅正运动技术公司

RTBasic/RTPlc/RTHmi 编程手册。

本手册中的信息资料仅供参考。如涉及产品升级，内容需要更改，恕不另行通知！正运动技术公司保留对本资料的最终解释权！如需获取更多详情请登陆正运动技术公司网站。



调试机器要注意安全！请务必在机器中设计有效的安全保护装置，并在软件中加入出错处理程序，否则所造成的损失，正运动技术公司没有义务或责任对此负责。




修订记录

更新日期	版本号	版本（更改）说明	更改人
2023/6/21	V1.0.0	RTSys 使用手册初版发布	zxl


2024/4/28	

V1.1.0	1.文件视图更新为工程视图；
2.工程视图新增EtherCAT 节点配置、轴配置等功能；
3.工具新增 PWM 和 SDO；
4.新增支持C 语言编译及编译平台设置；
5.部分菜单调整和内容补充；	

zxl

2024/5/10	
V1.1.1	1.新增附录C：EtherCAT 配置操作指引及流程图；

2.补充完善工程视图章节内容；

3.插件工具新增支持.bat 格式	
zxl


2024/8/5	

V1.2.0	1.示波器版本内容更新；

2.常见问题章节新增 3 个问题现象；

3.工程视图少部分内容调整；

4.修改“输入口”工具界面图。	

zxl

2024/10/8	
V1.3.0	1.优化HMI 功能：新增控件等；
2.示波器优化；

3.新增常见问题；	
zxl

2025/1/16	
V1.3.1	1.“设置”界面优化：新增 gcc 自定义编译；

2.“生成 ZAR”界面优化：新增 gcc 自定义编译；	
zxl



目录

第一章	RTSys 简介	1
系统要求	1
RTSys 安装/卸载/升级	2
1.2.1RTSys 安装	2
1.2.2RTSys 卸载步骤	4
1.2.3在线升级	5
RTSys 主界面	7
RTSys 操作说明	8
离线仿真	14
命令与输出	15
报警查看	23
紧急停止	26
自动备份	26
窗口停靠	27
编程指引	28
1.11.1Basic 编辑	28
1.11.2梯形图编辑	31
1.11.3HMI 组态编辑	39
使用帮助	43
第二章	控制器操作	49
连接	50
2.1.1连接控制器	50
2.1.2连接仿真器	55
2.1.3断开连接	56
下载	56
2.2.1下载到 RAM/ROM	57
控制器状态	57
2.3.1基本信息	58
2.3.2ZCan 节点信息	59
2.3.3槽位 0 节点信息	59
2.3.4控制器通讯配置	60
固件升级	62
系统时间	64
修改 IP 地址	64
比较控制器	65
锁定/解锁控制器	66
控制器复位	67
项目操作	67
2.10.1编译所有	67
2.10.2增加到项目	67
2.10.3设置	68
2.10.4生成ZAR 文件	69
2.10.5下载ZAR 文件	69
2.10.6注释	70
指示灯	71
第三章	编辑操作	72
常用编辑	73
添加/删除注释	74
书签功能	74
查找/替换	76
第四章	视图窗口	78
轴参数窗口	79
工程/标签/组态窗口	82
4.2.1工程视图	82
4.2.2标签视图	92
4.2.3组态视图	92
命令与输出窗口	93
查找结果窗口	93
帮助窗口	94
任务/监视窗口	94
第五章	工具窗口	96
示波器	97
5.1.1示波器界面	97
5.1.2示波器菜单栏功能	102
5.1.3辅助功能	107
5.1.4帮助	111
5.1.5示波器的使用	112
手动运动	120
输入口	120
输出口	123
寄存器	124
锁存图像	126
AD/DA	128
PWM	128
SDO	129
故障诊断	130
总线状态诊断	132
插件	133
第六章	程序调试	137
启动/停止调试	137
调试工具	140
断点	141
紧急停止	142
第七章	PLC 菜单	144
梯形图快捷工具	145
代码转换	146
7.2.1转换为语句表	146
7.2.2转换为梯形图	146
交叉参数表	147
寄存器使用列表	148
第八章	HMI 菜单	149
元件设置	150
窗口设置	152
8.2.1新建/导入窗口	152
8.2.2背景预设	153
8.2.3显示缩略图/详细信息	154
资源使用	155
8.3.1控件箱	156
8.3.2文本库	159
8.3.3图片库	159
8.3.4按键转换	160
控件排列	161
HMI 编辑	162
8.5.1批量修改地址	162
8.5.2Hmi 设置	164
显示设置	165
8.6.1属性窗口	165
8.6.2快捷图片库	168
语言/状态切换	170
第九章	RTSys 文件类型	173
项目文件	173
程序文件	174
ZAR 文件	174
库文件	174
ZML 文件	174
字库文件等	174
第十章	程序下载运行	175
程序下载（RAM/ROM）	175
程序自动运行	175
ZAR 下载	176
编译Lib	181
控制器程序比较	182
第十一章	右键快捷菜单	183
RTSys 右键	183
Basic 右键	184
Plc 右键	184
Hmi 右键	186
第十二章	RTSys 显示设置	187
状态栏	187
对齐线	188
自动换行	188
主题风格	189
语言切换	190
字体设置	192
窗口自定义设置	193
重置窗口布局	194
第十三章	常见问题	195
附录A：菜单一览表	197
文件选单	197
常用选单	197
控制器选单	199
编辑选单	200
视图选单	201
工具选单	202
调试选单	202
PLC 选单	203
HMI 选单	204
附录B：RTSys 快捷键	206
附录C：EtherCAT 配置操作指引	208
有实际驱动设备	208
无实际驱动设备	215
扫描不到设备操作流程	219


第一章 RTSys 简介

RTSys 是正运动推出的集成运动控制+机器视觉功能的开发软件，支持 RTBasic、RTPlc 梯形图、RTHmi、 C 语言、RTVision 机器视觉等二次开发，并可混合编程、实时仿真、在线跟踪以及诊断与调试，快速实现智能装备的视觉定位、测量、识别、检测和复杂的运动控制等系统的开发，RTSys 软件开发界面如下图所示。用户可通过串口或网口连接 PC 与控制器，使用 RTSys 软件编写的程序可以直接下载到正运动控制器
里脱机运行，也可以在 PC 平台仿真运行。

RTSys 软件支持四种编程方式：Basic、PLC 梯形图、HMI 组态、C 语言。使用 RTSys 进行 Basic 语言编程开发的优势在于：支持多个 Basic 任务运行；若使用 PLC 梯形图开发则支持一个 PLC 任务运行；若使用 HMI 组态编程开发则支持两个 HMI 任务运行。Basic 任务、PLC 梯形图任务和 HMI 组态任务、C 语言任务之间可以多任务运行。
RTSys 软件支持在线仿真调试，自带仿真器ZMC Simulator 和组态程序仿真工具 xplc screen。 在 RTSys 创建的项目不建议在 ZDevelop 中打开！
使用 RTSys 软件HMI 功能需要控制器固件支持才可使用！

 系统要求

要使用 RTSys 软件，硬件要求如下：


项目	最小要求	推荐使用
CPU	Pentium 级别处理器，主频
1GHz	intel i5 级别处理器，主频 2.9GHz
显卡	/	OpenGL 版本不低于 1.5
内存大小	1GB	4GB
硬盘剩余空间	1GB	4GB
操作系统	Windows7	Windows10
显示器	1024x768/24 位真彩色	1920x1080/8 位RGB
通讯接口	RS232 串行口	RS232 串行口/USB/以太网口(可以通过 HUB 转接)


RTSys 安装/卸载/升级

1.2.1RTSys 安装

安装步骤如下：
1.从正运动技术官网获取RTSys 软件安装包，存放至目标路径下（用户自行选择路径）。将安装包解压后，得到如下图所示的.exe 应用程序。
注：下图版本号不一定固定，软件版本号会随软件更新而更新。从正运动技术官网下载最新版的即可。


2.鼠标双击打开该.exe 应用程序。弹出如下窗口，点击“下一步”。

3.点击“浏览”选择软件安装路径。确定安装路径后点击“确定”，并点击“下一步”。

4.点击“安装”即可开始安装软件。右图即正在安装。



5.安装成功则弹出如下窗口，点击“完成”即可。




1.2.2RTSys 卸载步骤

1.打开系统的“控制面板”（可通过快捷键“win+R”→输入“Control”即可打开），选择“程序”中的“卸载程序”。如下图所示。


2.找到RTSys 软件程序，单击右键选择“卸载”，确定卸载即可。注意：
1.卸载软件时需关闭所有已打开的该软件界面。
2.不推荐使用第三方软件卸载！！建议使用系统自带卸载！



1.2.3在线升级

RTSys 软件支持在线升级更新。检查 RTSys 软件是否有更新版本，有以下两种方法：
(一) 有版本更新时，打开 RTSys 软件时会自动弹出更新提示窗口，用户可自行选择是否更新。操作方法如下：
1.需要更新时点击“立即更新”，弹出窗口显示更新内容。
若点击“不再提醒”，则再次打开RTSys 软件时将不再提示当前新版本的更新。


2.点击“下一步”，即可开始更新。更新完毕后弹出安装窗口，按本节上述安装步骤进行操作即可。




(二) 通过“关于 RTSys”可以查看版本更新及正运动技术官网等相关信息。操作方法如下：

1.点击软件界面右上角的“”即可弹出如下窗口，点击“检查更新…”

2.若当前版本已是最新版本则弹窗提示无需更新，如下左图所示。若有新版本可更新，则弹出如下右图的窗口，更新则与上述方法一致。






RTSys 主界面

RTSys 软件主界面如下图所示，默认显示有菜单栏、工程视图（标签视图、组态视图）、轴参数窗口
（帮助窗口、属性窗口）、命令与输出窗口（查找结果窗口）。
菜单栏包括新建项目文件以及连接控制器的基础功能，必备的编辑功能，同时还有方便用户进行调试和实用的工具。菜单详情可参见本手册后续内容。
主界面左侧为文件/标签/组态视图窗口。工程视图可查看当前项目中包含的文件数量、类型和文件的任务号，双击文件可以打开文件内容并进行编辑，在工程视图上点鼠标右键，弹出文件设置窗口。标签视图可显示所有Basic 文件中含有的 SUB 函数列表。组态视图则可显示 HMI 文件中已创建的所有窗口及每个窗口中包含的元件名称。
主界面右侧为轴参数/帮助/属性窗口。轴参数窗口主要显示运动控制中轴的相关参数，主要包括轴
号、轴类型（ATYPE）、脉冲当量（UNITS）、速度（SPEED）等，部分参数支持修改。帮助窗口则用于显示快速指令帮助文档内容，双击程序文件中的指令可在该窗口快速查看。属性窗口则用于显示/设置 HMI 功能的窗口和元件的属性参数。
主界面底部为命令与输出窗口，主要用于查询与输出控制器的各种参数，打印输出信息等，并支持在线命令输入以及输出内容捕获等。查找结果窗口则用于显示通过使用查找功能后，将查找结果所在的文件名、行号和内容显示在该窗口中。





RTSys 操作说明

首先在电脑里新建一个文件夹用来保存即将要建立的工程。打开 RTSys 编程软件，当前说明例程的 RTSys 软件版本为 V1.01.00，更新软件版本请前往正运动官方网站下载，网址：http://www.zmotion.com.cn基本流程：新建项目→新建文件→选择文件类型→添加自动运行任务号→编辑程序→连接控制器→下
载程序到控制器运行。
1.新建项目：菜单栏“文件”→“新建工程”。




点击“新建工程”后弹出“另存为”界面，选择开头已建好的文件夹并打开，输入项目名后保存项目，后缀为“.zpj”。


2.新建文件：菜单栏“文件”→“新建”或 菜单栏“常用”→“新建”。

点击“新建文件”后，出现下图所示的弹窗，支持 Basic/Plc/Hmi/C 混合编程，这里选择新建的文件类型为Basic 后确认（支持修改文件名）。










3.设置文件自动运行：如下左图，右键单击文件，选择“设置任务号”，弹出如下右图窗口，输入新任务号“0”，点击确定即可设置成功。
设置成功后在该文件名右侧显示任务号，如：Basic1.bas[0]。
注：支持设置多个文件的任务号，至少要有一个文件设置任务号，否则无法运行程序！设置了任务号的文件会自动同时运行，任务号数值不分优先级。


 

4.若需要使用“轴配置和EtherCAT 配置”功能，则需要单独开启。通过右键单击 RTSys“工程视图”空白处→选择“工程设置”→勾选“启用轴配置及 EtherCAT 配置”→点击“确定”。


5.编辑程序：程序编写完成，点击保存文件，新建的 Basic 文件会自动保存到项目.zpj 所在的文件下。
6.连接到控制器：在程序输入窗口编辑好程序，点击“常用/控制器”→“连接” →“控制器”。注：若无控制器使用，可使用RTSys 自带的仿真器进行连接运行。点击“连接”→“仿真器”，便可
连接到仿真器，并弹出仿真器连接成功提示。（弹出的仿真器窗口不可关闭，关闭即断开连接）



点击“连接”→“控制器”，弹出“连接到控制器”窗口，可选择串口连接或网口连接，选择匹配的串口参数或网口 IP 地址后，点击连接即可。连接成功则[ 命令与输出] 窗口打印信息： Connected to Controller:VPLC516E Version:4.99-20190219(型号跟版本跟随控制器变化)。

串口连接和网口连接的详细方法参见后续章节连接到控制器。
7.下载程序：点击菜单栏按钮“下载到 RAM”或按钮“下载到 ROM”，下载成功命令和输出窗口会有提示，同时程序下载到控制器并自动运行。
成功下载到 RAM：




成功下载到 ROM：

“RAM 下载”掉电后程序不保存，“ROM 下载”掉电后程序保存。下载到 ROM 的程序下次连接上控制器之后程序会自动按照任务号运行。

注意事项：
1.打开工程项目时，选择打开项目.zpj 文件，若只打开其中的.Bas 文件，程序无法下载到控制器。
2.ZMC0 系列部分型号不支持下载到 RAM。
3.不建立项目的时候，只有.bas 文件无法下载到控制器。
4.自动运行的数字 0 表示任务编号，以任务 0 运行程序，任务编号不具备优先级。
5.必须至少有一个文件设置自动运行任务号，若整个工程项目内的文件都不设置任务编号，下载到控制器时，系统提示如下信息：WARN: no program set autorun.


 离线仿真

RTSys 软件支持在线仿真调试功能，自带仿真器ZMC Simulator 和组态程序仿真插件 xplc screen。
仿真器可在无控制器情况下提前仿真并验证程序。打开仿真器可通过菜单栏“常用”/“控制器”-“连接”，选择“仿真器”即可连接至仿真器。


组态插件 xplc screen 用于显示HMI 组态界面，点击“显示”可离线仿真 HMI 界面。



 命令与输出

在线命令与输出窗口可以查询与输出控制器的各种参数、控制轴运动、打印程序运行结果、打印程序错误信息，打印在程序中由打印输出函数设置的内容（打印输出函数有：?、PRINT、WARN、ERROR、TRACE等命令输出）。TRACE、WARN、ERROR 信息是否输出可通过 ERRSWITCH 指令进行设置。
ERRSWITCH 指令为 TRACE、WARN、ERROR 指令的控制开关，不同的参数值对应不同的输出效果：
指令	ERRSWITCH






语法	ERRSWITCH=switch
switch：调试输出的开关
		值	描述	
		0	TRACE WARN ERROR 指令全部不输出	
		1	只输出 ERROR 指令	
		2	输出WARN ERROR 指令	
		3	TRACE WARN ERROR 指令全部输出	
		4	TRACE WARN ERROR 指令全部输出，以及运动指令监控	
该窗口支持在线命令输入，在“在线命令”输入栏输入对应的指令或相关函数，点击“发送”或按下 “Enter”键即可发送命令并立即执行。注意：在线命令需使用英文符号，中文符号输入无效。
在线命令与输出窗口如下所示，“>>”表示 RTSys 在线命令输入的指令，在线命令输入“print 1+2”窗口会打印计算结果。
连接了控制器或仿真器就可以使用此功能，不受程序运行状态的限制。“清除”按钮用以清空“命令与输出”窗口所有内容。“捕获”开启后弹出“另存为”窗口，将接下来命令与输出窗口输出的所有内容保存到 PC 上并生成一个.txt 文件，捕获开启后，原“捕获”按钮变为“捕获中”，直到再次按下“捕获中”停止捕获信息，打开已生成的.txt 文件可查看捕获到的内容。
ERROR 错误信息默认以红色字体提示，如下图所示：



WARN 警告信息默认以橙色字体提示，如下图所示：



常用的打印查看命令有：
?*SET：打印所有轴参数值与系统参数值



?*TASK：打印任务信息 任务正常时只打印任务状态
任务出错时还会打印出错误任务号，具体错误行





?*MAX：打印所有规格参数（以 XPCIE1032H 为例）


信息参数	描述
max_axis:64	所有轴的最大轴数
max_motor:64	可控的最大电机轴数
max_movebuff:4096	每个轴或者轴组的最大运动缓冲
max_in:16,4096	控制器自带 IN 输入个数，最多支持 IN 输入个数
max_out:16,4096	控制器自带 OUT 输出个数，最多支持 OUT 输出个数
max_ain:0,1000	控制器自带模拟量输入个数，最多支持模拟量输入个数
max_aout:0,1000	控制器自带模拟量输出个数，最多支持模拟量输出个数
max_pwm:4	PWM 输出个数
max_slot:4	总线个数
max_slotecat:4,128	槽位号个数，最大可扩展数量
max_comport:0	串口个数
max_ethport:0	与 PC、API 函数的网口通讯连接


max_ethcustom:0	自定义网口通讯的连接
max_ethiport:1	正运动控制器互联互通的网口通讯连接
max_flashnum:9999	FLASH 块数
max_flashsize:20480	每个 FLASH 空间大小
max_nand:262144KB	NandFlash 存储总共的数量空间
max_nandremain:262144KB	NandFlash 存储剩余可用的数量空间
max_softhwout:4,8,isolate	硬件比较输出口（isolate 表示独立）
max_pswitch:64	软件位置比较输出的最多个数
max_file:55	系统最多支持的文件数
max_3file:8	系统最多支持的三次文件数
max_task:28	任务数
max_timer:1024	定时器个数
max_loopnest:8	内部循环或者选择的次数
max_callstack:16	子程序调用的堆栈层数
max_local of one sub:32	SUB 的局部变量数
max_vr:8000	VR 寄存器空间个数
max_table:320000	TABLE 数组空间个数
max_modbusbit:8000	MODBUS_BIT 位寄存器空间大小
max_modbusreg:8000	MODBUS_REG 字寄存器空间大小
max_var:20480	最多支持变量个数(含全局变量与文件变量)
max_array:8000	最多支持数组个数(含全局数组与文件数组)
max_arrayspace:2560000	所有数组总共的空间大小
max_sub:4096	最多支持 SUB 子程序的个数
max_edgescan:1024	最多可支持的上升沿/下降沿扫描个数
max_lablelength:25	数组与变量等自定义字符的最大长度
max_hmi:2,size:5120kb
(max_hmi:2,x:1920 y:1080)	支持 2 个远端 Hmi,Hmi 显示空间大小为 5120kb
(支持 2 个远端 Hmi,Hmi 最大分辨率 1920🞨1080)
max_zvlatch:4,size:5120kb	视觉图像锁存通道个数
max_zvtask:8	视觉任务数
SERVO_PERIOD:1000 min:250
max:8000	系统周期默认 1000 微秒，最小 250 微秒，最大 8000 微秒
function support:Coder Cam
MultiMove Circ Merge Frame	支持的功能


NcGcode Zvision	
?*FILE：打印程序文件信息



?*SETCOM：打印当前串口的配置信息



?*BASE：打印当前任务的 BASE 列表（140123 以后版本支持）



?*数组名：打印数组的所有元素，数组长度不能太长，数组长度太大只显示部分内容。





?*数组名(n)或?数组名(n):打印数组的单个元素。



?*参数名：打印所有轴的单个参数值（以 SRAMP 指令为例)

?*ETHERCAT：打印 EtherCAT 总线连接设置状态（下图槽位号为 0，连接 1 个设备）




参数信息	描述
Slot 0 contain 2 nodes	0 槽位口共连接了 2 个设备
Lostcount 0-0	丢包数
Node	设备连接 NODE 编号
Status	设备连接状态，参考 NODE_STATUS
Manid	厂商 ID
Productid	设备 ID
Axises	设备总轴数
AL Status	设备OP 状态
Node_profile	设备 Profile 设置
Bindaxis	映射到控制器轴号
Drive_profile	设备收发 PDO 设置
Controlword	控制字
Drive_status	设备当前状态，参考 DRIVE_STATUS
Drive_mode	设备控制模式
Target	电机位置
Encoder	编码器位置


?*RTEX：打印 Rtex 总线连接设置状态




参数信息	描述
Slot 1 contain 1 nodes	1 槽位口共连接了 1 个设备
Lostcount 0-0	丢包数
Node	设备连接 NODE 编号
Status	设备连接状态，参考 NODE_STATUS
Manid	厂商 ID
Devicetype	设备 ID
Axises	设备总轴数
ALState	设备OP 状态
Bindaxis	映射到控制器轴号
Drive_profile	设备收发 PDO 设置
Controlword	控制字
Drive_status	设备当前状态，参考 DRIVE_STATUS
Target	电机位置
Encode	编码器位置


?*FRAME：打印机械手参数，需要 161022 及以上固件支持


?*SLOT：打印出控制器槽位口信息（下图所示为双总线控制器，包含一个 RTEX 口和一个 EtherCAT
口）





?*PORT：打印所有 PORT 通讯口

COM：串口通道 ETH：网口通道 LOCAL：本地接口通道
ECUSTOM：自定义网口通道 CONNECT：控制器互联通道


 报警查看

在 RTSys 软件实现运动控制过程中，若程序或轴状态运行中出现错误时，RTSys 软件提供了以下 3 种报警查看方式：状态栏报警、AXISSTATUS 判断轴状态、报警信息输出。


(一) 状态栏报警
当程序运行出现错误或轴运动异常时，会触发报警信号，在 RTSys 软件界面最底部的状态栏中，以红色闪烁状态予以警示。如下图所示。“状态栏”可在菜单栏“视图”中勾选显示。



























(二) AXISSTATUS 轴状态
在运动过程中，若轴出现运动异常等现象，可通过使用指令 AXISSTATUS 对轴进行状态判断，可在运行程序中添加该指令进行打印监控，或直接在“命令与输出”窗口的“在线命令”栏输入：? AXISSTATUS(轴号)进行查看。打印值为十进制。
另外，也可通过“轴参数窗口”直接查看 AXISSTATUS 参数打印值。显示值为十六进制。



AXISSTATUS 返回值对应如下表所示：

位	说明	打印值
1	随动误差超限告警	2	2h
2	与远程轴通讯出错	4	4h
3	远程驱动器报错	8	8h
4	正向硬限位	16	10h
5	负向硬限位	32	20h
6	找原点中	64	40h
7	HOLD 速度保持信号输入	128	80h
8	随动误差超限出错	256	100h
9	超过正向软限位	512	200h
10	超过负向软限位	1024	400h
11	CANCEL 执行中	2048	800h

12	脉冲频率超过 MAX_SPEED 限制.需要修改降速
或修改 MAX_SPEED	
4096	
1000h
14	机械手指令坐标错误	16384	4000h
18	电源异常	262144	40000h
19	精准输出缓冲溢出	524288	80000h
21	运动中触发特殊运动指令失败	2097152	200000h


22	告警信号输入	4194304	400000h
23	轴进入了暂停状态	8388608	800000h


(三) 报警信息输出
程序运行有误时，在“命令与输出”窗口会自动打印报警信息和错误码以提示，用户可根据报警信息内容进行判断和改正，其中的错误码可根据错误码表进行查找判断。













 紧急停止


紧急停止可立即停止程序和所有轴的运动。
在运动控制调试过程中，为避免发生失控等紧急情况，RTSys 软件中配置了紧急停止功能，该功能按钮可在菜单栏“常用”→“紧急停止”（或“调试”→“紧急停止”）。当在运行过程中按下紧急停止，状态栏则显示当前控制器处于“待机”状态。


 自动备份

RTSys 新增了自动备份的功能，有利于及时替用户保存已编辑的程序文件，当出现异常关闭 RTSys 软件情况时，通过备份功能恢复关闭时的程序文件。（RTSys 默认 10min 自动备份一次）
当 RTSys 异常关闭后重新打开软件，则会出现如下窗口提示，用户可自行选择是否恢复关闭前的文件。注意：RTSys 备份后会在项目目录下生成.tmp 文件，该文件不可删除！






 窗口停靠

RTSys 软件界面中可显示多个窗口/视图，软件界面支持窗口停靠功能。通过该功能将打开的窗口停靠至界面所需位置。通过鼠标按住窗口进行拖动时，界面中会出现停靠指示标，将窗口拖动至对应指示标位置即可停靠。



 编程指引

RTSys 软件支持 3 种编程方式：Basic 语言、PLC 梯形图、HMI 组态。这三种编程方式支持混合编程，同时也支持多任务运行。以下介绍三种编程方式在 RTSys 软件的操作编辑：

1.11.1Basic 编辑

Basic 语言编辑需新建/打开.bas 文件，在该文件下进行编辑操作。basic 编辑操作介绍如下：
(一) 指令输入具有快速提示输入功能。


(二) 提供语法高亮功能。指令、变量参数、注释等分别采用不同颜色显示。
(三)  IF  指令下方的线条为对齐线，可使用菜单栏“视图”-“对齐线”取消。 “切换自动换行”功能可以让程序显示跟随当前程序编辑窗口的大小自动换行。



(四) “添加注释”可快速将程序块变为注释部分，“删除注释”是将注释变为程序。有两种操作方法：
1.在 Basic 程序中选中某段程序，点击菜单栏“编辑”-“添加注释”后，选中的程序就会变成注释；选中某段注释，点击菜单栏“编辑”-“删除注释”后，选中的注释就会恢复为可运行程序内容。
2.在 Basic 选中某段程序/注释，在该行程序单击鼠标右键，弹出快捷窗口，选择“添加注释”/“删除注释”即可。参考下图所示：

(五) 菜单栏“编辑”-“插入一个制表符”/“删除一个制表符”功能用于控制程序缩进量。例：对 FOR
循环语句执行“删除 TAB”操作后的效果如下，单次操作，缩进量是固定的。

(六) “只读模式”用于限制程序文件不被编辑，防止误操作。在菜单栏“编辑”→单击“只读”即可将当前项目工程下的程序文件修改为只读模式，不可再对程序进行编辑操作。若需对程序进行编辑，须先取


消“只读”模式设置，再次单击“只读”按钮即可取消。
(七) 设置/取消书签功能用于给 Basic/PLC 程序添加书签做为标记，方便用户快速定位到某行程序内容。并可在“编辑书签”中查看已使用书签的程序行。Basic 程序中已设置书签的程序行，在行号右侧会显示绿色竖线；PLC 程序中已设置书签的程序行则在行前显示“M”的标识。有两种操作方法：
1.在 Basic/PLC 文件中选中某一行程序，在菜单栏“编辑”→“设置/取消书签”即可对操作行进行设置。
2.在 Basic/PLC 文件中选中某一行程序，单击鼠标右键弹出窗口，选择“设置/取消书签”也可进行设置。

(八) 菜单栏“编辑”-“查找”，在项目中查找(可通过查找范围设置)。

(九) 菜单栏“编辑”-“替换”，在项目中查找并替换(可通过查找范围设置)。




1.11.2梯形图编辑

RTSys 提供 PLC 梯形图与语句表两种编程方式，且梯形图和语句表可相互转换两种模式下均可编辑程序，指令不分大小写。梯形图编程较为直观方便，语句表编程需要对 PLC 指令较为熟悉。PLC 指令可参考 RTPlc 帮助手册。
操作方法：先在项目工程中打开 PLC 文件，通过菜单栏的“PLC”-“转换成语句表”/“转换成梯形图”互相转换。

语句表编程界面如下所示：






(一) 程序编写

1.打开 PLC 文件，在菜单栏“PLC”，点击对应软元件按钮即可快速在程序中插入对应软元件功能输入。





2.在对应的单元格双击或直接输入命令，也可以实现快捷输入。可输入指令或修改参数，点击“确认”保存修改结果，点击“取消”关闭快捷输入框，点击“帮助”打开帮助文档。注：输入字符须为英文状态。


三个点按钮表示打开 PLC 指令输入框，可选择指令和指令操作数，如下图：




3.程序编辑完成在末尾加上 END，否则无法运行。
PLC 中使用 EXE @指令调用 Basic 的命令执行，PLC 和Basic 还有一些共享的变量，详情参见 PLC 编程手册。


(二) 调试

在 PLC 文件中编辑好程序后，在菜单栏“调试”→“启动/停止调试”单击即可进入调试模式，程序为梯形图时使用的软元件会出现红绿色提示，绿色表示导通，红色表示关断。调试模式下，在寄存器上方也会显示寄存器的当前值。
注：PLC 梯形图模式下不支持断点调试，指令表模式下支持断点调试。





(三) 写入值

调试状态下，选中目标窗格，点击右键打开写入值窗口，快速对位变量M 或字节变量 D 的值进行编辑修改。下图示例给 M0 写入数据 ON 或OFF。



写入值窗口如下，仅显示当前窗格选中的寄存器。点击“强制 ON”按钮之后，即可将 M0 状态由 OFF
改为 ON，即为导通，显示为绿色。






(四) 注释

PLC 梯形图添加注释方式有三种，使用 PLC 注释指令、PLC 右键菜单“编辑批注”、菜单栏“视图”
-“注释”窗口。
1.注释指令“//”
使用该指令注释占一行，一般在某个功能模块前添加注释，补充说明该功能的作用等相关信息。



2.编辑批注
此功能在梯形图界面下可对软元件进行注释。
批注方法：选中要批注的软元件，点击右键选择“编辑批注”。



弹出批注编辑窗口之后，在批注下方双击左键，输入注释内容，点击确定。

完成操作成功即可显示批注，若软元件不显示批注，则选中软元件单击鼠标右键，点击“显示批注”即可。不希望显示批注时再次单击“显示批注”即可隐藏。



3.注释窗口
菜单栏“控制器”-“注释”打开注释窗口用于对 PLC 元件进行批注。
其中“系统注释”主要针对 PLC 特殊继电器 M 和特殊寄存器 D，可快速对所有特殊元件进行注释。

“项目注释”自定义输入寄存器名和注释，与编辑批注使用类似，支持将此窗口的项目注释导入导出。




1.11.3HMI 组态编辑

HMI 编程用于在显示屏自定义显示，采用触摸方式执行相关操作。在菜单栏“视图”→“工具箱”中选择组态“元件”可快速选择输入对象，提高编程效率。
元件常用的功能为：调用 Basic 子函数，打开/关闭组态窗口，绑定寄存器，显示文本/图片等，不同的


元件功能有所区别，详细用法参见《RTHmi 编程手册》。


菜单栏“HMI”-“Hmi 设置”，要运行 HMI 文件必须要进行此设置，Hmi 系统设置如右侧窗口所示。



分辨率根据显示屏尺寸设置，常见的 7 寸屏尺寸 800*480。上图例子为 10.1 寸屏，因此分辨率对应设置为 1024*600。
初始化函数为上电后只调用一次的函数，在 Basic 文件中定义，函数的定义必须是全局的(GLOBAL)。周期函数为不断扫描的函数，在 Basic 文件中定义，函数的定义必须是全局的(GLOBAL)。
初始化函数或周期函数不是必须设置的，HMI 调用 Basic 函数的方式还有自定义元件的绘图函数或刷新函数，元件的动作 call sub 等。
打开窗口或元件的属性窗口有两种操作方式：
1.在软件左侧的组态视图中，可查看该 HMI 文件已创建的所有窗口及所有元件，在窗口名处单击鼠标右键菜单“窗口属性”可快速编辑窗口显示情况，在元件名处双击鼠标左键可快速打开元件的属性窗口。
2.打开HMI 文件，在 HMI 窗口界面中单击已添加的元件，在软件右侧即可弹出元件属性窗口，如下左图所示；在 HMI 窗口界面中单击画布空白处，即可打开当前 HMI 窗口的属性窗口；单击画布外的空白地方即可打开 HMI 系统设置窗口。

元件 “属性”功能窗口	背景 “属性”功能窗口


以上对应属性参数详情可参见《RTHmi 编程手册》。新建 HMI 文件运行步骤：
1.在项目中建立 HMI 文件，添加自动运行任务号。
2.打开“HMI 系统设置”窗口，设置屏幕分辨率，起始显示的窗口等。
3.编辑HMI 文件，新建窗口，添加元件，打开对应“属性”设置窗口和元件的功能。
4.新建Basic 文件，编辑 HMI 元件要调用的Basic 子函数内容。
5.在“HMI 系统设置”窗口添加初始化函数和周期函数，在要调用Basic 程序的元件动作处添加子函数名称。
6.连接控制器或仿真器，下载程序运行。无实物屏可连接到 xplc screen 仿真。


HMI 编程案例：绑定寄存器
1.新建HMI 文件，并添加自动运行任务号。
2.在起始 10 号窗口添加位状态切换开关元件。
3.打开元件“属性”窗口选择寄存器类型和编号，“动作”选择“状态恢复”，表示元件动作时给寄存器赋值为 1，松开动作时寄存器值恢复为 0。
4.下载程序到控制器或仿真器，在组态界面按下主界面的元件查看效果。

实现效果：


未按下元件时，MODBUS_BIT(0)=0，按下元件后不松开，MODBUS_BIT(0)=1。当鼠标松开时，寄存器状态又会置 0。

HMI 编程案例：调用 SUB 函数
1.新建HMI 文件，并添加自动运行任务号。
2.在起始 10 号窗口添加位状态显示元件。
3.新建Basic 文件，无需设置自动运行任务号，在 Basic 里编辑好HMI 要调用的全局 SUB 子函数。
4.打开元件“属性”窗口，在“点击调用函数”处选择上一步编辑好的 SUB 子函数名称。
5.下载程序到控制器或仿真器，在组态界面按下主界面的元件查看效果。


实现效果：
当元件被按下时，调用Basic 的 SUB 子函数执行，每按下一次调用一次函数。更多HMI 例程在正运动官网下载。


 使用帮助

帮助文档即正运动为用户提供相关使用及开发文档，包括控制器使用入门文档、RTSys 开发环境使用帮助文档、RTBasic 语法帮助文档、RTPlc 语法帮助文档、RTHmi 语法帮助文档、RTNc 帮助文档、RTVision


视觉语法帮助文档。在文档内可查看所有相关指令的说明，与部分功能的介绍。查看帮助文档有两种方式：
一、菜单栏打开帮助文档。
点击菜单栏最右边→“帮助文档”，根据需求选择对应的帮助文档查看即可。


帮助文档简单使用方法：
1.可根据目录章节分类直接查看所需内容；
2.切换至“索引”项，可在检索栏输入指令等关键字后，按下“Enter”回车键进行查找；
3.切换至“搜索”项，可在搜索栏输入关键字后，按下“Enter”回车键进行全局查找，下方则会显示该关键字所在的章节内容；
注：在帮助界面内，按住 Ctrl，滚动鼠标可对帮助文档显示内容进行放大缩小。HOME 键跳到当前页的页首，END 键跳到当前页的页尾，pageup 和 pagedown 为上下滚动。



二、视图窗口“帮助”中显示
在 Basic 程序中，选中单个指令再按下 F1 键，可快速在“帮助”视图窗口打开帮助文档中的该指令详细说明页面，如下图。在 PLC 和HMI 编程下也支持 F1 快捷键。


7 个帮助文档打开如下图所示
RTBasic 帮助：

RTVision 帮助：



RTPlc 帮助：

RTHMI 帮助：

RTSys 帮助：






第二章 控制器操作

RTSys 支持与正运动控制器进行通讯连接，可通过 EtherNET 网口、RS232/RS485 串口、Local 接口等方式实现通讯。通讯后可在 RTSys 上查看控制器相关信息和配置控制器，如：控制器状态、修改 IP 地址、固件升级等。
操作速览

名称	图示	说明
控制器
连接	
	连接到控制器/仿真器
断开连接	
	断开与控制器/仿真器的连接
下载到 RAM	
	将程序项目下载到控制器/仿真器的 RAM 中，掉电不保存
下载到 ROM	
	将程序项目下载到控制器/仿真器的 FLASH 中，掉电保存

控制器状态	
	查看连接的控制器状态信息，包括控制器基本信息、ZCan 节点状态、
槽位节点状态、通讯配置等
固件升级	
	对控制器现有固件版本进行升级更新
系统时间	
	查看控制器当前时间，支持自定义控制器时间或同步 PC 时钟
修改 IP 地址	
	修改控制器 IP 地址，支持查看控制器当前 IP 地址
比较控制器	
	比较当前 pc 项目的文件跟控制器里面的文件是否一致
锁定控制器	
	对控制器采取密码锁定，锁定后上位机程序无法下载至控制器中
解锁控制器	
	对已锁定的控制器解锁，需输入正确的密码才可解锁
控制器复位	
	重新启动控制器，重启后软件需要重新手动连接上
项目
编译所有	
	对项目中的所有文件进行编译，但不下载进控制器
增加到项目	
	添加文件到当前项目中，支持添加程序文件、字体文件、图片等


项目设置	
	预留

生成ZAR 文件	
	生成专门的 ZAR 加密下载文件，可采用密码加密方式或绑定控制器 ID
的方式，文件后缀为.zar
下载ZAR 文件	
	将 ZAR 加密文件下载到控制器 ROM 中
注释	
	对项目文件中的寄存器做批注，支持查看系统寄存器的作用
指示灯	
	打开/关闭已连接的控制器上的 ALM 灯



 连接

2.1.1连接控制器

通过“控制器”→“连接”→“控制器”菜单，可以连接到控制器。
RTSys 支持串口和以太网口以及 PCI/Local 连接到控制器。

串口参数：串口编号，波特率，是否校验，串口 ID（填 0 即可）。
IP：控制器 IP 地址，连接等待时间。
PCI/Local：PCI 卡号或连接 MotionRT。

1.串口连接

使用该方式连接控制器，需先用串口线连接至上位机。当串口列表下拉选择时，会自动列出本计算机上可用的串口号，选择需要连接的串口编号，并设置好波特率、校验位之后，点击串口行的“连接”按钮，连接是否成功会在软件输出窗口自动打印出相应信息。
若使用 USB 连接会自动生成虚拟串口，选择串口号来连接即可。



若连接失败，按下面方法依次排查：
1.查看串口连接线是否为交叉线。
2.“连接到控制器”里的COM 口编号、参数是否选择正确。
打开电脑“设备管理器”→“端口”→“通信端口（COM）”→“端口设置”，查看 COM 口设置是否正确，控制器串口默认参数 波特率 38400，数据位 8，停止位 1，校验位无。






















在“端口设置”→“高级”选项中可更改 COM 端口号，通过下拉列表选择。

3.当通过串口连接到控制器时，对应的控制器串口必须配置为 MODBUS 从站协议模式（缺省模式），


断电重启即可恢复。
4.COM 口是否已被其他程序占用，如串口调试助手等。
5.PC 端是否有足够的串口硬件。
6.更换串口线/电脑测试。

2.网口连接

IP 地址列表下拉选择时，会自动查找当前局域网可用的控制器 IP 地址。
控制器出厂的缺省 IP 地址为 ************，“连接到控制器”窗口能显示出本机 IP 地址，电脑需要设置 IP 地址与控制器 IP 处于同一网段才能连接，即四段的前三段要相同，最后一段不同才可通讯。
其中 VPLC5 系列控制器包含两个网口，LAN1 网口默认地址 ************，LAN2 网口默认地址
************。
如下图通过“连接到控制器”窗口，可以快速查看本机 IP，请注意设置有线网卡与无线网卡各自的 IP。

同一个网络有多个控制器的时候，若在 IP 下拉列表处未找到控制器 IP 地址的话，可以采取 IP 扫描来查看。

PC 端 IP 地址修改方法：
查看电脑本地 IP 协议版本 4 地址是否为 192.168.0.xxx，前三段与控制器一致，最后一段不能一样，控


制器出厂默认 IP：************。如果 IP 地址的第三段不一样，则需要将对应的子网掩码改为 0。设置好之后再进行软件连接。

























控制器 IP 地址修改方法：
如果控制器 IP 被修改，不处于 192.168.0.XXX 这个网段，此时只能先通过串口连接控制器，获取控制器 IP 地址，然后修改本机 IP 或控制器 IP 使二者处于同一网段。
修改控制器 IP 地址方法有多种，可点击菜单栏“控制器”-“修改 IP 地址”，弹出如下窗口，此时会显示当前控制器网口 1 的 IP 地址（若使用的是网口 2，需切换至网口 2 再进行操作，并且二个网口的网段不能在同一个网段），在窗口直接输入新的 IP 地址即可修改控制器 IP。点击“确定”后等待软件自动断开连接后，重新选择连接到新的控制器 IP 即可。









或在 RTSys 菜单栏“控制器”-“控制器状态”查看或在线命令获取控制器 IP 地址，控制器 IP 用指令
IP_ADDRESS 修改。




修改 IP 后，控制器与 RTSys 的连接会断开，此时再次选择新设置的 IP 地址连接即可。

3.PCI 连接

PCI 连接方式一般用于 RTSys 与正运动 PCI 系列运动控制卡进行通讯，该连接方式用于扫描工控机上已插入的 PCI 运动控制卡卡号，找到对应的卡号进行连接。注：使用 PCI 卡需先根据用户手册安装驱动。


4.LOCAL 连接

LOCAL 连接方式一般用于 RTSys 与 MotionRT 进行实时通讯，常与正运动 XPCI/XPCIE 系列运动控制卡搭配使用。该连接方式需将控制卡插入计算机/工控机卡槽中，根据对应用户手册安装 MotionRT 驱动后，打开 MotionRT 软件进行相应配置后点击启动，在 RTSys 中 Local 栏即可扫描到 MotionRT，选择对应的 MotionRT 即可实现连接。一个 MotionRT 序号对应一个运动控制卡。
连接是否成功会在[命令与输出]窗口打印对应信息。
注：使用运动控制卡与RTSys 连接一般建议优先使用 Local 连接方式。



2.1.2连接仿真器

RTSys 支持离线仿真，在无控制器情况下可以提前仿真并验证程序。
通过菜单栏点击“控制器”→“连接到仿真器”可以自动启动仿真器并连接，或当仿真器启动后可以通过 IP 地址“127.0.0.1”来连接。
成功连接到仿真器，打印如下提示信息：

正运动提供的仿真器除了模拟仿真控制器连接，同时也提供了丰富的仿真操作。支持 IO 仿真及编码器仿真、HMI 仿真等功能。
输入仿真：用于 RTSys 连接仿真器时无外部输入设备时，可在仿真器中操作“输入仿真”，勾选对应编号即可模拟打开 IN 口，数字 0 即对应 IN0,数字 1 即对应 IN1，以此类推。
输出监控：用于监控输出口是否有输出信号。
编码器仿真：用于模拟给编码器输入对应值，使用时需将 ATYPE 设置为带编码器类型值。显示：当程序包含 HMI 工程时，点击“显示”来实现 HMI 界面仿真。
注：当 IO 口数或编号不够使用时，可点击“扩展 IO”，即可扩展至 64 个 IO 口。



某个HMI 示例工程的界面显示如下。

若出现仿真器连接失败问题请参考“常见问题”章节。

2.1.3断开连接



当 RTSys 成功连接至控制器或仿真器后，也可随时通过“断开连接”按钮将控制器/仿真器与 RTSys 连接断开。以下有三种断开连接方式需要注意：
1.RTSys 连接至控制器后，控制器断电会导致控制器与 RTSys 连接中断。一般建议在 RTSys 断开连接后，再给控制器断电。
2.连接至仿真器时，关闭仿真器会使仿真器与 RTSys 连接中断。建议无特殊情况先断开 RTSys 与仿真器的连接，再关闭仿真器。
3.RTSys 已成功连接至控制器/仿真器时，若关闭 RTSys 软件，则使 RTSys 与控制器/仿真器断开连
接。



 下载

下载即将在 RTSys 中编写好的程序下载到控制器/仿真器上运行，若只是对程序进行编译，无下载到控


制器中，则无法正常运行。注意：
1.必须建立“项目”后才能下载。
2.必须先成功连接到控制器/仿真器才可下载；
3.需至少有一个程序文件设置了自动运行任务号，才可正常下载运行。

2.2.1下载到 RAM/ROM




下载到 RAM：程序项目下载到控制器的 RAM 上，下载成功立刻运行，掉电后当前下载的项目会丢失。下载到 ROM：程序项目下载到控制器的 FLASH 上，下载成功立刻运行，掉电后当前下载的项目保持。


程序报错 error 时无法成功下载，优先检查控制器型号是否支持：ZMC0 系列部分型号不支持下载到 RAM。
程序报警告 warn 时无法成功下载，则优先检查工程视图中程序文件是否有设置自动运行任务号。
若文件较大的时候建议先对程序进行编译再下载到控制器，即在菜单栏“控制器”→“编译所有”，采用这种方式下载速度会比直接下载到控制器快很多。小文件下载过程此操作可以省略。


 控制器状态

连接好控制器或仿真器后，通过“控制器”-“控制器状态”查看当前连接的控制器状态信息。
“控制器状态”主要便于用户随时查看控制器相关信息。包括控制器基本信息、ZCan 节点状态、槽位节点状态、通讯配置。节点状态能显示连接的设备的轴数、起始 IO 编号等信息。
基本信息主要显示关于当前连接的控制器配置参数；ZCan 节点则显示 CAN 扩展模块的相关信息；槽位 0 节点则显示 EtherCAT 和 RTEX 总线相关配置信息；通讯配置则显示 CAN 通讯相关配置参数信息。




2.3.1基本信息

信息参数	描述
VirtualAxises	支持最大虚拟轴数
RealAxises	支持最大电机轴数
Taskes	最大任务数
Files/3Files	最大文件/三次文件数
Modbus0x Bits	Modbus 位寄存器用户可用空间大小
Modbus4x Regs	Modbus 字寄存器用户可用空间大小
VR Regs	VR 寄存器用户可用空间大小
TABLE Regs	TABLE 数组用户可用空间大小
RomSize	Rom 容量
FlashSize	Flash 容量
SoftType	软件型号
RTVersion	系统软件版本+固件版本
Build Date	固件版本生成时间
IpAddress	控制器 IP 地址


HardVersion	硬件版本
ControllerID	控制器唯一 ID
ZvlibVersion	视觉库文件版本
Axis features list	轴类型列表

2.3.2ZCan 节点信息


第一行 LOCAL 节点，显示硬件为 VPLC516E 控制器，第一行对应控制器的本地资源，支持 16 轴，数字量输入一共 28 个，编号 IN(0)-IN(27)，数字量输出一共 16 个，编号OP(0)-OP(15)，没有 AD 和 DA。
第二至 N 行显示 ZCan 节点的扩展模块的信息。
例如，CanID 节点 1 的硬件为ZIO1616，数字量输入有 16 个，编号为 IN(32)-IN(47)，数字量输出有 16
个，编号 OP(32)-OP(47)，无 AD 和 DA。



2.3.3槽位 0 节点信息

如果只有EtherCAT 总线，槽位 0 是EtherCAT 总线。如果只有RTEX 总线，槽位 0 是 RTEX 总线。


如果是 EtherCAT+RTEX 双总线，则槽位 0 是 EtherCAT 总线，槽位 1 是 RTEX 总线。

左边是一个最基本的 EtherCAT 初始化程序。
右边显示的第 1 至N 行包含的是 EtherCAT 节点的设备信息。
如上图，EtherCAT 节点 0 的硬件厂家 ID 为 41bh(正运动技术的厂商 ID)，硬件厂家设备 ID 为 1ab0h (正运动技术的产品 ID，对应产品为 EIO24088)，拨码号 0，8 个扩展轴，数字量输入 32 个，编号 IN(96)-IN(127)，数字量输出 16 个，编号OP(96)-OP(111)，无 AD 和 DA。


2.3.4控制器通讯配置

CAN 信息与 RS232/RS485/RS422 串口信息。



通讯设置显示内容如下：
此时 CAN 通讯的设置：CANIO_ADDRESS = 32，CANIO_ENABLE=1
结合CANIO_ADDRESS 与 CANIO_ENABLE 的信息可以知晓，此时控制器是 ZCAN Master 主站模式，CAN 总线通讯速率 500kbps，CAN 处于使能状态。
如需修改 CAN 通讯的设置，修改 CANIO_ADDRESS 与CANIO_ENABLE 相关参数即可。 Port0 为RS232，ModbusSlave 状态，地址 1，VR 与MODBUS 寄存器是两片独立区间。 Port1 为RS485，ModbusSlave 状态，地址 1，VR 与MODBUS 寄存器是两片独立区间。 更多详细内容解释请查看 SETCOM 指令相关的参数说明。

连接上控制器后通过下方状态栏打印信息查看当前连接的控制器型号、IP 地址、运行状态。也可通过 “命令与输出”窗口查看错误、警告、消息的具体内容。


 固件升级

固件升级用于对控制器现有固件版本更新，当前固件版本若无法满足程序运行要求，或部分指令功能不支持，就需要对固件进行升级。
固件升级有两种方法，一种是使用 RTSys 软件自带的固件升级功能，另一种是采用 zfirmdown 工具软件下载 zfm 固件包，进行固件升级。
两种升级操作方法类似，以下为 RTSys 软件固件升级操作指引：
1.先下载好需要升级的固件，将固件文件存放至电脑的任意路径下；
2.打开 RTSys 连接到控制器，连接成功后点击菜单栏“控制器”→“固件升级”后，在弹出的窗口里可查看控制器当前使用的固件版本，可用于对比是否为新固件。若为旧固件，则进行下一步操作：

3.点击“浏览”，按步骤 1 保存的路径选择目标固件后打开，如下图所示。

4.点击“升级”。确认升级之后会提示重启到 ZBIOS 的对话框，此时控制器需要重新连接。点击“确认”后会弹出“连接到控制器”窗口，选择合适的连接方式重新连接（串口或网口）。
注：升级的固件版本需要与控制器硬件型号一致，否则会报错。




5.控制器再次连接成功后，弹出“固件升级”界面，系统进入 ZBIOS 状态，显示型号为“VPLC516E-ZBIOS”。再次点击“升级”。

6.弹出以下界面，表示正在下载文件。下载过程中不可中途关闭。

7.进度条满格后，“固件升级”界面消失，命令与输出窗口显示如下信息，表示固件升级成功。

8.最后重新连接控制器、查看控制器状态的固件版本号。


 系统时间

控制器内置了系统时钟，系统时间用于查看控制器当前时间，用户可根据需求自定义设置时间或选择同步 PC 时钟。

设置自定义时间：用户可自定义修改控制器时间，具体可设置星期、年月日，以及时分秒各个参数。

同步 PC 时钟：同步当前 PC 上位机的时间。

修改 IP 地址

RTSys 支持修改控制器 IP 地址，可通过菜单栏“修改 IP 地址”功能修改，或使用在线命令 IP_ADDRESS
修改。
修改控制器 IP 地址需先使 RTSys 连接上控制器，可通过串口或网口等连接方式实现。


(一) 菜单栏“修改 IP 地址”功能
连接好控制器后，在菜单栏“控制器”→“修改 IP 地址”，弹出如下窗口，此时会显示当前控制器 IP，在窗口可直接输入新的 IP 地址。









修改 IP 后，控制器与 RTSys 的连接会断开，等待响应后再次选择新设置的 IP 地址连接即可。
(二) 在线命令修改控制器 IP
连接好控制器后，在[命令与输出]窗口“在线命令”栏输入指令：IP_ADDRESS = dot.dot.dot.dot，即写入要修改的 IP 地址。点击“发送”后，控制器会断开连接，等待响应后再次选择新设置的 IP 地址连接即可。


 比较控制器

比较控制器主要用于比较当前项目程序与控制器程序是否一致。通过菜单栏“控制器”→“比较控制器”进行操作。比较控制器界面如下图：



第一列显示当前 PC 端打开的项目程序文件，第三列则显示已下载至控制器端的所有程序文件，最后一列则显示比较结果，程序相同显示“YES”，程序不同显示“NO”。
 为程序保密，控制器不支持程序上传。


锁定/解锁控制器
锁定控制器即通过设置密码将控制器锁定，主要用于保护控制器内下载的程序，锁定之后上位机程序将无法下载到控制器，但生成的ZAR 文件仍可下载。解锁控制器则输入正确密码即可解锁。
注意：
1.控制器 LOCK 后将不能进行调试。
2.密码可设置为数字、字母和特殊符号，长度不超过 16 个字符。  密码采用不可逆算法加密，一旦忘记，将无法知晓。
操作路径：菜单栏“控制器”→“锁定控制器”/“解锁控制器”。



 控制器复位

控制器复位即对控制器进行重启。连接控制器后，点击菜单栏“控制器”→“控制器复位”打开如下窗口，点击确定之后控制器会重新上电启动一次，此时需再次连接控制器。











 项目操作

2.10.1编译所有

预留

2.10.2增加到项目


该功能用于将当前路径下或其他路径下的文件添加到当前项目工程中，支持添加的文件包括程序文件
（支持的格式如：.bas/.plc/.hmi）、字体文件（如：.zft/.ttf）、图片文件（如：.bmp/.png/.jpg/.gif）。该功能有两种操作方式：
(一) 菜单栏“增加到项目”功能
操作如下：点击菜单栏“控制器”→“增加到项目”，弹出如下窗口，找到需要添加的文件路径，选择目标文件后单击“打开”。即可将对应文件添加到当前项目中。添加后可在工程视图中查看。




(二) 工程视图中添加文件
在 RTSys 左侧工程视图中单击鼠标右键，弹出菜单，选择“增加到项目”，后续操作方式与上述方法一致。
注：RTSys 一般默认打开[工程视图]窗口，若无该窗口，可在菜单栏“视图”→“工程”打开工程视图。



2.10.3设置


支持查看当前工程路径及版本，并设置当前项目所用编译平台，可用于 C 语言编译，支持自定义 gcc 编译选项。不同控制器编译平台不同，详情可查看《C 语言外部函数接口使用手册》。
断点仅暂停单个任务：增加断点后可选择是否停止单个任务或整个工程。




2.10.4生成 ZAR 文件



把当前项目编译成专门的 ZAR 加密下载文件，可以实现独立的程序下载及保护程序的保密性。项目文件生成 ZAR 文件之后，看不到任何代码，但支持将 ZAR 文件下载到控制器运行。
详细操作可参考第十章—ZAR 下载。

2.10.5下载 ZAR 文件



下载ZAR 文件是将已生成的 ZAR 文件重新下载到控制器中，下载 ZAR 文件需输入正确的密码或使用程序已绑定的控制器才能重新下载。
详细操作可参考第十章—ZAR 下载。


2.10.6注释


该功能主要用于对寄存器进行注释，包括系统寄存器的用途注释，以及对项目寄存器的注释。 系统注释：查看系统已经注释的寄存器的作用，项目注释：自行添加寄存器和对应的批注内容。操作如下：
1.通过菜单栏“控制器”-“注释”可打开如下窗口，系统注释显示系统规定的寄存器用途；

2.切换到项目注释则用户可自定义寄存器及批注，双击空白处即可输入，输入完成后点击“确定”即可保存。


3.若需将自定义注释另存为，可将项目注释中的内容导出，点击“导出”，设置文件名选择存放路径后点击“保存”即可，并生成.ini 格式的文件。
4.若需将自定义注释导入当前项目中，可在项目注释中点击“导入”，找到目标路径中的.ini 格式目标文件，选中并打开文件即可。


 指示灯

支持在 RTSys 直接操作打开或关闭已连接的控制器上的 ALM 灯。该功能主要便于用户在多控制器情况下迅速区分已连接中需操作的控制器。



第三章 编辑操作

RTSys 集程序编辑和程序调试于一体，简单易用。

操作速览

名称	图示	说明
编辑
粘贴	
	将剪贴板中的内容粘贴至项目文件中
剪切	
	从项目文件中剪取选定的程序内容/元件暂时存放至剪贴板中
复制	
	复制项目文件中的选定内容暂时存放至剪贴板中
删除	
	删除项目文件中所选内容
添加注释	
	将项目文件中所选行整行添加为注释
删除注释	
	删除项目文件中所选行的注释
插入制表符	
	对项目文件中光标所在行的首端插入一个制表符
删除制表符	
	对项目文件中光标所在行删除一个制表符
跳转到上一个位置	
	跳转至上次所在位置
跳转到下一个位置	
	跳转至下一个位置
撤销	
	撤销上一次的操作
还原	
	还原上一次的撤销动作
只读	
	打开/关闭只读模式（仅对 basic 和 plc 文件可用）


书签
设置/取消书签	
	对项目文件中所选单行设置/取消书签
上一个书签	
	跳转到同一项目中的上一个书签
下一个书签	
	跳转到同一项目中的下一个书签
编辑书签	
	查看项目中已设置的书签所在文件及行号，支持对书签进行操作
查找/替换
查找	
	对项目文件中指定内容进行查找（查找范围可选）
替换	
	对项目文件中指定内容进行替换（替换范围可选）



 常用编辑

RTSys 为方便用户对程序进行编辑，提供了多种且常用的程序编辑功能，包括复制/粘贴/剪切/删除、插入/删除制表符、跳转位置、撤销和还原操作、只读模式等。以下编辑功能均在菜单栏“编辑”中可使用。

1.剪切/复制/粘贴/删除：用于对 Basic/Plc/Hmi 程序或元件进行编辑。在菜单栏“编辑”中可点击使用，也支持使用鼠标右键/快捷键方式。
2.插入/删除一个制表符：用于对 Basic 程序插入/删除一个制表符，插入/删除几个制表符由插入/删除次数决定，也支持在键盘上按“Tab”键插入。每个制表符的长度已固定，不可修改。
3.跳转到上一个位置/跳转到下一个位置：用于 Basic/Plc/Hmi 程序内的位置跳转，支持书签内跳转/查找结果跳转等。
4.撤销上一次操作/还原上一次的撤销动作：用于对 Basic/Plc/Hmi 程序编辑动作的撤销或还原撤销。
5.只读：用于对 Basic/Plc 文件设置是否启用只读模式。单击“只读”图标即可开启，再次单击图标即可取消。开启只读功能后无法编辑程序。若强制编辑则会弹出提示窗口选择是否解除只读模式
（HMI 不参与只读功能）。


添加/删除注释


将程序内容添加为注释内容或者删除程序的注释内容。将某行/多行程序添加注释后，注释的内容将不再运行，删除注释再次下载即可恢复运行。添加注释后注释内容前生成一个单引号且字体颜色为绿色。如下右图所示。
操作方式：
1.可在菜单栏“编辑”→“添加注释”/“删除注释”打开。
2.选中目标程序行内容，右键“添加注释”/“删除注释”。
3.在需注释内容前，添加一个英文单引号（'）即可注释内容。



 书签功能


书签功能包括设置/取消书签，以及书签编辑，且支持书签间的跳转。


设置/取消书签：用于给 Basic/PLC 程序添加书签做为标记，方便用户快速定位到某行程序内容。Basic程序中已设置书签的程序行，在行号右侧会显示绿色竖线；PLC 程序中已设置书签的程序行则在行前显示 “M”的标识。（目前仅支持单行添加书签，不支持多行同时添加）。
上一个书签/下一个书签：在已设置书签的程序行之间跳转。操作方法：
1.在 Basic/PLC 文件中选中某一行程序，在菜单栏“编辑”→“设置/取消书签”即可对目标行进行设置。
2.在 Basic/PLC 文件中选中某一行程序，单击鼠标右键弹出窗口，选择“设置/取消书签”也可进行设置。

编辑书签：用于查看当前项目中已使用书签的程序行所在位置。操作方法：
1.书签跳转：单击某一行书签可显示该书签的保存路径，点击“编辑代码”即可跳转至目标行处；双击某一行书签可在当前项目工程中直接跳转至目标书签处。
2.书签移除：选中某一个书签，点击“移除”→“确定”，即可取消该行的书签；若需取消全部书签则点击“移除所有”→“确定”，即可取消当前项目中所有书签。






查找/替换


查找：在当前项目/文件中搜索目标内容，便于用户快速定位目标内容所在位置。支持选择查找范围、反向查找、整字匹配、匹配大小写。（同时支持查找/替换 Plc 中的寄存器如：X/Y/M/T/C/S 等，例如将 X0替换为 M10 或将指令 LD M0 替换为 LDI M0。）
打印所有：将当前项目所查找的内容所在位置打印到[查找结果]窗口。可具体打印出目标内容所在的文件名及所在行行号及该行内容。（[查找结果]窗口可在菜单栏“视图”→“查找结果”打开）



替换：将查找的目标内容替换为新内容。便于用户将文件中相同内容快速替换为新内容。RTSys 支持选择替换范围、替换单个目标或全部替换。













替换：对单个已查找的内容进行替换。输入查找内容和替换内容后，单击一次“替换”则从光标处向下查找目标内容（若勾选“反向查找”则向上查找），查找到的目标内容会显示阴影，若首次查找到的内容不需要替换，则点击“查找下一个”进行搜索下一个目标，直到查找到目标内容后，再单击一次“替换”则将目标内容替换为新内容。
全部替换：对全部已查找内容进行替换。单击一次“全部替换”则从光标处向下查找并替换所有目标内容。查找/替换通用功能介绍
查找范围：RTSys 支持 4 种范围，包括当前文件、选中内容、所有文件、当前项目。
反向查找：从光标处向上查找目标内容。RTSys 默认仅从光标处向下查找内容，若需向上查找内容，则需勾选该功能。
整字匹配：查找内容与程序内容完全匹配才可查找到。例：若勾选了该功能，想在当前项目中查找 UNITS指令，打开查找窗口，须输入完整的“UNITS”查找内容才可匹配到，若只输入“UN”“UNIT”等字符则无法匹配。
匹配大小写：是否将查找内容区分大小写进行搜索。勾选了该功能后，查找则仅根据查找内容所输入的字符大写/小写进行查找。例：当查找内容输入：“units”，勾选了该功能后，仅对小写的“units”进行匹配查找，大写则不会被查找。
正则表达式：预留。



第四章 视图窗口


操作速览

名称	图示	说明
窗口
轴参数	
	打开/关闭[轴参数]窗口，可查看窗口监控运动控制中常见的参数。

工程	
	打开/关闭[工程视图]窗口，可查看当前项目中的文件数量、文件类型
以及运行任务号；支持轴配置或 EtherCAT 节点配置等
标签	
	打开/关闭[标签视图]窗口，可查看所有 basic 文件中定义的 SUB 函数

HMI	
	打开/关闭[组态视图]窗口，可查看 HMI 文件中包含的窗口信息及元件
信息

命令与输出	
	打开/关闭[命令与输出]窗口，可查询信息、打印运行结果，在线命令输
入等
查找结果	
	打开/关闭[查找结果]窗口，用于显示查找结果
帮助	
	打开/关闭[帮助]窗口，用于显示帮助文档
任务	
	打开/关闭[任务]窗口，启动调试后显示。可查看各个任务的详细状态
监视	
	打开/关闭[监视]窗口，启动调试后显示。可监视变量、寄存器等的数值
紧急停止	
	立即停止所有任务
显示
语言	
	切换软件显示语言（简体中文/英文），切换后需重启软件生效
字体	
	设置程序文件字体样式、大小等


主题风格	
	切换软件显示风格（共 4 个风格可选）
自定义	
	设置窗口自定义样式（共 4 个窗口可设置）
重置
重置窗口布局	
	恢复软件默认的窗口布局，重启生效



 轴参数窗口

轴参数窗口用于监控运动控制中常见的参数，运动过程中参数的实时变化可在该窗口查看。默认显示在 RTSys 界面右侧，可通过菜单栏“视图”→“轴参数”打开/关闭。下方可变参数可以双击后直接修改，只读参数不支持修改，参数指令含义可查看 RTBasic 帮助。该窗口还支持进行“轴选择”和“参数选择”。
参数释义如下：


参数	描述

ATYPE	轴类型设置，例如值设置为 0 表示虚轴，1 为脉冲输出，3 为正交编码输入，65
为 EtherCAT CSP 模式。
UNITS	脉冲当量，指定每单位发送的脉冲数，支持 5 位小数精度。


ACCEL/DECEL	轴加速度/轴减速度，单位为 units/s/s。当多轴运动时，轴组插补运动的加速度为主轴合成矢量加速度/主轴合成矢量减速度，不设置 DECEL 时，默认等于 ACCEL
的值。

SPEED	轴速度，单位为 units/s。当多轴运动时，作为轴组插补运动时主轴的合成矢量速
度。
CREEP	轴回零时爬行速度，用于原点搜寻，单位为 units/s。


LSPEED	轴起始速度，同时用于停止速度，缺省 0，单位为 units/s。多轴运动时为轴组插补运动的合成矢量起始速度。当追求效率时，此值可设置非 0 值，但不可设置过
大。
MERGE	设置 ON 时，前后缓冲的运动连接到一起而不减速，用于连续插补。
SRAMP	加减速过程 S 曲线设置，单位为 ms。多轴运动时，为轴组合成矢量曲线的时间。
DPOS	轴的期望坐标位置，或称目标位置。单位是 units。
MPOS	轴的测量反馈位置，或称实际位置。单位是 units。
ENDMOVE	轴当前运动的最终目标绝对位置。单位是 units。
FS_LIMIT	轴正向软限位位置，单位是 units。轴运动越界会停止并报错 FSOFT。
RS_LIMIT	轴负向软限位位置，单位是 units。轴运动越界会停止并报错 RSOFT。
DATUM_IN	原点开关对应的输入口编号，-1 无效。

FWD_IN/REV_IN	正向/负向硬件限位开关对应的输入点编号，-1 无效。控制器限位信号生效后，会
立即停止轴，停止减速度为 FAST_DEC。FAST_DEC 一般设置为 10 倍的DECEL。
IDLE	轴当前运动状态判断，运动中返回 0，运动结束返回-1，只读。
LOADED	运动缓冲区没有缓冲的运动指令时返回TURE，否则 FALSE，只读。

MSPEED	轴的测量反馈的实际速度，单位是 units/s。MSPEED 为 MPOS 微分出来的数值，
只读。

MTYPE/NTYPE	当前正在进行运动指令类型 MTYPE/被缓冲的第一条运动指令类型 NTYPE。当
插补联动时，对从轴总是返回合成矢量主轴的运动指令类型，只读。
REMAIN	返回轴当前运动 MTYPE 还未完成的距离，单位是 units，只读。

VECTOR_BUFFERED	返回轴当前当前运动和缓冲运动还未完成的距离，单位是 units。对多轴插补是合
成矢量距离，单位是 units。
VP_SPEED	返回轴当前运动的规划速度，单位为 units/s。当多轴运动时，轴组返回的是插补


	运动的合速度，不是主轴的分速度。非主轴返回的是该轴对应合成矢量速度相应
的分速度，与 MSPEED 效果一致，只读。

AXISSTATUS	查看轴各种位的状态，例如：正反向硬限位，正反向软限位，轴运行状态等，只
读。


MOVE_MARK	运动指令的 MARK 标号，这个标号会和运动指令一起写入运动缓冲。每调用一条运动指令，MOVE_MARK 会自动加一。如果要强制指定 MOVE_MARK，需要每次运动前都设定一次。通过 MOVE_PAUSE 指令可以在 MARK 不同的边界处
暂停。
MOVE_CURMARK	返回当前轴正在运动指令的 MOVE_MARK 标号。
更多参数参见正运动技术《RTBasic 编程手册》中轴参数与轴状态指令。
轴选择：用户自由选择需要监控的轴号显示在窗口。勾选轴号点击“确定”即可。

参数选择：可自定义选择需要监控显示的轴参数，选择监控的参数不宜过多，否则会影响刷新效率。该窗口中有较多参数指令提供选择，并对各个参数指令进行简单描述。部分轴参数只支持只读，部分可
读可写，可根据参数选择中的“只读”列是否勾选进行查看。同时支持对窗口参数自定义排序。
排序的操作方法：打开“参数选择”窗口，选好需要显示的参数，点击“排序”，此时该按钮显示为“排序中…”，在序号列按所需顺序依次对参数进行点击，点击后序号会变为新序号。排序完毕后再次点击“排序中…”结束排序，点击“确定”/“应用”即可生效。（注：排序过程中时，若某个参数排序点错，再次点击该参数无法重新排序，须先结束排序再从头开始）
若要恢复初始显示，则点击“默认”即可。






工程/标签/组态窗口

4.2.1工程视图

一般默认显示在 RTSys 界面左侧，用于显示查看当前项目中包含的文件数量、类型和文件的任务号，以及配置轴参数、EtherCAT 节点、ZCAN 节点等。(在菜单栏“视图”→“工程”即可打开)
轴配置和EtherCAT 配置功能则需要单独开启。（(在菜单栏“控制器”→“设置”→勾选“启用轴配置和 EtherCAT 配置”即可）
  EtherCAT 节点详细配置流程请参考附录C。

1.工程/配置文件

工程文件支持添加 Basic 文件、Plc 文件、Hmi 文件、C 语言文件等，可为程序文件设置自动运行任务号。右键单击“工程文件”→“增加到工程”→选择支持的格式文件添加即可。


配置文件：一般指驱动器的.zml 配置文件。右键单击“配置文件”→“增加到工程”→选择已有的.zml格式文件添加即可。（配置文件无需设置运行任务号）
Startup.bas 文件：控制器的 EtherCAT 总线初始化配置文件。可在“轴配置”或“控制器”中的“应用”自动生成并打开。
注意：添加的工程文件和配置文件的文件名均不能超过 26 个字符。
双击文件可以打开文件内容并进行编辑，在工程视图上单击鼠标右键，弹出文件设置窗口。窗口内容如下：
设置任务号：给选中文件设置自动运行任务号。
增加到工程：添加文件到当前项目中。详细介绍参考项目操作章节。
增加到配置文件：添加.xml 文件/.zml 文件到项目工程中，添加.xml 文件可自动转为.zml 文件。
新建筛选器：新建文件组便于对各种文件进行分类。
工程设置：设置工程的编译平台等。
生成ZAR 文件：生成 ZAR 加密文件。详细介绍可参考 ZAR 下载章节。
删除文件：将选中文件从当前项目中移除，但不会删除项目工程路径下的文件。
重命名文件：对选中文件重命名。项目路径中的文件同步被修改。（重命名前需要先关闭该文件）
Startup.bas 设置：自定义添加固定配置内容至 Startup.bas 文件或修改 Startup.bas 文件中的部分参数数据。如下图，在设置窗口左边添加的相关 basic 程序代码，在生成 Startup.bas 文件时会自动插入到对应程序行中。在窗口右边可修改 Startup.bas 文件中相关参数。
注意：
1.已应用生成 Startup.bas 文件后通过该窗口进行设置修改，点击“立即更新”即可同步更新。
2.Startup.bas 文件的生成可在“轴配置”、“控制器”、“Drive n”、“ZCan 节点-节点 n”等界面右下角的“应用”按钮中生成。




Startup.bas 恢复：记录每次修改保存后的 Startup.bas 文件，支持自行选择恢复某个时间的 Startup.bas 文件。


2.轴配置

即控制器的轴列表，根据型号不同显示已连接的控制器总轴数（实轴+虚拟轴）。可在该处直接对各轴进行相关功能和参数的配置。在工程视图中轴配置列表可查看已配置轴的类型（local 为本地脉冲轴， EtherCAT 为总线轴）


(1).轴配置总界面
读取显示各轴的基本信息：轴注释、轴来源（各轴的类型配置）、轴使能状态等；支持将轴配置内容一键应用生成 Startup.bas 配置文件，并支持导出或导入其他轴配置文件。
















(2).单轴配置界面
对具体各个单轴进行轴配置，支持直接设置单轴的硬件限位/原点开关、轴使能及输入/输出反转等。配置轴基本参数、回零、简单手动运动等。
各模块操作方法：
1)常用输入配置：根据实际情况修改各输入开关的序号即可生效（即 IN 口编号），设为-1 则为取消配置。配置了开关后即可选择是否开启反转输入。同时支持自动配置，在“控制器”→“输入 IO 映射关系”修改 IO 编号后，此处的序号将按起始输入编号自动配置，但需满足以下 3 个条件才可生效：
A.需要轴对应上；
B.数量不能为 0；
C.IN 编号为-1，或为原来起始编号开始，逐渐+1；
2)常用输出配置：根据实际情况修改输出口序号即可生效并选择是否开启输出口，设为-1 则为取消配置。
3)轴状态读取：根据实际轴运动情况实时获取轴状态和轴运动位置数据。其中，轴注释和轴停止原因支持写入，其余为只读。
4)运动：支持设置单轴的基本参数及轴运动方式。该处的“停止”只停止单轴。（注意：该处设置的轴参数需要选择“运动”或点击“同步轴参数”才会生效替换原本的参数。）
5)回零：设置回零方式及相关回零参数，并启停回零运动。（回零方式根据轴类型不同有不同方式选择）
6)其他：进行同步运动时读取叠加主轴号，支持取消同步运动。




3.控制器

支持配置控制器的相关参数，包括：轴映射、数字量 IO 映射、模拟量 AD/DA 映射，修改控制器总线周期等。并支持一键自动配置轴/IO/模拟量映射编号和类型，同时支持将已配置的数据导出保存或导入其他已配置数据。
（单击右键“控制器”可更新 xml/zml 列表：即当有新的 xml/zml 文件添加至 RTSys 软件目录中的 “EtherCAT”文件夹时，需在此处进行更新，软件才可扫描到文件。）
轴映射关系：可手动设置控制器的轴数分配并映射轴号和配置轴类型。可根据控制器支持的总轴数进行脉冲轴和EtherCAT 总线、CAN 扩展轴的数量分配。分配好轴数后点击“自动配置”可一键完成各轴的映射和轴类型设置（脉冲轴默认 4 模式，EtherCAT 总线轴默认 65 模式，可手动修改）。



输入/输出 IO 映射关系：读取已连接的控制器、CAN 扩展、EtherCAT 扩展模块等设备节点，并显示输入映射的起始编号和各设备的 IN 口数量。支持自动配置并将配置参数应用到“Startup.bas”文件。
















模拟量输入/输出映射关系：读取已连接的控制器、CAN 扩展模块等设备节点，并显示输入映射的起始编号和各设备的 IN 口数量。支持自动配置并将配置参数应用到“Startup.bas”文件。



(1).EtherCAT 节点/EtherCAT-0
显示控制器每个槽位号上已连接的 EtherCAT 节点设备，并自动读取已连接扫描到的 EtherCAT 设备相关参数信息。

EtherCAT 节点/EtherCAT-0 右键菜单介绍：
扫描设备：扫描已连接至控制器的 EtherCAT 节点设备。需手动点击“扫描设备”才显示节点设备。
添加设备：手动添加相关 EtherCAT 设备。可在未有实际设备接入时手动添加设备提前配置。
删除所有设备：删除 EtherCAT 节点上所有设备。
总线启动：启动槽位号上所有EtherCAT 设备总线。
总线停止：停止槽位号上所有EtherCAT 设备总线。单个EtherCAT 设备右键菜单介绍：


插入设备：手动插入其他 EtherCAT 设备。（插入的顺序是当前点击设备的上方）
删除设备：删除选中设备。
重命名：对选中的EtherCAT 设备重命名。
拷贝配置到设备：将选中设备的配置拷贝到其他节点设备应用。

1)单个节点设备（Drive n）
显示选中 EtherCAT 设备的基本信息及连接状态、变量等，支持自定义配置 PDO、启动参数等。

“启动参数”支持修改/新增 CoE 协议中的相关数据字典参数。（RTSys 工程视图→双击"Drive n"→"启动参数"→"新增"/"修改"）




2)EtherCAT 节点基础使用流程：
注意：如果软件能扫描到驱动，但不显示具体的驱动名称，一般是缺少该设备的 xml 文件。

(2).ZCan 节点


读取显示已扫描到的 ZCan 节点设备，双击 ZCan 节点可以显示所有 ZCan 设备的基本信息。

1)节点 n
根据 ZCan 的拨码组合值作为节点编号，双击可查看 ZCan 节点设备的基本信息和 IO、模拟量、PWM
等的状态。


2)ZCan 节点使用流程：将控制器与 EtherCAT 节点设备正确接线后，RTSys 连接控制器后点击“扫描设备”即可读取设备及信息。


4.2.2标签视图

一般默认显示在 RTSys 界面左侧（与工程视图合并为一个窗口）,用于显示查看所有 Basic 文件中含有的 SUB 函数列表。鼠标双击对应的 SUB，可以快速跳转到程序对应的定义文件和行号。（可在菜单栏“视图”→“标签”打开）


4.2.3组态视图

一般默认显示在 RTSys 界面左侧（与工程视图合并为一个窗口），用于显示查看Hmi 文件中含有的所有窗口及每个窗口中包含的元件。（可在菜单栏“视图”→“HMI”打开）
在该视图中，6,7,8 号窗口一般为默认内置的键盘窗口。初始窗口一般默认为 10 号窗口，可在该窗口添加元件等。鼠标双击窗口可显示/隐藏该窗口下的所有元件（或点击窗口号前的“+/-”号），鼠标双击对应的元件可弹出元件“属性”编辑窗口。将鼠标放置窗口名处，鼠标右键单击，可弹出菜单窗口如下右图所示。
窗口属性：打开选中窗口的“属性”编辑窗口。删除窗口：删除选中窗口及其包含的所有元件。
拷贝窗口：拷贝选中窗口并新建窗口将其内容应用到新窗口中。


	

 命令与输出窗口

命令与输出窗口用于查询与输出控制器的各种参数、控制轴运动、打印程序运行结果、打印程序错误信息，打印在程序中由打印输出函数设置的内容。
详细介绍可参考命令与输出章节。



 查找结果窗口

查找结果窗口用于显示查找目标内容后打印的信息。与查找功能配合使用。查找方法：菜单栏“编辑”→“查找”打开如下窗口。

搜索到的结果如下图所示，显示匹配结果所在的文件名、行号和内容，在该窗口点击对应行能定位到程序位置。




 帮助窗口

用于显示帮助文档内容的便捷窗口。通过菜单栏“视图”→“帮助”打开窗口后，在 Basic/Plc 程序中，双击选中某个指令，帮助窗口即可显示对应指令介绍。若双击指令帮助窗口未切换，则在选中指令后按下 F1键，即可切换到帮助文档。




















任务/监视窗口
任务/监视窗口用于查看任务的运行情况和增加监视项目。该窗口属于程序调试的部分，在调试时打开这两个窗口可进行监视。
注意：只有在调试状态下才能打开任务与监控窗口。
操作方法：RTSys 连接好控制器/仿真器后，在菜单栏“调试”→“启动/停止调试”，启动后再在菜单栏“视图”→“任务”/“监视”窗口。（一般默认启动调试后自动打开监视/任务窗口）




监视窗口：用于监视当前文件中全局变量/文件变量/结构体/寄存器等有效表达式的数值变化，程序运行时自动获取参数值显示出来。监视的内容需自行输入，双击空白格即可输入/删除。也可以在调试状态下，在程序编辑区域选择变量后点击右键“增加到监视”加入到监视内容，通过双击监视内容名称来修改或增加监视项。（不支持 LOCAL 局部变量的监视，需要通过任务窗口查看）
全局变量支持双击手动修改值。若监视内容的值显示“Online command fail of error……”，则表示该内容不是全局变量且不在当前监视的文件中。

任务：用于查看当前项目中各个任务的详细状态，根据任务号可查看该任务处于运行/停止状态，运行则显示“Running”，停止则显示“Stopped”，同时显示运行中的文件名及对应运行到的行号。上图示例为多个任务，控制器支持多任务运行，最大支持任务数在“控制器状态”窗口查看Taskes 参数。
栈：当程序调用 SUB 过程时，会自动将原来的状态和局部变量存储起来，称之为栈。局部变量：监测当前项目文件中 LOCAL 变量定义，以及 SUB 调用传入的参数。


同一任务的不同栈的局部变量是不一样的，就算名称一样。

栈层次有限，子程序调用的堆栈层数一般为 8 层，请留意递归的使用。



第五章 工具窗口


操作速览

名称	图示	说明
示波器	
	监测/调试运行中的程序，将程序中参数数据变化转化为图形显示
手动运动	
	设置轴参数对电机直接进行手动操作
输入口	
	实时监测输入口状态
输出口	
	实时监测输出口状态
寄存器	
	实时监测各类寄存器值的变化
锁存图像	
	用于显示查看锁存通道内的图像或更换锁存通道图像
AD/DA	
	监测 AD/DA 数值变化
PWM	
	设置/读取 PWM 的占空比和频率值
SDO	
	监测 AD/DA 数值变化
故障诊断	
	监测控制器状态及显示故障诊断信息
总线状态诊断	
	对 ETHERCAT、RTEX 总线状态进行诊断并显示诊断信息
插件	
	添加自定义小插件，默认已有“xplc screen” HMI 仿真插件


 示波器

5.1.1示波器界面

示波器属于程序调试与运行中极其重要的一个部分，用于把肉眼看不到的信号转换成图形，便于研究各种信号的变化过程。示波器利用控制器内部处理的数据，把数据显示成图形，利用示波器可以显示各种不同的信号，如轴参数、轴状态等。在“工具”→“示波器”中，打开示波器窗口。
操作方法：在 RTSys 编写好程序后，成功连接到控制器/仿真器后，打开示波器，设置好所需要的数据源及对应编号，选择自动触发/手动触发，点击“”启动按钮，再将程序下载至 RAM/ROM，即可采样。按以上操作选择自动触发时点击启动按钮后立即触发。选择手动触发时，需在点击“”后，点击“手动触发”按钮后下载至RAM/ROM，或点击“”后直接下载至RAM/ROM 后等待Basic 程序触发，才可成功采样（注：等待Basic 程序触发时，程序中需加入“TRIGGER”指令）。

1.示波器主界面与通道号界面

按钮功能如下：

按钮	功能
通道	选择的通道及叠加通道，对比通道不显示。
配置	选择进行示波器参数配置、观察器配置、数据源设计及配置的导入导出。


辅助功能	辅助观察波形，包括搜索波形、对比波形及导入导出波形。
帮助	显示鼠标操作指南界面，提示各个模式下鼠标的快捷操作内容。

	示波器启动开关。启动状态下显示为，但不触发示波器采样。


触发模式	下拉中可选择自动触发或手动触发。选择自动触发时手动触发按钮不可用。自动触发在用户点击启动按钮后立即触发；选择手动触发时，需在点击“”后，点击“手动触发”按钮后下载至 RAM/ROM，或点击“”后直接下载至RAM/ROM 后等待 Basic 程序触发，
才可成功采样（注：等待 Basic 程序触发时，程序中需加入“TRIGGER”指令）。
手动触发	手动触发示波器采样按钮。
<<	按下隐藏通道名称和峰值，只显示通道编号。


水平刻度	横轴一格的刻度。下拉菜单选择，可手动输入数值和单位，默认输入单位为 ms，输入完
成后自动转化为 s。将鼠标放置数值框，滚动鼠标可以缩放水平刻度。YT 模式有效，XYZ模式和 XYZD 模式下变为灵敏度，表示鼠标左键操作的灵敏度。


显示模式	有四种模式可切换，包括YT 模式、XY 模式、XYZ 模式和 XYZD 模式。通道数设置小于 2 时不可用 XY/XYZ/XYZD 模式；小于 3 时不可用 XYZ/XYZD 模式；小于 4 时不可
用 XYZD 模式。
YT 模式	不同数据源随时间变化的曲线，每一个通道显示一条波形。
XY 模式	XY 平面显示两个轴的插补合成轨迹，连续两个同类型通道为一组显示一条波形。


XYZ 模式	XYZ 三维空间显示合成轨迹。依次选择通道作为 X、Y、Z 轴，三个同类型通道为一组显示一条波形(通道类型包括：常规通道、叠加通道、对比常规通道和对比叠加通道)，每种类型最多显示一条波形。
注：使用本模式时显卡的 OpenGL 版本须为 1.5 及以上。




XYZD 模式	XYZD 四通道可视化显示轨迹，其中 XYZ 为三维空间合成轨迹显示，D 为以圆点形式显示的数据源。计算方式为：圆点直径大小=当前 D 数值÷D 基准数值×D 基准大小。参数修改位于“观察器配置”窗口。依次选择通道作为 X、Y、Z 轴和 D 数值取值通道，四个同类型通道为一组显示一条波形(通道类型包括：常规通道、叠加通道、对比常规通道和对比叠加通道)，每种类型最多显示一条波形。
当前D 数值：当前位置的数据源值的大小。
注：使用本模式时显卡的 OpenGL 版本须为 1.5 及以上。

通道数	设置要采样的常规通道总数，启动状态下不可修改。当设置通道数大于控制器支持通道
数时，会弹出提示信息：超过控制器支持的最大通道数。
3D 视角	可选斜视角、正视角、左视角和俯视角，默认斜视角。XYZ 模式和 XYZD 模式有效。


连续采集	不开启连续采集时，到达采样最大采集周期数后便停止采样；开启连续采集之后示波器会持续采样，到达采样最大采集周期数后仍继续采样，忽略设置的采样最大采集周期数，
直到按下停止按钮后才会停止采样，采集的数据自动重新覆盖之前的数据。连续采集的


	所有波形采样数据均能导出。（使用串口时自动取消连续采集功能）
跟随	开启跟随后，横轴自动移动到实时采样处，跟随波形显示。

放大镜	勾选放大镜，鼠标移动到显示区域后自动在鼠标的右下方显示放大视图，放大视图跟随
鼠标移动及刷新。放大镜参数修改位于“观察器配置”窗口。YT 模式有效。

显示	选择当前通道曲线是否显示。示波器有四种类型的通道，包含：常规通道 1~8，叠加通
道 1~4，对比波形的常规通道 1~8 和对比波形的叠加通道 1~4。


编号	选择需要采集的数据源编号，如：轴号、数字量 IO 编号，模拟量 IO 编号、TABLE 编号、VR 编号、MODBUS 编号等。编号设置范围为 0 到控制器的最大轴数，可手动输入
编号。

数据源	选择采集的数据类型。点击鼠标左键可手动输入数据类型，或点击下拉菜单选择类型
参数。可以在“数据源设计”窗口设置需要的参数类型。
偏移	波形纵轴偏移量设置，下拉菜单选择偏移量，可手动输入。

垂直刻度	纵轴一格的刻度。选择 auto 时表示自动刻度，示波器停止时可用，刻度值根据当前采集
到的波形运动幅度自动变化，使波形能够完整的显示在当前示波器界面上。


	提示此处可能会出现丢点现象，与最大采集周期数有关。示波器开启连续采集后，在最大采集周期的 80%处会重新触发采集，此时 TABLE 数据开始重新覆写，在此过程中可能会出现丢点现象。手动触发模式下使用“TRIGGER”指令有效，在最大采集周期数的
80%处左右出现。
注意：若要设置示波器参数，如轴编号、数据源以及示波器“参数配置”窗口，要先停止示波器再设置。

2.示波器游标界面

游标工具主要是用于标定测量示波器图形数据或者是坐标距离。点击“游标”按钮，显示如下图所示的游标界面。



按钮功能如下：

按钮	功能

显示X	选择是否启用游标 X。X 游标数量为 2 个，分别为 X1、X2。首次显示时游标在默认位置，再次启用显示为上次启动时的位置。用户可拖动(鼠标左键按下后移动)改变游
标的位置，游标位置不可超过观察器的范围。YT 模式及 XY 模式生效。

显示Y	选择是否启用游标 Y。Y 游标数量为 2 个，分别为 Y1、Y2。YT 模式及 XY 模式生
效。

附着	勾选时在游标上显示若干小圆，小圆随游标所在位置附着到当前波形上，便于寻找极
值。YT 模式的 X1/X2 生效。

默认	点击按钮，游标恢复到默认位置。X1/Y1  的默认位置为当前波形显示区域的 1/3
处;X2/Y2 的默认位置为当前波形显示区域的 2/3 处。
通道号	用户选择的通道。用户可在“Time(ms)”行的 X1(Y1)及 X2(Y2)修改游标位置。
X1(Y1)	游标X1(Y1)在该通道的位置。
X2(Y2)	游标X2(Y2)在该通道的位置。
X2-X1(Y2-Y1)	该通道上游标位置之间的差值。

3.示波器统计界面
统计功能可在指定时间范围内，自动统计该范围中的 Y 轴数据的最大值、最小值、波动值、平均值、标

准差等。点击“统计”按钮，显示如下图所示的统计界面。统计参数可在菜单栏“配置”中的“参数配置”设置。YT 模式下有效。
按钮功能如下：

按钮	功能
范围	可输入统计的水平刻度的范围，输入后自动更新统计的内容。

游标范围	选择是否使用游标的范围，勾选后不可手动输入范围，将自动统计游标 X1 及 X2 的
范围。使用“游标范围”前须勾选“显示 X”。
通道号	选择显示的通道。
最大值	在指定时间范围内通道在 Y 方向的最大值。
位置(ms)	最大值所在的位置。
最小值	在指定时间范围内通道在 Y 方向的最小值。
位置(ms)	最小值所在的位置。
波动值	最大值与最小值的差值。
平均值	在指定时间范围内通道在 Y 方向的平均值。
标准差	在指定时间范围内通道在 Y 方向的标准差。


5.1.2示波器菜单栏功能

1.示波器通道

点击示波器上方菜单栏“通道”按钮，显示如下图所示界面。

按钮功能如下：

按钮	功能
通道	根据所选的通道数及叠加数显示，对比通道不显示。



加载	加载时会判断加载波形与当前波形点数是否一致，加载成功后会覆盖对应通道的波形。各个通道的点数允许不一致，当加载的通道点数大于当前显示的点数时，则截断波形显示；当加载的通道点数小于当前显示的点数时，则自动补全波形显示，自动补全是以最后一个点的位置水平补全点数。（例如通道 1 的波形在 50s 结束，通道 2 的
波形在 60s 结束）。加载文件格式为.txt。
保存	导出指定通道的波形及每个点间隔的时间。格式为.txt。

2.示波器配置

(一) 参数配置窗口

点击示波器上方菜单栏“配置”按钮，点击“参数配置”，弹出如下所示“参数配置”窗口。



参数定义如下：

参数	描述
基本参数
采样周期(us)	示波器两次采样之间的时间间隔。不可修改。


间隔周期数	采样时间间隔，单位为系统周期，与控制器固件版本有关，一般默认 1ms，指令 SERVO_PERIOD 查看。（例：间隔周期数设置为 1，则表示 1 个周期采样一次；间隔周期数设置为 5，则表示 5 个周期采样一次；周期时间取决于控制器固件版本）一般
来说，间隔周期数越小，采样数据越准确，单位时间内数据量越大。

最大采集周期数	总共采样的数据次数，数值越大采样范围越大。（即：一个通道采集的数据所需使用
的 TABLE 大小）
自动使用 TABLE
数组末尾	
抓取数据存放的位置，默认为 True。






TABLE 位置	设置抓取数据存放的位置。一般默认自动使用 TABLE 数据末尾空间，当“自动使用 TABLE 数组末尾”设置为 False 时可以自定义设置，但是设置时注意不要与程序使用的 TABLE 数据区域重合。
查看控制器 TABLE 空间大小有以下三种方法：
a.使用 TSIZE 指令读取；
b.在“控制器状态”窗口查看； c.在线命令?*max 打印查看。

导出参数	需要导出示波器通道参数信息时选择，勾选后在导出波形时导出示波器参数，包括：
基本参数、叠加参数和通道配置参数(编号、数据源、偏移、垂直刻度)。默认 True。
叠加参数
通道数	选择叠加的通道数量，下拉菜单选择。叠加通道最多支持 4 个。
叠加的第一/二个
通道	
用户选择进行叠加的通道号。


叠加方式	设置两条通道之间的叠加方式，下拉菜单选择相加或相减两种叠加方式。通道叠加后
会生成新的叠加波形。（例：在通道号界面显示 CH:1-2 表示第一个通道和第二个通道以相减的形式进行叠加）
统计参数
统计参数	设置示波器统计页面显示的参数信息，默认为 True。

(二) 观察器配置窗口

点击示波器上方菜单栏“配置”按钮，点击“观察器配置”，弹出如下所示“观察器配置”窗口，配置完成后，点击“应用”按钮可预览修改后的效果，最后点击“确定”修改成功。



参数定义如下：

参数	作用
背景颜色/网格颜色/
游标颜色/坐标颜色	
设置对应的颜色。
网格线类型	设置波形显示界面网格线类型，可选择实线或虚线。
游标线类型	设置游标线显示类型，可选择实线或虚线。


通道线类型	三种形式可选，包含点、实线、虚线。点显示时示波器按固定周期采集得出一系列采样点的数据，通道可设置参数：点大小；实线和虚线显示时将采样点连成平滑的
线段显示，线段更容易发现异常点的数据显示，通道可设置参数：线宽。
线条质量	设置通道波形的线条质量，数据量较大时建议设置标准模式可以加速示波器性能。
字体/字体大小	设置波形显示界面上通道编号、通道名称和峰值的字体和字体大小。
常规通道/叠加通道/
对比通道/对比叠加通道	

设置对应通道的线宽、点大小和通道颜色。



D 基准数值/ D 基准大小	用于计算XYZD 模式下圆点直径大小，直径大小与 D 基准大小/D 基准数值的比值
有关，比值越大，圆点直径越大。计算公式为：圆点直径大小=当前 D 数值÷D 基准数值×D 基准大小。(当前 D 数值为“D 每组取值”的数值)

D 每组点数	每 N 个采集点显示一个圆点。(例如“D 每组点数”设置为 100，即在每 100 个采
集点依照“D 每组取值”的取值方式显示一个圆点)

D 每组取值	当前显示圆点尺寸大小在 N 个采集点中的取值方式，可选最大值、最小值和平均值。(例如“D 每组取值”设置为最大值，“D 每组点数”设置为 100，则将每 100
的采集点中的最大值作为计算当前显示圆点直径的大小的依据)
放大镜	设置放大镜放大界面的宽、高和放大倍数。
搜索	设置搜索波形时搜索结果显示的线宽、点大小和通道颜色。

(三) 数据源设计窗口

点击示波器上方菜单栏“配置”按钮，点击“数据源设计”，弹出如下所示“数据源设计”窗口。



按钮功能如下：

按钮	功能

第一/二级菜单栏	数据源选择的菜单栏。当第二级菜单有内容时，则一级菜单的文本为类别，二级菜
单的文本为数据源；当二级菜单无内容时，则一级菜单的文本为数据源。

	新增按钮，在一级菜单或二级菜单新增一项内容。

	删除按钮，删除选中一级菜单或二级菜单的指定项。第一级菜单栏的轴参数和寄存
器默认不可修改。

	上移/下移按钮，选中菜单栏的一项，用于排序。
重新整理	一级菜单和二级菜单的项按照字符(A~Z)顺序进行排序。

(四) 导入配置/导出配置

导入配置：导入示波器相关配置，包括：参数配置、观察器配置、数据源设计、通道参数配置（显示、编号、数据源、偏移、垂直刻度）。导入数据的文件格式为.ini。
导入配置方法：点击菜单栏“配置”中的“导入配置”，选择导入的配置文件打开即可。导入文件的参数将会覆盖当前示波器的参数。
导出配置：导出当前示波器的相关配置，包括：参数配置、观察期配置、数据源配置、通道参数配置（显示、编号、数据源、偏移、垂直刻度）。导出数据的文件格式为.ini。
导出配置方法：点击菜单栏“配置”中的“导出配置”，选择文件夹保存配置数据。

5.1.3辅助功能

1.搜索波形

点击示波器上方菜单栏“辅助功能”按钮，点击“搜索波形”，弹出如下所示“搜索”窗口。YT 模式可用。

按钮功能如下：


按钮	功能
通道	选择要进行搜索的通道。YT 模式可用。

搜索范围	输入需要搜索波形在 Y 方向上幅值的范围。输入数值后，点击“查找下一个(F)”
按钮，将在当前范围内进行搜索。
从起点/终点开始搜索	选择搜索顺序。

2.对比波形

示波器支持多通道波形与现波形对比。点击示波器上方菜单栏“辅助功能”按钮，点击“对比波形”，弹出如下所示“对比波形”窗口。示波器在停止状态下才能对比波形。

对比文件：导入需要对比的波形数据文件。加载对比文件时会判断对比波形与当前波形点数是否一致，各个通道的点数允许不一致。（当对比波形的点数大于原始波形的点数时，会截断波形并显示；当对比波形的点数小于原始波形的点数时，会自动补全波形并显示，自动补全是以最后一个点的位置水平补全点数。）加载完毕后会更新对比通道的参数(是否显示、编号、数据源、偏移和垂直刻度)。对比文件的格式为.txt。
有效范围：对比文件内波形的点数范围。导入对比文件后系统会自动获取有效范围，有效范围的数值始终为正值，有效范围的起始值默认为 0；终止值为对比波形的最大点数，但不可大于原始波形的最大点数。
偏移：设置波形偏移的时间位置。当设置偏移后，波形会移动对应的偏移范围。此时，有效范围会对应增加/减少偏移大小的点数。（设置为正值时往右偏移，设置为负值时往左偏移）
例如：
(1)若对比波形最大点数为 5000，原始波形最大点数为 6000，则有效范围为：0-5000。对比波形点数小于原始波形点数，对比波形自动补全点数并显示。
当对比波形设置偏移为 200 时，有效范围会变化为：200-5200。
当对比波形设置偏移为 1200 时，有效范围会变化为：1200-6000。(原因：有效范围的终止值最大为原始波形的最大点数。偏移 1200 后，对比波形的最大点数为 6200，大于原始波形的最大点数 6000，对比波形会被截断。)
当设置偏移为-200 时，有效范围会变化为：0-4800。(原因：有效范围的数值始终为正值，最小为 0。偏移-200 后，波形左移 200ms，则波形可能显示不全。此时对比波形的最大点数为 4800，小于原始点数，则有效范围的终止值为对比波形偏移后的最大点数。)


(2)若对比波形最大点数为 6000，原始波形最大点数为 5000，则有效范围为：0-5000。对比波形点数大于原始波形点数，对比波形将会截断波形并显示。
设置偏移为 200 时，有效范围会变化为：200-5000。(原因：有效范围的终止值最大为原始波形的最大点数。偏移 200 后，对比波形的最大点数为 6200，大于原始波形的最大点数，对比波形会被截断。)
设置偏移为-200 时，有效范围会变化为：0-5000。(原因：有效范围的数值始终为正值，最小为 0；有效范围的终止值最大为原始波形的最大点数，对比波形的最大点数为 6000，偏移-200 后，对比波形的最大点数为 5800，大于原始波形的最大点数，对比波形会被截断。)
设置偏移为-1200 时，有效范围会变化为：0-4800。(原因：有效范围的数值始终为正值，最小为 0；偏移-1200 后，波形左移 1200ms，则波形可能显示不全。此时对比波形的最大点数为 6000，偏移-1200 后，对比波形的最大点数为 4800，小于原始波形的最大点数，则有效范围为对比波形偏移后的最大点数。)
清除：用于清除已导入的对比波形文件。
对比波形方法：点击“导入”按钮，选择对应的数据文件打开，打开后示波器会显示出波形。利用游标测量出需要对比的波形间的水平时间差值，根据差值设置偏移进行波形对比。设置偏移后，按动键盘回车按钮，示波器波形随之变化。
例：原始波形颜色为红色，对比波形颜色为蓝色。导入对比文件后，系统自动获取对比波形的有效范围。

使用游标工具，根据需要进行波形对比，获得原始波形与对比波形在水平方向的距离，即两个 X 游标


之间的差值。

可以在“对比波形”窗口设置偏移，输入偏移后，按动键盘回车按钮，示波器即可显示偏移后的对比波形。此时对比波形有效范围会对应增加/减少偏移大小的点数。如下图所示。



3.导入波形/导出波形

导入波形：导入示波器波形的参数信息，包括：是否显示、编号、数据源、偏移、垂直刻度、每个点的间隔时间等。示波器在停止状态下才能导入波形。导入波形的文件格式为.txt。
导入波形方法：点击菜单栏“辅助功能”中的“导入波形”，选择导入的数据文件后打开。导入成功后波形会覆盖对应通道的波形。（当导入文件中含参数信息时，支持选择是否导入参数。若选择是，则覆盖当前示波器的参数；否则使用当前示波器的参数。）
导出波形：导出示波器波形的信息。当在“参数配置”中选择导出参数时，导出波形参数信息，包括：是否显示、编号、数据源、偏移、垂直刻度、每个点的间隔时间以及各个通道波形的数据；否则，只导出各个通道波形的数据。示波器在停止状态下才能导出波形(对比通道不导出)。导出波形的文件格式为.txt。
导出波形方法：点击菜单栏“辅助功能”中的“导出波形”，选择文件夹保存示波器波形数据。导出的带参数的文件如下图所示。






























5.1.4帮助

点击示波器上方菜单栏“帮助”按钮，点击“鼠标操作指南”，弹出如下所示“鼠标操作指南”窗口。


该窗口用于提示各个模式下鼠标的快捷操作内容。



5.1.5示波器的使用

1.示波器采样方法

1）打开项目工程，连接控制器或仿真器，再打开示波器窗口（操作示波器窗口之前需要连接到控制器或仿真器）。
2）在示波器菜单栏“配置”选择“参数配置”窗口，设置采样周期、最大采集周期数、间隔周期数、是否自动使用TABLE 数据末尾、Table 位置和显示类型等，设置完成后确认保存当前设置。
3）再选择采样数据编号和数据源，选择自动触发/手动触发后，点击启动按钮。
4）将程序下载到控制器运行，选择自动触发时点击启动按钮后立即触发，显示出不同数据源的波形。选择手动触发时，需在点击“  ”后，点击“手动触发”按钮后下载至 RAM/ROM，或点击“  ”后直接下载至 RAM/ROM 后等待 Basic 程序触发，才可成功采样（注：等待 Basic 程序触发时，程序中需加入“TRIGGER”指令）。
5）若波形采样精度不高或显示不完整，可点击按钮停止后再打开“参数配置”窗口，调整好参数(例如增大“最大采集周期数”)后重新执行上述采样过程。
若需要采样的时间较长，可开启“连续采集”功能，此时采样时间与最大采集周期数无关。


TRIGGER 指令可以非常简洁与灵活的辅助波形查看和问题查找，示波器功能对查找问题非常有用，可以针对性的用好，进一步提高调试效率。

2.示波器使用注意事项

(1).示波器采样时间计算：
例如最大采集周期数：1000，间隔周期数：5
如果系统周期 SERVO_PERIOD=1000,也就是 1ms 轨迹规划周期，间隔周期数 5 表示每 5ms 采集一个数据点，一共采集 1000 次数据，采集时间长度为 50s。
(2).TABLE 数据末尾存储空间计算：
设置抓取数据存放的位置，可在“参数配置”窗口设置自动使用 TABLE 数组末尾，此时根据采样数据占用空间大小自动计算起始空间地址。
计算方法：采样数据占用空间大小=通道数*最大采集周期数。
例：若控制器的 TABLE 空间大小为 320000，采样 4 个通道，最大采集周期数为 30000，每个采样点占用一个TABLE，所以会占用 4*30000=120000 个TABLE 位置，320000-120000=200000.此时 TABLE 的起始
位置为 200000。
(3).当不选择自动使用TABLE 数组末尾时，数据存放的位置也可以自定义配置，若按上面的通道数和最大采集周期数，起始 TABLE 空间自定义时不能超过 200000，否则点击按钮后无法运行。如下图。示波器采样数据占用的空间不要与程序使用的TABLE 数据区域重合。

(4).示波器的最大采集周期数设置过低时易造成采集时出现丢点的现象。
例如：当示波器最大采集周期数设置为 100 时，采集得到的波形容易出现丢点；当示波器设置最大采集


周期数为 10000 时，采集得到的波形不易出现丢点现象。对比如下图。


(5).示波器开启连续采集时出现折线的现象。
此现象与最大采集周期数有关，本质为示波器采集时出现了丢点现象。示波器开启连续采集后，在最大采集周期的 80%处会重新触发采集，此时 TABLE 数据开始重新覆写，在此过程中可能会出现丢点现象。




当通道线类型为实线时，则会出现折线。


3.示波器使用例程

例：三轴插补运动


BASE(0,1,2) ATYPE = 1, 1, 1
UNITS = 100, 100, 100
DPOS = -50, -40, 20
SPEED=100, 100, 100
ACCEL=1000, 1000, 1000
DECEL=1000, 1000, 1000 SRAMP=100,100,100

MERGE=ON CORNER_MODE =2,2,2
DECEL_ANGLE = 15 * (PI/180)	'设置开始减速角度
STOP_ANGLE = 180 * (PI/180)		'设置结束减速角度 FORCE_SPEED=100	'等比减速时起作用



TRIGGER DPOS=-50,-40,20

MOVEABS(-50, -40, 20)
MOVECIRCABS(-40, -50, -40, -40, 0) MOVE_OP(0, ON)
MOVEABS(-30, -50, 20) MOVE_OP(0, OFF) MOVEABS(30, -50, 20) MOVE_OP(0, ON) MOVEABS(40, -50, 20) MOVE_OP(0, OFF)
MOVECIRCABS(50, -40, 40, -40, 0) MOVE_OP(0, ON)
MOVEABS(50, -30, 20) MOVE_OP(0, OFF) MOVEABS(50, 30, 20) MOVE_OP(0, ON)


MOVEABS(50, 40, 20) MOVE_OP(0, OFF) MOVECIRCABS(40, 50, 40, 40, 0) MOVE_OP(0, ON) MOVEABS(30, 50, 20) MOVE_OP(0, OFF)
MOVEABS(-30, 50, 20) MOVE_OP(0, ON) MOVEABS(-40, 50, 20) MOVE_OP(0, OFF)
MOVECIRCABS(-50, 40, -40, 40, 0) MOVE_OP(0, ON)
MOVEABS(-50, 30, 20) MOVE_OP(0, OFF) MOVEABS(-50, -30, 20) MOVE_OP(0, ON) MOVEABS(-50, -40, 20) MOVE_OP(0, OFF)



MOVEABS(-50, -40, 5)
MOVEABS(-50, -40, 5)
MOVECIRCABS(-40, -50, -40, -40, 0)
MOVEABS(40, -50, 5)
MOVECIRCABS(50, -40, 40, -40, 0)
MOVEABS(50, 40, 5)
MOVECIRCABS(40, 50, 40, 40, 0)
MOVEABS(-40, 50, 5)
MOVECIRCABS(-50, 40, -40, 40, 0)
MOVEABS(-50, -40, 5)


END


示波器采样轴 0、轴 1 和轴 2 的位置和速度曲线：



XY 模式下的两轴插补合成轨迹：

XYZ 模式下三轴插补合成轨迹：




XYZD 模式下四通道合成轨迹，点的直径大小通过在“观察器配置”窗口修改D 基准数值和 D 基准大小修改。当通道线类型设置为点时，示波器只显示圆点；当通道线类型设置为实线或虚线时，则示波器显示时点与点之间会使用实线或虚线连接。示波器显示和观察器配置如下图所示：




 手动运动

“手动运动”用于通过手动操作电机。可通过菜单栏“工具”→“手动运动”打开。
操作方法：连接好控制器及电机，打开该工具。可在左侧实时输入/修改轴相关的参数，选择轴号（可在下拉列表中选择），设置好相关参数后，按住“左转”/“右转”按钮不放，电机持续左或右运动，松开按钮停止运动。“指令位置”显示当前 DPOS 运动距离（单位为 units）。填写“距离”参数，点击“运动”，勾选“绝对”时，电机运动到绝对距离参数位置；不勾选“绝对”时，点击“运动”，电机按相对距离参数运动。
“反馈位置”/“运动状态”/“轴状态”用于监测反馈轴运动状态，这三个参数均为只读，不可修改。按下“停止”按钮轴运动立刻停止。


 输入口
用于实时监测控制器输入口状态变化。该窗口的输入状态随控制器输入口状态变化而变化。通过菜单栏 “工具”→“输入口”打开。
程序中使用 INVERT_IN 指令设置使某个输入口输入反转（ZMC 系列的特殊输入定义后需要信号反转，因为 ZMC 系列是 OFF 有效，ECI 系列不需要反转）。设置了输入反转之后，“输入反转”一栏绿灯常量，无反转则是暗红色，此时输入口有输入信号时，“输入状态”显示灰色，输入口无输入显示绿色。
“特殊输入”用来显示原点、限位、报警等特殊信号提示。例：JOG 运动







上图中，输入 IN0、IN1、IN5、IN6、IN7、IN8 均定义输入反转（ZMC 系列控制器默认 OFF 有效，故进行信号反转，ECI 系列与之相反）。当给 IN5 一个输入信号时，反转后输入状态灯则不亮，其他口均无输入。
点击“IO 选择”可自定义显示输入 IO 口，每 16 个输入一组，如下图所示。“刷新”刷新各个输入 IO
的状态及特殊功能定义。






 输出口

用于监测控制器 OUT 口状态并对输出口进行操作。通过“工具”→“输出口”打开。查看 OUT 口状态还可通过仿真器查看，但仅能显示编号 0-11 的状态。
操作方法：按下按钮能操作 OP 口输出，如下图，OP1 口和OP2 口开启，其他口关闭。“IO 选择”选择要显示的输出，每 16 个输出一组。直接点击输出口可以切换其输出状态。






 寄存器

用于批量查看控制器寄存器的数值，可以选择查看不同类型的寄存器（支持 PLC 功能的控制器才支持此功能）。使用该窗口可通过菜单栏“工具”→“寄存器”打开。
使用方法：选择要读取的寄存器的类型、起始编号、个数后点击“读取”即可在窗口显示出数据。勾选自动刷新功能便于自动实时采集寄存器值变化并显示出来，否则需要再次点击读取才能获取的寄存器的值变化情况。注意：读取个数不要超出寄存器范围，否则会提示错误。




寄存器类型：
X(IN)：输入继电器，由外部开关信号驱动，对应 IN Y(OP)：输出继电器，能直接驱动外部负载，对应 OP S：状态继电器，用于对工序步进控制
M：辅助继电器，不能直接驱动外部负载，对应 MODBUS_BIT
D(MODBUS_REG)：16 位整型数据寄存器，MODBUS 区域数据，对应 MODBUS_REG D(MODBUS_LONG)：32 位整型数据寄存器，MODBUS 区域数据，对应 MODBUS_LONG D(MODBUS_IEEE)：32 位浮点型数据寄存器，MODBUS 区域数据，对应 MODBUS_IEEE D(MODBUS_STRING)：：1 字节字符串数据寄存器，MODBUS 区域数据，对应 MODBUS_STRING AIN：模拟量输入
AOUT：模拟量输出
DT(TABLE)：浮点寄存器，长度 32 位，对应 TABLE V：变址寄存器，长度 16 位。
Z：变址寄存器，，长度 16 位。 T：定时器，单位为 1ms。 C：计数器。
VR：掉电保存寄存器，32 位浮点型。
VR_INT：掉电保存寄存器，32 位整型。
“导入”/“导出”:可快速上传/下载寄存器数据。导出数据方便客户把自己关注的部分寄存器导出到文


本保存。导入数据方便客户把关注部分的已保存的数据直接更改到控制器内部。导出数据示例：


















 锁存图像

在机器视觉开发环境下使用此窗口查看视觉图像。必须先采集到图片之后图像窗口才能正常使用，可在全部视觉图像显示和视觉锁存通道图像显示之间切换。（通过菜单栏“工具”→“锁存图像”打开）
视觉图像：通过视觉指令将控制器中的图片显示出来，包括采集到的图像和处理的图像。并可将该图片保存到锁存通道中，若锁存通道中已有图片，原图片则将被替换。
视觉锁存：显示锁存通道中的图像。选择锁存通道号后将显示当前锁存通道中的图像，当前锁存通道为


空时，没有图像。

获取锁存通道中的图像例子：
以下是以仿真器举例，需提前将图片存放至仿真器的 flash 文件夹下才可读取。若为控制器则需将图片存放至控制器的 flash 中。


AD/DA


用于查看模拟量输入和模拟量输出变化及对应刻度值等。使用该工具可通过菜单栏“工具”→“AD/DA”打开。（注意：使用该工具需要控制器支持模拟量输入和输出功能）
操作方法：连接上支持模拟量输入/输出的控制器，打开“AD/DA”工具窗口，点击“重新读取”即可读取到当前控制器的模拟量值。
参数介绍：
通道号：显示当前已连接的控制器的 AD/DA 对应的输入输出口。大小：显示每个通道口的 AD/DA 大小，以百分比显示。
刻度值：显示已输入或输出的模拟量刻度值。（DA 的“刻度值”和“电压或电流值”支持修改。）电压或电流值：显示已选的“电压或电流范围”内的值。
最大刻度值：取决于控制器模拟量的分辨率为 12 位还是 16 位。具体可查看对应的用户手册。模拟量输入分辨率为 12 位，对应的刻度值范围为 0~4095。
模拟量输入分辨率为 16 位，对应的刻度值范围为 0~65535。电压或电流范围：根据需求及控制器所支持的范围进行量程选择。


PWM

读取/设置控制器的 PWM 的占空比、频率等相关参数。支持的 PWM 通道数根据控制器型号不同自动


识别读取。使用该工具可通过菜单栏“工具”→“PWM”打开。
操作方法：连接支持 PWM 的控制器，打开“PWM”工具窗口，点击“重新读取”即可获得相应 PWM
参数。同时也支持在该工具中直接设置参数数据。


SDO

用于把 EtherCAT 数据字典写入/读取到控制器。使用该工具可通过菜单栏“工具”→“SDO”打开。操作方法：
1.选择识别方式：按“设备号和槽位号方式”/“轴号方式”对设备进行数据字典读取/写入。
2.选择需要写入/读取数据的设备，若选择“设备号和槽位号方式”，则需要填写“设备编号”和“插槽”；若选择“轴号方式”则需要填写“轴号”。
3.设置数据字典：根据实际需求选择需要读取/写入的“SDO 数量”，并手动添加正确的数据字典索引、子索引、数据类型及写入值等相关参数（具体需查看驱动器 EtherCAT 通讯手册），点击“写入”即可，若参数设置有误则会返回相应错误码。
4.读取数据字典：设置读取后数据需要存储到的 Table 位置，再打印对应 Table 位置即可获取数据。也可在该工具中添加好要查询的数据字典索引及正确的相关参数，点击“读取”即在“读取值”列显示。




 故障诊断

用于快速查看控制器状态及故障的详细信息，使用该工具可在菜单栏“工具”→“故障诊断”打开。该工具支持查看控制器型号、日期、版本号、任务运行状态以及错误信息提示，方便客户现场查看故障
以及程序停在哪里。
Run 灯、Alm 灯也可以手动开关，便于在众多控制器里快速找到当前连接的控制器。任务运行状态：



错误信息提示：



 总线状态诊断

用于查看控制器当前支持的总线，总线上连接的所有节点的设备信息。使用该工具可在菜单栏“工具”
→“总线状态诊断”打开。
打印信息含义参见?*ETHERCAT 指令帮助。该窗口显示信息内容与在[命令与输出]窗口使用在线命令输入：“?*ETHERCAT”和“?*RTEX”打印数据结果一致。
点击“更多信息”可查看当前总线槽位号上已连接的相关设备基本信息及丢包检测。



 插件

插件管理器是 RTSys 新增的一个功能，支持自定义添加小软件，可将需要用到的小工具的应用程序添加到该处（支持的插件格式有.exe、.zpl 和.bat）。该工具支持对插件进行管理，包括：新增/删除插件，对插件程序显示顺序进行调整（上移/下移），修改插件名称及设置提示等。可通过菜单栏“工具”→“插件”打开。
RTSys 支持添加自制插件（格式为.zpl），插件的制作方法可参考《VC 插件制作教程》。插件管理器介绍
新增：新增插件程序（支持.exe 和.zpl、.bat 格式）删除：删除选中插件
上移/下移：对当前选中插件调整顺序位置，向上移动或向下移动。
立即重置：对.zpl 格式的插件强制重置为初始化状态。（例如 xplc screen 插件选择跟随 RTSys 自动连接后，该插件会自动连接到当前控制器；若当 RTSys 换了其他控制器连接时，xplc screen 无法及时刷新则可使用“立即重置”）
菜单名称：设置插件下拉菜单中显示的名称。
菜单提示：鼠标指向插件下拉菜单中某个插件时显示对应文字提示。


注：RTSys 默认自带插件 xplc screen，该插件是触摸屏仿真工具，用于仿真显示 HMI 组态界面。



自制插件的.zpl 程序文件必须单独存放在一个空文件夹中，并把插件相关配置文件放到该文件夹中，再添加到 RTSys 插件功能中。






















打开已添加插件的操作方法：鼠标点击“插件”的下拉按钮，下拉菜单显示多个插件内容，选择对应插件单击即可打开。
注：通过该功能添加的插件在 RTSys 中不显示软件图标，需用户手动添加。



添加插件及菜单项图标的操作方法：
1.在菜单栏“工具”→“插件”打开插件管理器。点击“新增”，弹出以下窗口，双击打开文件添加到插件管理器中，点击“确定”即可。




2.添加成功后在插件管理器和下拉菜单中就会显示新插件。（但不显示该软件图标）同时该插件的对应文件将被添加至 RTSys 目录下 Plug_in 文件夹中。
需要添加图标则按以下步骤：
3.先找一张用来做图标的图片，通过编辑将图片改为 16🞨16 像素的大小。



4.将已修改大小后的图标进行另存为.png 格式，并重命名为 menu，将该图标文件保存至 RTSys 目录下的 Plug_in 文件的对应插件的目录中。如下例子所示，给 ZrobotView 插件，则将上述图标保存至该文件夹下。（注意：图标格式仅支持.png，图标名只能为 menu 才能生效，其他命名无效）
5.保存成功后，回到 RTSys 中重新打开“插件管理器”，再次点击“确定”后关闭该窗口，在下拉菜单中即可显示图标。




第六章 程序调试


操作速览

名称	图示	说明
启动/停止调试	
	对程序和任务启动或停止调试、监测功能
运行	
	对启动调试功能后的程序进行运行
暂停	
	暂停运行在调试监测中的程序
运行到	
	设置运行到指定程序行
单步进入	
	跳到下一条语句
单步跳过	
	跳过下一条语句
单步跳出	
	跳出 SUB 子程序运行
断点	
	在 Basic 程序增加或删除断点
紧急停止	
	紧急停止程序和所有轴的运动


启动/停止调试
调试功能用于追踪程序运行，从调试菜单进入调试模式，可以选择不同的进入方式。当需要查看当前控制器程序的运行状态时，请选择附加到当前程序。进入调试模式后，文件默认开启只读模式，若调试中需修改程序，则需退出调试模式。通过菜单栏“调试”→“启动/停止调试”进入程序调试状态。




再次下载到 RAM：表示程序再次下载到 RAM 运行，RAM 掉电不保存。再次下载到 ROM：表示程序再次下载到 ROM 运行，ROM 掉电保存。
不下载，复位程序：表示不下载程序，重新运行之前下载的程序，并打开任务窗口显示目前的运行状态。
附加到当前程序：表示此时程序不下载，仅打开任务窗口显示目前的运行状态。


当程序运行出错后，RTSys 软件会显示出错信息，双击出错信息可以自动切换到程序出错位置，如果出错信息没有看到，可以通过命令行输入?*task 再次查看出错信息，或打开“故障诊断”窗口。
以如下程序为例说明：
RAPIDSTOP(2) WAIT IDLE(0) WAIT IDLE(1)

BASE(0,1)	'选择轴 0，轴 1 ATYPE=1,1
UNITS=100,100 SPEED=100,100	'运动速度 ACCEL=1000,1000 DECEL=1000,1000 SRAMP=100,100		'S 曲线
MERGE=ON	'开启连续插补
TRIGGER	'自动触发示波器
DPOS=100,0	'坐标偏移


MOVE(-50,100		'第一段相对运动 MOVE(-100,0)	'第二段相对运动


MOVE(-50,-100) '第三段相对运动 MOVE(50,-100) '第四段相对运动 MOVE(100,0)	'第五段相对运动 MOVE(50,100) '第六段相对运动 END
命令与输出窗口打印插补文件的第十七行语法错误，报 error 错误程序无法成功下载到控制器，解决问题之后再次点击下载程序。






























故障诊断窗口也能查询程序出错的信息。






 调试工具

开启调试时，调试工具栏有效。


运行：运行控制器。
暂停：暂停控制器运行，所有任务将被暂停。
单步进入：单步进入 SUB 子函数里面，若无子函数，则单步运行下一行程序。
单步跳过：运行下一行程序。若运行到 SUB 子函数调用，则不进入但仍执行 SUB 子函数内容。单步跳出：跳出 SUB 子函数运行。
运行到：运行到光标所在处的指定行。


当程序与控制器不一致或是对程序进行再修改后没有及时下载，会导致调试指定的行号产生偏移。程序暂停时当前已经进入缓冲区的运动并不会暂停。


 断点

通过增加断点来捕获和暂停程序的运行。
断点调试可以查看程序运行的具体过程，主要用于判断程序逻辑错误。配合监视内容和轴参数变化情况可以查看程序每执行一步对寄存器、变量、数组等的影响。程序停止在断点处后，就可以进行逐步调试，快捷键 F11，按一次程序向下执行一步。
新增/删除断点操作方法：
断点快捷键 F9 添加/删除，或菜单栏“调试”→“增删断点”，断点可以添加多个，菜单栏“调试”-“清除断点”可一次性清除项目文件中的所有断点。
添加断点后，程序运行会停止在断点处，此时断点处所在行的指令不执行，前面已经扫描的程序功能不受影响，如下图，第 18 行 MOVE(-100,0)已经执行，此时走完第二段插补运动，轴的位置为（-150,100）；第三段运动（即第 19 行）不执行，轴的位置不变。
注意：如果断点是设置在循环中，那么下次循环运行到断点处时还是会停止程序。

设置断点后一般程序运行到断点处所有任务都会暂停。若只需暂停当前任务，则可以在菜单栏“控制器”→“设置”→勾选“断点仅暂停单个任务”，勾选后添加的断点即可只暂停单个任务。（注意：勾选该项前已添加的断点仍默认为暂停全部任务）




断点编辑
通过断点编辑窗口可以查看当前文件中已添加的所有断点，并对所有的断点所在行进行编辑。双击断点信息可跳转到断点行，可以选择移除一个或多个断点，移除后点击“确定”生效。
程序调试完成后，需要清除所有断点或者关闭调试模式才能下载到控制器运行，不关闭调试模式且不清除断点就下载程序到控制器，命令与输出区域打印如下警告信息：Warn file:"Basic1.BAS" line:11 task:0, Paused.

 紧急停止


紧急停止可立即停止程序和所有轴的运动。
在运动控制调试过程中，为避免发生失控等紧急情况，RTSys 软件中配置了紧急停止功能，该功能按钮可在菜单栏“常用”→“紧急停止”（或“调试”→“紧急停止”）。





第七章 PLC 菜单


操作预览

项目	图标	说明
取指令	
	用于与母线连接的常开触点
取反指令	
	用于与母线连接的常闭触点

取脉冲上升沿指令	
	用于检测与母线连接的常开触点的上升沿，仅在指定位软元件的上升沿时
（由OFF→ON 变化时）接通一个扫描周期

取脉冲下降沿指令	
	用于检测与母线连接的常开触点的下降沿，仅在指定位软元件的下降沿时
（由 ON→OFF 变化时）接通一个扫描周期
步进开始指令	
	使用步进梯形图指令程序的起始指令

比较指令	
	用于两个数据之间的数据比较，将操作数 S1、S2 按指定条件进行比较，
满足条件触点导通，不满足条件触点闭合。
输出指令	
	软元件线圈驱动的指令
函数	
	打开 PLC 指令输入表，选择指令
子程序	
	建立 PLC 子函数，作为子函数的入口
横线	
	添加梯形图横线
竖线	
	添加梯形图竖线
删除横线	
	删除梯形图横线
删除竖线	
	删除梯形图竖线
转换为语句表	
	将梯形图转换为语句表
转换成梯形图	
	将语句表转换成梯形图


寄存器使用列表	
	查询当前项目下各类寄存器的使用情况和寄存器注释
交叉参照表	
	查询当前项目下各类寄存器的使用和存在位置
插入一行	
	在选中窗格上方，插入一行
扩展一列	
	在选中窗格左侧，扩展一列



 梯形图快捷工具

以取指令为例，点击“取指令”图标弹出如下窗口，在 S 一栏选择操作数，点击“确定”即可在梯形图中形成一个常开触点。



 代码转换

此功能仅限 PLC 编程下使用，用户可使用梯形图和语句表两种编程方式，梯形图便于可视化编程，指令表编程针对对指令语法和编程逻辑较为熟悉的用户。

7.2.1转换为语句表

将梯形图转换为语句表后的效果如下图。注意需要在无语法错误的前提下，才能转换成功。


7.2.2转换为梯形图

将上方语句表转换成梯形图显示效果如下图。注意需要在无语法错误的前提下，才能转换成功。




 交叉参数表

通过菜单栏“PLC”-“交叉参数表”打开下图窗口。
“交叉参照表”仅适用于 PLC 编程方式，查看已使用的寄存器的具体信息，双击该行使光标跳转到寄存器在程序中的对应窗格。
右上角的“保存”按钮，用于将搜索到的数据另存为 csv 格式。



 寄存器使用列表

通过菜单栏“PLC”-“寄存器使用列表”打开下图窗口。
“寄存器使用列表”仅适用于 PLC 编程方式，便于查看寄存器的使用情况，获知寄存器在程序中使用和使用的次数，支持在注释栏显示注释或编辑注释，需要在梯形图编程界面显示注释，需要在右键菜单勾选“显示批注”。
使用方法：选择要搜索的寄存器，输入个数，该项目下需搜索的 PLC 文件等，点击“搜索”，窗口内即可显示出搜索的结果。
在“寄存器”或“使用(次数)”两行双击鼠标左键，可弹出“交叉参数表”窗口。右上角的“保存”按钮，用于将搜索到的数据另存为 csv 格式。


第八章 HMI 菜单


操作预览

项目	图标	说明
新建窗口	
	新建一个Hmi 窗口
导入窗口	
	导入已有的 Hmi 窗口（仅支持.hmi 格式）
背景预设	
	预设全局窗口背景及元件样式
窗口对比		选择一个Hmi 文件与当前 hmi 进行对比
显示缩略图	/	组态视图显示为窗口缩略图
显示详细信息	/	组态视图显示窗口及元件详细信息
控件箱	
	打开/隐藏[控件箱]窗口，存放 HMI 所有元件，可从控件箱直接调用
文本库	
	一次设置多种语言文本内容并在元件调用

图片库	
	添加图片到图片库并支持调用，分为系统图片库和用户图片库，图片仅供
Hmi 使用
按键转换	
	将物理按键与虚拟键功能绑定


排列	

/	对多个元件进行排列，包括左对齐、右对齐、上对齐、下对齐、水平/垂直
居中对齐、水平/垂直相同间距、相同宽度/高度/尺寸、窗口水平/垂直居中显示
批量修改地址	
	批量修改寄存器地址
Hmi 设置	
	对 Hmi 系统预设置，包括设置起始窗口或分辨率等
属性	
	打开/关闭[属性]窗口，可查看/设置 HMI 元件/窗口属性信息

快捷图片库	
	打开/关闭[快捷图片库]窗口，可查看 HMI 图片库内容，并快速应用到 HMI
元件上或删除元件上已应用样式
显示/隐藏图层	
	显示/隐藏顶层、中层、底层的元件


栅格	/	显示/隐藏栅格
元件名称	/	Hmi 窗口中显示/隐藏元件名称
语言	
	切换文本库中的语言
状态	
	切换元件状态

 元件设置

RTSys 支持 Hmi 编程方式，提供了丰富的常用元件，用户可自行从控件箱中进行调用。同时支持对元件属性进行设置。通过菜单栏“HMI(P)”→“控件箱”即可找到元件，一般控件箱会隐藏在界面最左侧，鼠标点击则弹出窗口。
 
控件箱元件简介可参考控件箱章节，更多详细介绍及使用请参考《RTHmi 编程手册》。
添加元件操作方法：新建/打开.hmi 文件→打开 Hmi 窗口→打开控件箱→单击控件箱中的元件→鼠标移动到Hmi 窗口中单击添加即可。
元件属性设置
Hmi 支持用户添加元件并修改元件属性，元件属性包括基本属性、外观、标签、动作、位置和尺寸等内


容。下面以“功能键”元件举例。

属性	功能	说明
基本属性
元件编号	/	支持修改编号
元件名称	/	支持修改元件名称


显示层次	

选择元件显示层次	顶层：表层，显示在最外层，覆盖底下元件
中层：中间层
底层：底层(默认)


有效显示	

选择元件是否显示	显示：下载运行后显示元件且功能可用不显示：下载运行后不显示
仅显示，不可用：下载运行后显示但功
能不可用

采用有效控制	
通过寄存器控制元件是否显示	默认 False，选择 Ture 才会显示以下三个参数，通过寄存器控制元件是否显示
设备编号（有效
控制为 True）	
设备编号	
默认 local
寄存器类型（有效控制为
True）	
选择寄存器类型	
多种寄存器下拉列表选择
寄存器编号（有效控制为
True）	
设置寄存器的编号	
寄存器值为 0 时不显示，非 0 时使用
安全时间 ms	最少按键时间	单位 ms
绑定虚拟按键	选择要绑定的虚拟按键码	默认不选择
绑定物理按键	绑定示教盒上面的物理按键	按键码值查看“虚拟键”章节
外观
图片来源	背景图片或背景图片库	图片库或背景图片中选择
背景图片库	背景图片选择	在图片来源先选择背景图片库后添加
绘制边框	选择是否绘制边框	/
是否图片化	元件变为图片的形式	默认 False
标签
文本库	文本库的名称	不设置文本库显示格式文本

格式文本 0/1	打开格式文本设置窗口设置元件
要显示的文本	
默认显示文本 0，按下时显示文本 1
动作
动作	按键执行时的动作	参见“动作”章节描述

松开时动作	
选择按下时或松开时执行动作	默认 False 为按下执行动作，Ture 为松
开时动作
动作函数名	按键动作后要调用的 SUB 函数	下拉列表选择Basic 已有全局 SUB 函数


位置和尺寸
水平位置	元件的水平起始位置	不要超出水平分辨率
垂直位置	元件的垂直起始位置	不要超出垂直分辨率
宽度	元件的宽度	/
高度	元件的高度	/

不同元件属性有所不同，更多属性介绍请查看《RTHmi 编程手册》。



 窗口设置


8.2.1新建/导入窗口

新建窗口：在当前项目中新建一个或多个窗口。
导入窗口：在当前项目导入其他项目中已创建的 HMI 窗口。（可选一个或多个同时导入）
选择要导入窗口的 HMI 文件后即可打开以下窗口，可对该文件中的窗口进行选择性导入。
（注：若导入的 HMI 项目中有窗口编号与原项目中的窗口编号重复，则窗口名称显示为红色，可选择是否替换原窗口。）





8.2.2背景预设

对 HMI 窗口背景及部分元件设置默认的样式/颜色，保存设置后在新建窗口或元件时生效。对已创建的窗口和元件不更改原样式。
操作方法：选择需更改样式的元件/窗口，点击“修改”，有两种样式方式选择。
方法一：若使用图片库样式则选择“图片库”可将图片导入，更改图片比例可调整元件显示大小； 方法二：若不使用图片库样式则在“状态 0”和“状态 1”自定义颜色。（注意：两种样式方式只能二
选一生效，使用了图片库样式则会覆盖自定义颜色样式）
窗口预设背景颜色则使用方法二设置“状态 0”自定义颜色，新建窗口后生效。




8.2.3显示缩略图/详细信息

显示缩略图：在组态视图显示窗口缩略图、窗口号及窗口名称。（黄色背景表示当前已打开的窗口，鼠标单击可切换）参考以下左图。
显示详细信息：在组态视图显示窗口信息（窗口号、窗口名称）和元件信息（已创建的元件编号和元件名称）。参考以下右图。


 

 资源使用



8.3.1控件箱

工具箱主要是存放各种组态元件。HMI 开发可在该窗口中添加组态元件。在菜单栏“视图”→“工具箱”即可打开，一般默认将工具箱隐藏至界面最左侧。

工具箱元件简介：

名称	图示	说明
矢量图形
线段/多线段/多边形	
	根据绘制点数画出相应的线段或多边形
矩形	
	绘制矩形
贝塞尔曲线	
	四点绘制三阶贝塞尔曲线
圆/圆弧/扇形	
	拖动绘制整圆/椭圆/圆弧/扇形
刻度	
	绘制等距的间隔刻度


表格	
	绘制 3🞨3 表格，可自定义修改表格样式
导入	
	导入矢量图形
常用控件
静态文本	
	在窗口添加静态文字，可自定义设置相关属性
图片	
	从系统/背景图片库插入图片
位状态显示	
	根据绑定的位寄存器地址的值显示对应的状态
多状态显示	
	根据绑定的字寄存器地址的值显示对应的状态
位状态设置	
	根据元件动作的状态设置位寄存器地址的值
多状态设置	
	根据元件动作的状态设置字寄存器地址的值

位状态切换开关	
	根据元件动作设置位寄存器地址的值并显示对应状态
（位状态显示和位状态设置的功能结合）

多状态切换开关	
	根据元件动作设置字寄存器地址的值并显示对应状态
（多状态显示和多状态设置的功能结合）

功能键	
	根据元件动作实现状态切换/窗口切换/软键盘切换等，只
有两种显示状态并且无法绑定寄存器
物理按键	
	用于与虚拟按键/实际按键绑定后通过实际按键动作
列表	
	可显示多个列表项，通过绑定的寄存器的值切换对应选项
值	
	编辑并显示数值以及修改对应绑定寄存器的值
字符显示	
	编辑或显示字符串并更改字寄存器的值
滑块开关	
	通过拖拽滑块更改字寄存器数值
定时器	
	定时刷新进行重复动作
自定义	
	在元件区域内通过调用 basic 函数实现动态绘图
增强控件
报表视图	
	以表格的形式呈视多组数据，用于显示和管理报表数据


文件浏览器	
	显示当前目录、以表格形式显示文件内容
三次文件编辑器	
	支持HMI 中开发三次程序的编辑元件
CAD	
	显示矢量图形
菜单	
	设置菜单项，通过点击菜单项触发调用 SUB 动作


树形图	

	以树状图形式显示所有表项，单击树节点坐标的角图标
（或双击树节点内容）可进行子树的展开/缩起，单击树节点内容进行触发动作。
组态元件详细介绍请参考《RTHmi 编程手册》。



8.3.2文本库

采用标签形式一次性设置不同状态下的多种语言文本以及每个文本对应的字体并在对应元件调用它，每个语言可填写不同的文字内容。（一个标签最多支持 256 种状态，一种状态最多支持编写 8 种语言文本）
操作方法：
1.在[标签设置]板块双击添加“标签名”。（“标签”用于区分元件调用哪个文本库）。
2.设置所需的状态数和语言数、文本的格式。
3.对状态进行选择，在已选择的状态下给对应数量的语言添加需要显示的文本内容。
4.设置好标签及语言文本后，若无需设置字体，点击[确定]即可保存。
5.若需设置字体，则需要在项目中先添加字体文件，导入字体后可在[字体设置]板块对每个语言进行字体设置，调用对应文本库后运行即可显示。




8.3.3图片库

汇总存放 Hmi 元件样式图片或自定义图片的库。包括系统图片库和用户图片库。该库中支持对图片样式颜色，不同状态对应显示内容/颜色等进行修改。
注：图片库最大数量为 512。
系统图片库为系统默认图片，不支持删除或增加。该库为用户提供了丰富的元件样式选择，用户可直接


在该处选择并应用。
用户图片库为用户创建自定义图片库，支持添加外部图片到该库中使用。元件应用图片库样式有两种操作方法：
(一) 在背景预设中直接修改元件的样式，具体操作可参考背景预设章节。
(二) 在 Hmi 窗口中添加一个元件，点击该元件弹出“属性”编辑窗口，在[属性]窗口中的“图片来源”选择“背景图片库”，选择后在下一行弹出“背景图片库”项，点击该项后的“…”，即可打开图片库，在图片库中找到喜欢的样式图片单击该图片，再点击“确定”即可应用到元件中。

























更多详细操作请参考《RTHmi 编程手册》。



8.3.4按键转换

将物理按键与虚拟键的功能绑定起来，实现通过操作物理按键即可使用虚拟键功能的效果。该工具已预设了ZHD300X 和ZHD400X 的按键功能。同时支持将已设置好的键值内容导出或导入。（通过菜单栏“HMI”
→“按键转换”打开）
物理键：物理键是指外部设备上的实际按键，每个按键都有独有的编码值（例如下图的物理键编码数值），按下时会发送一条信息，这条信息就是按键的编码值。


虚拟键：虚拟键编码值 0-127 都对应 ASCII 码表，128 之后则已自定义了对应功能。虚拟键值对照表可查看《RTHmi 编程手册》。
注意：1.物理键的编码值由硬件决定，程序中无法修改。外设不同，对应按键的编码值也不同。
2.Hmi 中，虚拟键编码由底层封装而成，程序中无法修改。操作方法：
(一) 若使用已预设的 ZHD300X 和 ZHD400X 的按键功能：打开“按键转换”窗口→点击右上方的“下拉菜单”按钮，即可选择对应型号示教盒→点击“预设”，列表即可显示对应按键功能→点击“确定”设置成功。（若要更改预设内容，可点击“清除”即可全部清除；若只更改部分内容可直接双击修改）
(二) 若需新建一套自定义按键功能：打开“按键转换”窗口→在对应空白格处双击即可填入物理键值和虚拟键值→点击“确定”即可保存。




















 控件排列

RTSys 提供多种对齐排列方式，将多个元件按一定规则整齐排列，使得整个 Hmi 界面更加美观有序。需同时框选多个元件才能进行排列。包括了左/右对齐、上/下对齐、水平/垂直居中对齐、水平/垂直相同间距、相同宽度/高度/尺寸、窗口水平/垂直居中显示以及锁定元件。（通过菜单栏“HMI”→“排列”可进行使用）


以上排列方式均以框选元件中显示红色框为目标元件，以目标元件为标准进行排列对齐。若需自定义目标元件，则先选中目标元件，按住“crtl”键，鼠标单个点击其他跟随元件，元件选择完毕后松开“ctrl”键，再选择排列方式。

HMI 编辑


8.5.1批量修改地址

对多个 HMI 组态元件的寄存器地址进行批量修改，可修改为统一的寄存器类型，以及设置地址间距。通过菜单栏“HMI”→“批量修改地址”可打开如下窗口。
注：仅显示支持绑定寄存器的组态元件。



操作方法：
1.在 HMI 窗口中，选中需批量设置寄存器的多个元件；（建议选择可使用相同类型寄存器的元件）
2.打开“批量修改地址”窗口，可看到选中的支持绑定寄存器的元件名称及可设置的寄存器类型和地址；
3.对元件的“寄存器类型”进行选择，点击即可弹出下拉列表；并设置寄存器地址起始编号；（选择
AUTO 则表示自动跟随上一个寄存器类型，选择其他类型则不改变已选类型）

4.在窗口下方根据需求设置“地址间距”，默认间距为 1。确定间距后点击“自动地址”；该窗口中寄存器类型为 AUTO 的将被统一，地址则按设置的地址间距顺序排列显示。点击“确定”即可。如下图所示。




注意：
1.第一个元件不能设置为 AUTO 类型；
2.设置地址时注意寄存器占用空间是否重复；
3.元件显示顺序按元件添加顺序排列，建议将需要使用同类型寄存器的元件按顺序进行选择（选中某个元件后按住“ctrl”键按顺序依次对元件进行选择）
4.每次自动地址后，若需重新修改寄存器类型并自动地址，需重新手动对某个寄存器修改类型，并将在该寄存器之后的寄存器设置为 AUTO 类型。

8.5.2Hmi 设置

对 HMI 系统进行初始属性设置，可修改整体 HMI 窗口分辨率、起始基本窗口等属性，详情可参加下表。通过菜单栏“HMI”→“Hmi 设置”即可打开 Hmi 系统设置的属性窗口。


背光时间	示教盒实际背光时间	/
屏保时间	设置屏保时间	/
起始基本窗口	设置HMI 起始显示的基本窗口	默认显示 10 号窗口
起始置顶窗口	设置HMI 起始置顶窗口	/

初始化函数	
添加HMI 初始化函数	上电后只调用一次的函数，在 Basic 文件中定义 ，函数 的定 义必须 是全 局
(GLOBAL)的 SUB

周期函数	
添加HMI 周期函数	上电后不断周期扫描的函数，在 Basic 文件中定义，函数的定义必须是全局 
(GLOBAL)的 SUB


压缩图片	

选择是否对图片进行压缩	旧压缩：有损压缩且不可逆( 即使用
ZDevelop 中的压缩方式)
新压缩：无损压缩，图片画质不变不压缩：不压缩图片

图片质量	选择图片显示的质量
(注：选择不压缩时，图片质量决定是否防失真缩放)	标准：图片显示质量较低，但 HMI 性能较高
高：图片显示质量较高，但 HMI 性能降低

文本自适应	
文本自适应元件大小	文本显示内容超出元件范围时自动缩小字
体，最小不低于用户设置的 50%
不使用文本库格式文本	True：使用控件格式文本
False：使用文本库格式文本	
/
水平分辨率	窗口显示的分辨率	/
垂直分辨率	窗口显示的分辨率	/



 显示设置


8.6.1属性窗口

用于显示和设置 HMI 文件中的窗口/元件属性。打开该窗口需先新建/打开 HMI 文件，再在菜单栏“视图”→“属性”即可打开，否则“属性”则呈灰色不可点击状态。
窗口和元件的属性窗口如下图所示：



元件“属性”功能窗口	背景“属性”功能窗口元件属性介绍（以功能键为例）：


设备编号（有效控
制为 True）	
设备编号	
默认 local
寄存器类型（有效控制为 True）	
选择寄存器类型	
多种寄存器下拉列表选择
寄存器编号（有效
控制为 True）	
设置寄存器的编号	
寄存器值为 0 时不显示，非 0 时使用
安全时间 ms	最少按键时间	单位 ms
绑定虚拟按键	选择要绑定的虚拟按键码	默认不选择
绑定物理按键	绑定示教盒上面的物理按键	按键码值查看“虚拟键”章节
外观
图片来源	背景图片库或背景图片	图片库或背景图片中选择
背景图片（库）	背景图片选择	在图片来源先选择背景图片后添加
绘制边框	选择是否绘制边框	/
是否图片化	元件变为图片的形式	默认 False
标签
文本库	文本库的名称	不设置文本库显示格式文本

格式文本 0/1	打开格式文本设置窗口设置元件要显
示的文本	
默认显示文本 0，按下时显示文本 1
动作
动作	按键执行时的动作	参见“动作”章节描述

松开时动作	
选择按下时或松开时执行动作	默认 False 为按下执行动作，Ture 为松开时动作
动作函数名	按键动作后要调用的 SUB 函数	下拉列表选择Basic 已有全局 SUB 函数
位置和尺寸
水平位置	元件的水平起始位置	不要超出水平分辨率
垂直位置	元件的垂直起始位置	不要超出垂直分辨率
宽度	元件的宽度	/
高度	元件的高度	/


窗口属性介绍：

名称	功能	说明
基本属性
窗口编号	当前窗口的编号	同一项目下窗口编号不能重复
窗口名称	当前窗口的名称	/
窗口类型	可选 5 种窗口类型	参见“窗口类型”说明
垄断	选择是否垄断	垄断后不能操作窗口下层的元件
外观
背景颜色	选择窗口背景颜色	/
绘制边框	选择是否绘制边框	选择TRUE 之后，可选择边框颜色
图片来源	从背景图片库或背景图片中选择	先添加图片才能选择，图片名称不超过 26


		个字符

公共窗口 1	
设置当前窗口的公共窗口 1	当前窗口可以显示公共窗口的控件，最多可
设置 3 个公共窗口
透明度	背景透明度	预留，暂不支持使用
位置和尺寸
水平位置	窗口显示的左上角X 坐标	不要超出水平分辨率
垂直位置	窗口显示的左上角Y 坐标	不要超出垂直分辨率
宽度	当前窗口的显示宽度	/
高度	当前窗口的显示高度	/
更多详细属性介绍请查看《RTHmi 编程手册》。

8.6.2快捷图片库

用于给 HMI 元件快速应用或移除图片库中的样式。通过菜单栏“视图”→“快捷图片库”即可打开该窗口。
操作方式：
添加图片库：打开 HMI 文件，选中 HMI 窗口中的单个元件，打开快捷图片库，找到想要应用的样式图片，双击该图片，即可将图片快速应用到元件上。（图片库中有多个分类，可于[快捷图片库]窗口顶部点击则弹出下拉菜单进行选择）
移除图片库：单击选中元件，在[快捷图片库]窗口中点击“移除图片库”即可。




显示/隐藏图层：对设置在不同图层的元件进行选择显示或隐藏。（在菜单栏“HMI”→“显示/隐藏图层”进行设置）
操作方法：
1.点击元件，打开属性窗口，在元件“显示层次”中进行选择，共有顶层、中层、底层可选。
2.点击“显示/隐藏图层”弹出下拉菜单，选择某个图层进行显示或隐藏。


栅格和元件名称：选择 HMI 窗口中是否显示栅格，元件和窗口是否显示名称。栅格的作用便于用户将文件参考对齐。全部勾选则如下图显示：






语言/状态切换

语言切换：对已调用文本库的元件进行语言切换。需先在文本库设置好当前状态下每个语言对应的内容，在该元件进行文本库调用，选择语言进行文本内容切换，L0 即对应语言 0，L1 即对应语言 1，以此类推，超出可在更多中输入语言编号。语言最多可设置 8 个，即 L7。
状态切换：对功能键或位状态/多状态元件进行状态切换，选择状态 S 即可切换到不同状态。S0 对应状态 0，S1 对应状态 1，以此类推。状态最多可设置 256 个，即 S255。




切换语言 L1 后的效果如下：

切换状态 S1 后的效果如下：






第九章 RTSys 文件类型

使用 RTSys 必须理解“项目”的概念。为便捷的进行应用设计开发，通过建立一个文件夹，里面包含该应用相关的各个程序，这样的一个集合体称之为“项目”,一个项目通过一个项目文件内含一个或多个文件来管理。


 项目文件

项目文件的文件名后缀为“.zpj”，项目里面程序文件必须与项目文件位于同一个文件夹（即将 zpj 文件包含的 bas/plc/hmi 文件保存在同一文件夹内）。
打开文件时选择打开项目，已增加到项目的文件，即该 zpj 文件下的 bas/plc/hmi 文件会自动打开，或者拖动 zpj 文件到 RTSys 直接打开。
只打开 bas/plc/hmi 文件，不打开对应的项目，程序无法下载，也无法运行。



 程序文件

程序文件则是包含在项目中用于支持编程的文件，主要包括 Basic 文件、PLC 文件、HMI 文件。其中
Basic 程序文件的文件名后缀为“.bas”；PLC 文件的后缀为“.plc”；HMI 组态文件的后缀为“.hmi”。注意：需先创建/打开项目文件（.zpj）后，再在项目文件中打开/新建程序文件，才可下载运行。

ZAR 文件

ZAR 文件是一种加密文件，文件后缀为.zar。项目文件生成 ZAR 文件之后，看不到任何代码，但支持将 ZAR 文件下载到控制器运行，加强程序文件的保密性。生成及下载 ZAR 文件的方法参见第十章—ZAR 下载说明。


 库文件

库文件是由“编译为 Lib”功能生成后保存，便于程序保密或防止程序被修改，库文件的文件名后缀为 “.zlb”，编译为 Lib 文件的方法参见第十章—编译Lib 说明。

ZML 文件

ZML 文件是由正运动开发的用于识别硬件设备的功能，该功能仅限设备通过 EtherCAT 通讯。ZML 文件需通过正运动小工具进行配置生成，生成的 ZML 文件通过 RTSys 软件下载到控制器中。

 字库文件等

RTSys 支持使用自定义字体，需将字体文件添加进 RTSys 项目中。字体文件名后缀为“.ttf”/“.zft”。



第十章 程序下载运行


程序下载（RAM/ROM）

根据第一章的操作说明，创建项目后即可对程序进行编辑，编辑好的程序必须下载到控制器上才能够实现设备的运行。下载到控制器有两种方式：下载 RAM 和下载到 ROM。
下载到 RAM：程序项目下载到控制器的 RAM 上，下载成功立刻运行，掉电后当前下载的项目会丢失。下载到 ROM：程序项目下载到控制器的 FLASH 上，下载成功立刻运行，掉电后当前下载的项目保持。
程序下载操作方法：创建项目→新建文件→选择文件类型→设置自动运行任务号→打开文件→编辑程序→连接到控制器→下载到 RAM/ROM。
注意：
必须创建项目后才支持下载程序。
 程序报错 error 时无法成功下载，ZMC0 系列部分型号不支持下载到 RAM。
文件较大的时候建议先点“编译所有”再下载到控制器，这样下载速度会比直接下载到控制器快很多。小文件下载过程此操作可以省略。（“编译所有”功能目前预留，暂不支持使用）

 程序自动运行

RTSys 连接控制器/仿真器运行程序时，RTSys 支持设定此时程序是否自动运行，以及自动运行的任务号。通过文件列表右边的任务号进行设置，每种型号的控制器可以运行的最大任务数量不同，请查看“控制器状态”或对应的用户手册。
每个项目都需要设置至少一个自动运行任务号，不设置自动运行任务号程序下载后无法运行，打印信息：WARN: no program set autorun.
注意：
1.一般在下载程序之前设置好自动运行任务号。
2.一个项目文件最好只设置一个自动运行任务号，其他任务使用 RUNTASK 指令或 RUN 指令开启。
3.同个项目中不可设置相同的任务号，且任务号不分优先级，仅作为标识区分。



ZAR 下载

通过生成专门的 ZAR 加密下载文件，可以实现独立的程序下载，这样可以把下载文件传给终端客户而不用担心程序泄密，文件后缀.zar。RTSys 中还提供支持绑定控制器 ID 的功能（控制器 ID 是出厂时控制器的唯一序列号），绑定后该 ZAR 文件仅供此控制器使用。另外当项目中含有.c 文件，需要选择编译平台或自定义 gcc 编译选项。
控制器 ID 查看方法：
1.提前连接好控制器，在菜单栏“控制器”→“控制器状态”中查看 ControllerID 信息；
2.连接好控制器后，在[命令与输出]窗口的“在线命令”窗口输入：?SERIAL_NUMBER ，点击“发送”即可打印出控制器 ID。
生成 ZAR 文件操作方法：
1.调试完成程序，通过菜单栏“控制器”→“生成 ZAR 文件”打开窗口，用户自行选择采用密码绑定方式或绑定控制器 ID 的方式进行加密生成，即在该项前进行勾选。（也支持两种同时选择，但下载时需两者同时满足才可下载）

2.若选择密码绑定方式则勾选当前项并在输入框中设置密码。（密码支持字母、数字及“_”等部分特殊
符号，最多可设置 16 个字符）当不确定输入的密码是否与自己所想一致时，可取消勾选输入框后的“***”。



3.若选择绑定控制器 ID 方式，则勾选当前项，并在输入框中输入控制器 ID。（每个控制器的 ID 都不同，查看方法请参考本节第二段内容）

4.设置好加密方式后，点击 Zar 文件项的“浏览”，选择 Zar 文件的保存路径后，点击“确定”即可。




注意：
密码采用不可逆算法，一旦遗忘，将无法知晓！请务必记好密码！每个控制器都有唯一的 ID，不可修改！

下载 ZAR 文件操作方法（2 种）：
(一) 在 RTSys 中下载 ZAR 文件
ZAR 文件是将整个项目进行加密打包，因此下载 ZAR 文件时不需要在项目中进行下载。
1.打开RTSys 软件，连接至对应的控制器（若该 ZAR 文件已绑定控制器 ID，则需连接绑定的控制器）。
2.使用APP_PASS 指令对密码进行校验。在[命令与输出]窗口中的“在线命令”栏输入: APP_PASS(密码)，点击“发送”即可。（指令括号中的密码即为用户设置的字符密码）


3.在菜单栏“控制器”→点击“下载 ZAR 文件”。弹出如下窗口，找到存放 ZAR 文件的路径，选择
ZAR 文件后点击“打开”。




4.若密码或控制器 ID 正确，则在[命令与输出]窗口打印如图的信息则表示下载成功。（若使用绑定控制器 ID 方式，则下载 ZAR 程序时自动校验控制器 ID，ID 一致 ZAR 程序才能成功下载到控制器。）


5.若下载失败则弹出如下窗口，此时需检查输入的密码是否正确或控制器 ID 是否与当前连接的控制器
ID 一致。




(二) 使用U 盘加载 ZAR 文件
打开 RTSys 软件，先连接至控制器，将存有ZAR 文件的 U 盘插到控制器的 USB 口上。
使用APP_PASS 指令对密码进行校验。在[命令与输出]窗口中的“在线命令”栏输入: APP_PASS(密码)，点击“发送”即可。（指令括号中的密码即为用户设置的字符密码）


使用 FILE 指令的“LOAD_ZAR”功能加载 U 盘里的 ZAR 文件执行。在[命令与输出]窗口中的“在线命令”栏输入:FILE "LOAD_ZAR","filename"，点击“发送”即可下载。
注：指令中的“filename”即 ZAR 文件名，文件名必须为英文字符才可下载！



编译 Lib

此功能同 ZAR 下载一样属于程序加密的范畴，“编译为 Lib”功能可以把一个程序文件编译为一个库文件后保存，便于程序保密或防止修改，库文件的文件名后缀为“.zlb”，库文件仅能显示全局 SUB 定义。
编译 Lib 文件操作方法：
1.程序调试完成后，点击菜单栏“文件”→“编译 Lib”后选择需要编译为 Lib 的程序文件，将其编译成一个库文件。（支持同时编译多个程序文件）
2.选择已编译好的 Lib 文件保存到目标路径下即可。

下载 Lib 文件的方法：
1.打开/新建一个项目文件（.zpj），将已编译的 Lib 文件重新添加到项目中，在软件左侧的[工程视图]窗口，单击鼠标右键后点击“添加到项目”，如下左图。
2.找到保存 Lib 文件的目标路径，选择 Lib 文件后点击“打开”即可添加到当前项目中，如下右图所示。
3.给 Lib 文件设置自动运行任务号，点击“下载到 RAM/ROM”即可将 Lib 文件重新下载到控制器。



打开此时Lib 格式的程序可以看到程序主体定义的声明，只有全局定义可以查看，但无法看到局部变量定义和 SUB 子函数具体过程，适合多人合作开发时子程序的保密。


 控制器程序比较

该功能可查看目前 PC 端的程序文件以及控制器端的程序文件分别有哪些，并支持把当前项目程序与控制器程序进行比较，判断程序是否一致。通过菜单栏“控制器”→“比较控制器”进行操作。比较控制器界面如下图：

第一列显示当前 PC 端打开的项目程序文件，第三列则显示已下载至控制器端的所有程序文件，最后一列则显示比较结果，程序相同显示“YES”，程序不同显示“NO”。
为程序保密，控制器不支持程序上传。



第十一章 右键快捷菜单


RTSys 右键

(一) 视图窗口右键
在 RTSys 界面中选择任意视图窗口，单击右键可弹出如下图的快捷菜单。菜单内容主要对窗口的位置进行选择。
浮动：将当前选中窗口切换到“浮动”形式，即浮于 RTSys 界面上，可随意拖拽换位置。停驻：将当前选中窗口固定到 RTSys 软件界面的默认位置中。
自动隐藏：将当前选中窗口隐藏到 RTSys 界面边缘，并形成一个小标签，鼠标指到该标签时弹出窗口，不指向时则隐藏。
隐藏：将当前选中窗口隐藏，即关闭该窗口显示。
注：取消隐藏的方法在菜单栏“视图”中重新点击该窗口即可停驻显示。

(二) 程序文件右键
在 RTSys 中打开程序文件后，在程序文件名标签处单击右键可弹出如下图的快捷菜单。关闭：在 RTSys 中关闭当前选中文件。
保存：保存当前选中文件。
关闭所有：关闭当前已打开的全部文件。
注：关闭的文件可在[工程视图]中找到对应文件双击重新打开。（若无文件则需手动添加文件到当前项目中）




Basic 右键

在 Basic 编辑窗口中，单击右键可弹出如下图的快捷菜单。通过右键菜单，可以做各种特殊编辑操作。右键中的大部分快捷方式在菜单栏中均能找到。
跳到定义处：选择 SUB 子函数时单击右键点击“跳到定义处”即可快速跳转到定义该 SUB 子函数的位置，可查看该函数内容。
选择全部：选中当前编辑界面中的所有内容。
增加到监视：启动调试功能后，将选中变量添加到监视窗口可实时查看该变量的值变化。




Plc 右键
在 Plc 编辑窗口中，单击右键可弹出如下图的快捷菜单。右键中的大部分快捷方式在菜单栏中均能找到。


写入值：启动调试模式后，手动修改软元件/寄存器的值状态。如下右图所示。
跳到定义处：选择 SUB 子函数时单击右键点击“跳到定义处”即可快速跳转到定义该 SUB 子函数的位置，可查看该函数内容。
插入一行：在当前行上方插入空白行；删除一行：删除当前行并下行上移列插入：在当前列的左边插入空白列；
扩展一列：在 Plc 窗口中最末尾处扩展一列（即虚线部分）

批注功能
编辑批注：用于对 Plc 文件中的寄存器进行批注，方便区分每个寄存器的用处。选中需要批注的软元件，单击右键选择“编辑批注”，即可对当前软元件进行注释。双击空白处即可输入，添加后点击“确定”即可保存。
显示批注：用于在 Plc 程序中在软元件下方显示已编辑的批注内容。




Hmi 右键

在 Hmi 编辑窗口中，单击右键可弹出如下图的快捷菜单。右键中的大部分快捷方式在菜单栏中均能找到。
属性：弹出当前选中元件的属性窗口，可查看/修改元件属性。选择全部：将当前Hmi 中所有元件选中。



第十二章 RTSys 显示设置


 状态栏

状态栏显示三部分内容，可用于光标定位、打印消息统计、控制器型号/IP/状态显示。
一、光标定位，显示光标处的行列等信息（例如 Basic，行 35，列 15，字符数 318 表示光标前程序有 17行，光标在该行第一个字符后，首行到光标处字符总数 394；例如 PLC，ROW:30,Col:2 表示当前光标停留在第 30 行的第 2 个网格处）。
二、统计当前命令与输出窗口的错误信息、告警、打印信息的条数。
三、显示当前连接的控制器型号、控制器 IP 地址以及控制器运行状态，出现 ALM 或者 ERROR 右下角会提示哪个轴出错或者系统出错，并用红色背景突出显示。
没有连接到控制器显示红色字体“没有连接”：


控制器正常运行显示“运行”状态：



程序不运行或急停后显示“待机”状态：


按下调试菜单“暂停”按钮，程序暂停扫描：


运行过程中出现警报状态栏“红色闪烁”提示：

上图就可以看到轴 0 axis alarm，客户就可以针对性去检查 axis0 axisstatus 的状态或查看打印信息提示。

 对齐线

对齐线用于在Basic 程序中有多层嵌套缩进时将同等缩进量的程序行对齐，使得程序更有层次化和更加规范化。通过该功能勾选可选择在 Basic 程序编辑界面时是否显示对齐线。如下图箭头所示即为对齐线。


 自动换行
自动换行即对 RTSys 软件窗口缩放时，Basic 程序内容会根据窗口的大小自适应进行换行。用户可自行选择是否勾选该功能。




 主题风格

RTSys 支持多种主题风格切换，包括：浅色、深色、深灰色、彩色 4 种主题颜色切换。用户可根据喜好选择已搭配好的主题，选择后立即生效。若无喜好的主题支持用户自定义设置，参考窗口自定义设置章节内容。下图以深色主题为例。



 语言切换


RTSys 支持语言切换，当前 RTSys 版本支持中文和英文。若需使用其他语言，用户可自行制作添加。通过“视图”-“语言”进行语言的选择（需重启生效），选择后自动弹出窗口提示需要重启 RTSys，点击 “确定”自行关闭 RTSys 软件再重新打开即可生效。

用户自行添加语言的操作方法：
1.打开 RTSys 软件的安装路径（可在桌面找到 RTSys 图标单击右键选择“打开文件所在位置”）。在
RTSys 目录下找到“Language”文件夹，如下图所示。




2.打开“Language”文件夹，该文件夹中已有英文和中文的文件。若需添加其他语言，则可复制其中一个
zlang 文件重新命名为“zlang3”或“zlang4”…以此类推。
注意：1.文件命名必须为“zlang 数字”形式！
2.必须按已有文件的数字顺序依次命名（如：已有zlang1 和zlang2，则创建新文件则必须为 zlang3，不可跳过未有数字命名。）


3.重命名好新文件后，打开新文件（可选用“记事本”/“写字板”等方式打开），需要手动对文件内容进行修改，即语言替换。
例如：新语言要设置为日语，则打开“zlang3.dat”文件后，需手动将文件中对应位置的内容替换为日语后保存即可。如下图所示。



4.重启 RTSys 软件即可在菜单栏“视图”→“语言”下拉菜单处选择新增语言。修改过的图标文字将显示为对应语言。如下图所示。



 字体设置


RTSys 支持用户自行修改 Basic 和 Plc 程序字体（默认宋体）。目前修改西文字体仅对英文字符生效，中文字符不生效。可通过菜单栏“视图”-“字体”打开如下窗口进行调整。






 窗口自定义设置


RTSys 支持对 RTBasic 和梯形图及语句表的编程界面显示风格进行自定义设置。提供了丰富的设置项供用户自定义。可自定义设置背景颜色、光标颜色、行号颜色等，还可具体设置函数颜色、变量颜色、注释颜色等。其中轴参数用以选择“轴参数”视图显示参数情况。






 重置窗口布局


用于将 RTSys 软件的窗口布局重置到默认位置。用户自行调整窗口位置后若需恢复初始位置只需在菜单栏“视图”→点击“重置窗口布局”，提示需重启 RTSys 软件后点击“确定”，手动关闭软件后重新打开即可生效。



第十三章 常见问题

程序运动出错后，RTSys 软件会显示出错信息，如果出错信息没有看到，可以通过命令行输入?*task 再次查看出错信息，双击出错信息可以自动切换到程序出错位置。


	2.检查驱动器的双脉冲配置是否与控制器一致，控制器脉冲模式的配置参数为 INVERT_STEP。
3.检查电机接线，重点查方向线。





非中国大陆操作系统使用 RTSys 中文版出现乱码	原因：系统不同导致编码格式中文无法识别。解决方法：
1.在电脑上“win+R”输入“cmd”打开命令提示符界面；
2.输入命令：chcp，即可得到该系统的代码页编号。如大陆系统代码页默认为 936。
3.得到代码页编号后，到 RTSys 安装文件目录下打开“Language”
文件夹→打开“ zlang2.dat” （简体中文） 文件→找到代码行 “Lang=936”→将 936 改为刚刚 cmd 查到的代码页编号。
RTSys 打开项目后连接 7 系控制器报
20020 错误码/
升级 FPGA 固件时出现 RT 死机	原因：MotionRT7 配置的 Total Memary 内存太大，超过了系统内存的大小；
解决方法：在 MotionRT7 中把Total Memary 内存改小即可。

示波器出现异常	1.导入非示波器参数的.txt 文件;
2.导入的示波器参数文件内容被修改；


打开 xplcterm 报错open lcd failed	1.?set_xplcterm 打印查看是否自启动显示屏，打印结果为 1 则已经自启动，设置为 0 在重启控制器解决；
2.是否多开 xplcterm 屏，如果用 RTSys 自带的 xplc screen 需要打
开插件管理点击立即重置。



附录 A：菜单一览表

以下菜单栏选单部分名称含有超链接！

文件选单

名称	图示	说明
新建文件	   建立新的工程文件并选择文件	类型（不同编程语言对应不同文件类型）
打开已有文件	   打开已有的工程文件（仅支持打开.bas / .hmi / .plc 格式）
关闭文件	   关闭当前打开的工程文件
关闭所有文件	   关闭所有已打开的工程文件	
保存文件	   将当前的工程文件保存至当前项	目路径下
另存为	   将当前的工程文件另存至其他路径下
保存所有	   保存所有已创建的工程文件至当前项目路径下

编译 lib	   将当前打开的工程文件编译为.zlb 文件（仅支持 Basic 文件和 PLC 文件）
新建工程	   建立新的工程项目并保存至对应路径下
打开工程	打开已建立的.zpj 格式的工程文件



常用选单

名称	图示	说明
		文件
新建文件	   建立新的工程文件并选择文件类型（不同编程语言对应不同文件类型）


	
打开已有文件	打开已有的工程文件（仅支持打开.bas / .hmi / .plc 格式）
保存文件	将当前的工程文件保存至当前项目路径下
保存所有	保存所有已创建的工程文件至当前项目路径下
	控制器
连接	连接到控制器/仿真器
断开连接	断开与控制器/仿真器的连接
下载到 RAM	将程序项目下载到控制器/仿真器的 RAM 中，掉电不保存
下载到 ROM	将程序项目下载到控制器/仿真器的 FLASH 中，掉电保存
	编辑
只读	打开/关闭只读模式（仅对 basic 和 plc 文件可用）
向后导航	   跳转到上次打开的文件页面位置
向前导航	   还原跳转后的文件页面位置
添加注释	   在 basic 程序文件中添加注释
删除注释	   取消 basic 程序文件中当前选中行的注释
撤销	   撤销上一次的操作
还原	   还原上一次的撤销动作
	常用工具
示波器	用于将数据转化为图像显示，可显示不同信号的图像
寄存器	批量查看控制器不同寄存器类型中的数值
	调试
启动/停止调试	用于追踪程序运行
紧急停止	紧急停止程序和所有轴的运动
	帮助
帮助文档	提供多个帮助文档方便查看，或在 basic/plc 文件选中某个指令按下 F1




键在右侧帮助窗口快捷查看




控制器选单

名称	图示	说明
	控制器	
连接	   连接到控制器/仿真器	
断开连接	   断开与控制器/仿真器的连接	
下载到 RAM	   将程序项目下载到控制器/仿真器的 RAM 中，掉电不保存
下载到 ROM	   将程序项目下载到控制器/仿真器的 FLASH 中，掉电保存

控制器状态	查看连接的控制器状态信息，包括控制器基本信息、ZCan 节点状态、
槽位节点状态、通讯配置等
固件升级	   对控制器现有固件版本进行升	级更新
系统时间	   查看控制器当前时间，支持自定义控制器时间或同步 PC 时钟
修改 IP 地址	   修改控制器 IP 地址，支持查看控制器当前 IP 地址
比较控制器	   比较当前 pc 项目的文件跟控制器里面的文件是否一致
锁定控制器	   对控制器采取密码锁定，锁定	后上位机程序无法下载至控制器中
解锁控制器	   对已锁定的控制器解锁，需输入正确的密码才可解锁
控制器复位	   重新启动控制器，重启后软件	需要重新手动连接上
	项目	
编译所有	   对项目中的所有文件进行编译	，但不下载进控制器
增加到项目	   添加文件到当前项目中，支持添加程序文件、字体文件、图片等
项目设置	   预留

生成 ZAR 文件	生成专门的ZAR 加密下载文件，可采用密码加密方式或绑定控制器 ID
的方式，文件后缀为.zar


	
下载 ZAR 文件	将 ZAR 加密文件下载到控制器 ROM 中
注释	对项目文件中的寄存器做批注，支持查看系统寄存器的作用
指示灯	打开/关闭已连接的控制器上的 ALM 灯


编辑选单

名称	图示	说明
	编辑	
粘贴	   将剪贴板中的内容粘贴至项目文件中
剪切	   从项目文件中剪取选定的程序	内容/元件暂时存放至剪贴板中
复制	   复制项目文件中的选定内容暂时存放至剪贴板中
删除	   删除项目文件中所选内容	
添加注释	   将项目文件中所选行整行添加为注释
删除注释	   删除项目文件中所选行的注释	
插入制表符	   对项目文件中光标所在行的首端插入一个制表符
删除制表符	   对项目文件中光标所在行删除	一个制表符
跳转到上一个位置	   跳转至上次所在位置	
跳转到下一个位置	   跳转至下一个位置	
撤销	   撤销上一次的操作	
还原	   还原上一次的撤销动作	
只读	   打开/关闭只读模式（仅对 basic 和 plc 文件可用）
	书签
设置/取消书签	   对项目文件中所选单行设置/取消书签


	
上一个书签	   跳转到同一项目中的上一个书签
下一个书签	   跳转到同一项目中的下一个书签
编辑书签	   查看项目中已设置的书签所在文件及行号，支持对书签进行操作
	查找/替换
查找	   对项目文件中指定内容进行查找（查找范围可选）
替换	   对项目文件中指定内容进行替换（替换范围可选）


视图选单

名称	图示	说明
		窗口
轴参数	打开/关闭[轴参数]窗口，可又次窗口监控运动控制中常见的参数。

标签	    打开/关闭[标签视图]窗口，可查看所有 basic 文件中定义的 SUB 函数



监视	打开/关闭[监视]窗口，启动调试后显示。可监视变量、寄存器等的数值







主题风格		切换软件显示风格（共 4 个风格可选）
自定义



重置窗口布局	



	设置窗口自定义样式（共 4 个窗口可设置）

重置

恢复软件默认的窗口布局，重启生效

工具选单		
名称	图示	说明
示波器	
	监测/调试运行中的程序，将程序中参数数据变化转化为图形显示
手动运动	
	设置轴参数对电机直接进行手动操作
输入口	
	实时监测输入口状态
输出口	
	实时监测输出口状态
寄存器	
	实时监测各类寄存器值的变化
锁存图像	
	用于显示查看锁存通道内的图像或更换锁存通道图像
AD/DA	
	监测 AD/DA 数值变化
故障诊断	
	监测控制器状态及显示故障诊断信息
总线状态诊断	
	对 ETHERCAT、RTEX 总线状态进行诊断并显示诊断信息
插件	
	添加自定义小插件，默认已有“xplc screen” HMI 仿真插件



调试选单

名称	图示	说明
启动/停止调试	   对程序和任务	启动或停止调试、监测功能
运行	   对启动调试功能后的程序进行运行


	
暂停	   暂停运行在调试监测中的程序
运行到	   设置运行到指定程序行
单步进入	   跳到下一条语句
单步跳过	   跳过下一条语句
单步跳出	   跳出 SUB 子程序运行
断点	   在 Basic 程序增加或删除断点
紧急停止	   紧急停止程序和所有轴的运动


PLC 选单

名称	图示	说明
取指令	   用于与母线连接的常开触点	
取反指令	   用于与母线连接的常闭触点	

取脉冲上升沿指令	用于检测与母线连接的常开触点的
沿时（由OFF→ON 变化时）接通	上升沿，仅在指定位软元件的上升
一个扫描周期

取脉冲下降沿指令	用于检测与母线连接的常开触点的下降沿，仅在指定位软元件的下降
沿时（由 ON→OFF 变化时）接通一个扫描周期
步进开始指令	   使用步进梯形图指令程序的起始指令

比较指令	用于两个数据之间的数据比较，将操作数 S1、S2 按指定条件进行比
较，满足条件触点导通，不满足条件触点闭合。
输出指令	   软元件线圈驱动的指令
函数	   打开 PLC 指令输入表，选择指令
子程序	   建立 PLC 子函数，作为子函数的	入口
横线	添加梯形图横线	



删除横线	删除梯形图横线
删除竖线	删除梯形图竖线
转换为语句表	将梯形图转换为语句表
转换成梯形图	将语句表转换成梯形图
寄存器使用列表	查询当前项目下各类寄存器的使用情况和寄存器注释
交叉参照表	查询当前项目下各类寄存器的使用和存在位置
插入一行	在选中窗格上方，插入一行



HMI 选单

名称	图示	说明
新建窗口	
	新建一个Hmi 窗口
导入窗口	
	导入已有的 Hmi 窗口（仅支持.hmi 格式）
背景预设	
	预设全局窗口背景及元件样式
显示缩略图	/	组态视图显示为窗口缩略图
显示详细信息	/	组态视图显示窗口及元件详细信息
控件箱	
	打开/隐藏[控件箱]窗口，存放 HMI 所有元件，可从控件箱直接调用
文本库	
	一次设置多种语言文本内容并在元件调用

图片库	
	添加图片到图片库并支持调用，分为系统图片库和用户图片库，图片
仅供Hmi 使用
按键转换	
	将物理按键与虚拟键功能绑定

排列	
/	对多个元件进行排列，包括左对齐、右对齐、上对齐、下对齐、水平/垂直居中对齐、水平/垂直相同间距、相同宽度/高度/尺寸、窗口水平/
垂直居中显示


		
批量修改地址	
	批量修改寄存器地址
Hmi 设置	
	对 Hmi 系统预设置，包括设置起始窗口或分辨率等
属性	
	打开/关闭[属性]窗口，可查看/设置 HMI 元件/窗口属性信息

快捷图片库	
	打开/关闭[快捷图片库]窗口，可查看 HMI 图片库内容，并快速应用到
HMI 元件上或删除元件上已应用样式
显示/隐藏图层	
	显示/隐藏顶层、中层、底层的元件
栅格	/	显示/隐藏栅格
元件名称	/	Hmi 窗口中显示/隐藏元件名称
语言	
	切换文本库中的语言
状态	
	切换元件状态


附录 B：RTSys 快捷键


操作	快捷键
Plc 快捷键
LAD 增加横线	F8
LAD 删除横线	Ctrl+F8
LAD 增加竖线	F9
LAD 删除竖线	Ctrl+F9
LAD 插入一行	Shift+Insert
LAD 删除一行	Shift+Del
转换成 IL	Ctrl+I
转换成 LAD	Ctrl+L
控制器连接/断开
连接控制器	Ctrl+Alt+C
连接仿真器	Ctrl+Alt+S
断开连接	Ctrl+Alt+D
编辑
跳转到定义处	F12
跳转到上一个位置	Ctrl+-（或 Ctrl+小键盘-）
跳转到下一个位置	Ctrl++（或 Ctrl+小键盘+）
设置/取消书签	Ctrl+F2
编辑书签	Ctrl+M
上一个书签	Shift+F2
下一个书签	F2
查找	Ctrl+F 或 Shift+F4（之前为整个项目中查找，已合并）
替换	Ctrl+H
调试
增删断点	F9
启动/停止调试	Ctrl+F5
调试的运行	F5
运行到	Ctrl+F10


单步进入	F11
单步跳过	F10
单步跳出	Shift+F11
剪切、复制、粘贴、删除、全选、撤销、
恢复、打开、保存等	
通用的快捷键
帮助（不同的状态弹不同的帮助）	F1
界面操作

连接控制器界面	回车键为连接（根据当前设置的参数自动选择一种方式连接控制
器）
命令与输出界面	输入栏↑及↓按钮可翻动输入历史，回车键为发送
组态界面缩略图方式	Del 为删除、↑及↓为选中上一个、下一个
标签界面	回车键为跳转
示波器界面	↑、↓、←、→键为查看的波形
HMI 绘图	ESC 键为取消绘制

HMI 界面	↑、↓、←、→键移动选中控件 1 个像素的，加 Shift 为快速移动
（2 像素）、加 Ctrl 为对齐控件

LAD 界面	↑、↓、←、→键切换选中的格子，加 Shift 键为增加/减少选中的区域、Home 键为回到第一列、End 键为回到最后一列、PgUp 键
为跳到第一行、PgDn 为跳到最后一行、回车键为输入



附录 C：EtherCAT 配置操作指引

各窗口及菜单介绍请参考本手册工程视图章节！

RTSys 有无创建工程均可按流程配置。
（注意：无工程情况下配置好相关参数后需要导出保存；有工程时相关配置参数则可保存在该工程中）

有实际驱动设备
1.RTSys 连接上控制器。
2.开启轴配置及EtherCAT 配置功能。（右键单击 RTSys"工程视图"空白处→选择"工程设置"→勾选"
启用轴配置及 EtherCAT 配置"→点击"确定"）

3.添加驱动器设备.xml/.zml 文件到配置文件。（若已知驱动器是控制器和 RTSys 软件能识别的型号，则可跳过此步；或无需配置驱动器参数可跳过此步）
RTSys 工程视图→右键单击"配置文件"→选择"增加到配置文件"→选择目标.xml/.zml 文件添加即可。
(注：xml 文件添加会自动转为 zml 文件)



4.添加 xml/zml 文件后，点击“控制器”→“下载到 RAM/ROM”，将文件下载到控制器中，使控制器识别该设备。（若需修改 zml 文件配置，在工程视图中双击该文件修改，修改后重新下载即可）



















5.添加驱动器设备.xml/.zml 文件到 EtherCAT。RTSys 工程视图→右键单击“控制器”→“添加到 xml/zml 列表”。（该步骤目的是使 RTSys 能识别驱动器设备，用于需要在软件对驱动器设备进行轴配置等情况，否则可跳过。）



RTSys 不识别表现为：扫描设备后仅显示“Drive n”且无法显示对应设备型号，如下图所示。
6.扫描设备。RTSys 工程视图→右键单击"EtherCAT 节点/EtherCAT-0"→选择"扫描设备"。（若有多槽位号时则选择对应槽位号扫描设备，如"EtherCAT-0"、"EtherCAT-1"等）
若出现扫描不到设备且报错："Online command warn, Slot return error:3205."则请参考"扫描不到设备操作流程"。



7.配置总线周期、轴映射关系、数字量 IO、模拟量映射关系。RTSys 工程视图→双击"控制器"→手动进行各参数配置。
（以下介绍“轴映射关系窗口”操作，其他 IO、模拟量映射关系操作同理） “轴映射关系”界面操作步骤：
Tip1：当控制器支持多种轴类型时，需要对轴资源进行分配及映射。如下图所示，扫描设备后读取到的控制器（脉冲轴）数为 6 个，ZCan 节点扩展轴数为 0，EtherCAT-0（总线轴）数为 2。
Tip2：当已打开“控制器”窗口，后续有新增设备等操作，需要重新双击工程视图中的“控制器”刷新读取。




















(1).先对轴资源数量进行分配，在“EtherCAT-0”类中的“拨码号”列修改总线轴数为 10。（该例子连接的是支持 16 轴的控制器，因此可增加轴数）




(2).对各轴进行轴号映射分配和轴类型设置。设置轴编号在“映射轴号”列进行手动设置；设置轴类型在“轴类型”列进行设置。（除了手动设置也可点击“自动配置”一键分配轴号、轴类型及 IO 等）
注意：连接正运动EIO、ZIO 系列扩展模块，扩展轴只能配置为脉冲轴类型。

8.配置/查看各个 EtherCAT 总线设备的 PDO、启动参数等。RTSys 工程视图→双击"Drive n"→手动进行各参数配置。（Tip："Drive n”指已扫描到的单个设备编号，如："Drive 0"、"Drive1"等）



9.配置轴相关基本参数及开关信号等。RTSys 工程视图→单击"轴配置"小箭头→打开轴列表→双击选择需要配置的轴号→进行轴参数等相关配置。

10.总线启动。启动多个槽位号上设备：RTSys 工程视图→右键单击"EtherCAT 节点"→"总线启动"；启动同个槽位号上所有设备：RTSys 工程视图→右键单击"EtherCAT-0"→"总线启动"；（若有多槽位号时则选择对应槽位号扫描设备，如"EtherCAT-0"、"EtherCAT-1"等）




11.应用生成总线初始化"Startup.bas"文件（非必要步骤），生成该文件后可下载至控制器，适用于脱机运行时。RTSys 工程视图→双击"轴配置"或"控制器"或"Drive n"→右下角"应用"；
（若有其他内容需要补充进“Startup.bas”文件，则可选择工程下的文件单击右键→选择“Startup.bas 设置”）


无实际驱动设备
注意：无实际设备接线无法启动总线！
1.RTSys 连接上控制器。
2.开启轴配置及EtherCAT 配置功能。（右键单击 RTSys"工程视图"空白处→选择"工程设置"→勾选"启用轴配置及EtherCAT 配置"→点击"确定"）


3.手动添加设备。右键单击"EtherCAT-0"→"添加设备"→选择需要添加的设备型号及设备数量→点击"新增"即可。（若有多槽位号时则选择对应槽位号扫描设备，如"EtherCAT-0"、"EtherCAT-1"等）


4.查无目标驱动厂商设备则需添加驱动设备.xml 或.zml 文件。（若有目标驱动设备则跳过第 2,3 步，直接
选择后新增后看第 4 步）
操作方法：打开 RTSys 软件文件路径→打开"EtherCAT"文件夹→将目标驱动设备的.xml 或.zml 文件放置该文件夹中。


5.RTSys 中更新 xml/zml 列表。RTSys 工程视图→右键单击"控制器"→"更新 xml/zml"列表。

6.配置总线周期、轴映射关系、数字量 IO、模拟量映射关系。RTSys 工程视图→双击"控制器"→手动进


行各参数配置。
（分配好脉冲轴、总线轴数后可点击“自动配置”一键分配轴号和对应默认轴类型。）

7.配置/查看各个 EtherCAT 总线设备的 PDO、启动参数等。RTSys 工程视图→双击"Drive n"→手动进行各参数配置；（Tip："Drive n”指已扫描到的单个设备编号，如："Drive 0"、"Drive1"等）


8.配置轴相关基本参数及开关信号等。RTSys 工程视图→单击"轴配置"小箭头→打开轴列表→双击选择需要配置的轴号→进行轴参数等相关配置。



9.应用生成总线初始化"Startup.bas"文件（非必要步骤），生成该文件后可下载至控制器，适用于脱机运行时。RTSys 工程视图→双击"轴配置"或"控制器"或"Drive n"→右下角"应用"。
（若有其他内容需要补充进“Startup.bas”文件，则可选择工程下的文件单击右键→选择“Startup.bas 设置”）

Tip：按以上流程对设备进行参数配置后，可将设备参数配置导出应用到其他工程。
导出配置文件：轴或 IO 相关配置生成.ini 文件保存，EtherCAT 设备参数导出则生成.ini 和.zml 文件。
（RTSys 工程视图→双击"轴配置"或"控制器"或"Drive n"→右下角"导出"）
导入配置文件：.ini 文件可导入其他工程中，使其他工程可应用相同参数配置（RTSys 工程视图→双击 "轴配置"或"控制器"或"Drive n"→右下角"导入"）；.zml 文件则添加至配置文件下。（RTSys 工程视图→右键单击"配置文件"→选择"增加到工程"）


扫描不到设备操作流程
说明：扫描后报错"Online command warn, Slot return error:3205."表示控制器无法识别该驱动器，需要将配置后的.zml 文件与工程一起下载进控制器中。
1.手动添加设备。右键单击"EtherCAT-0"→选择"添加设备"→选择目标设备型号及设备数量→点击"新增"即可。（若有多槽位号时则选择对应槽位号扫描设备，如"EtherCAT-0"、"EtherCAT-1"等）

2.配置/查看各个 EtherCAT 总线设备的 PDO、启动参数等。RTSys 工程视图→双击"Drive n"→手动进行各参数配置。（Tip："Drive n”指已扫描到的单个设备编号，如："Drive 0"、"Drive1"等）



3.完成参数配置后，将该设备参数配置导出生成.zml 文件。（RTSys 工程视图→双击"Drive n"→右下角"
导出"。）

4.将生成的.zml 文件添加到目标工程下并下载进控制器即可。（RTSys 工程视图→右键单击"配置文件"→选择"增加到工程"）完成后需要进行轴配置请参考“有实际驱动设备”内容。





