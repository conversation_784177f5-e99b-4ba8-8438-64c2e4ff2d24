'=============================================================================
' AB信号回零完整示例程序 - 包含模式13测试
' 适用于不带Z信号的编码器回零
' 支持标准回零（模式3）和限位反找回零（模式13）
'=============================================================================

' 全局变量
GLOBAL total_axis            ' 轴数量
GLOBAL encoder_home_status(3) ' 各轴回零状态

' 主程序
total_axis = 3
CALL InitEncoderHome()

PRINT "=== AB编码器回零程序 ==="
PRINT "基础回零模式（模式3）："
PRINT "IN0 - 单轴回零（轴0）"
PRINT "IN1 - 多轴顺序回零"
PRINT "IN2 - 多轴同时回零"
PRINT ""
PRINT "限位反找模式（模式13）："
PRINT "IN3 - 模式13单轴回零（轴0）"
PRINT "IN4 - 模式13多轴顺序回零"
PRINT "IN5 - 模式13多轴同时回零"
PRINT ""
PRINT "系统功能："
PRINT "IN6 - 状态检查"
PRINT "IN7 - 回零验证"

WHILE 1
    ' 基础回零模式（模式3）
    IF SCAN_EVENT(IN(0)) > 0 THEN
        CALL DoSingleHome(0, 3)
    ENDIF

    IF SCAN_EVENT(IN(1)) > 0 THEN
        CALL DoSequentialHome(3)
    ENDIF

    IF SCAN_EVENT(IN(2)) > 0 THEN
        CALL DoSimultaneousHome(3)
    ENDIF

    ' 限位反找模式（模式13）
    IF SCAN_EVENT(IN(3)) > 0 THEN
        CALL DoSingleHome(0, 13)
    ENDIF

    IF SCAN_EVENT(IN(4)) > 0 THEN
        CALL DoSequentialHome(13)
    ENDIF

    IF SCAN_EVENT(IN(5)) > 0 THEN
        CALL DoSimultaneousHome(13)
    ENDIF

    ' 系统功能
    IF SCAN_EVENT(IN(6)) > 0 THEN
        CALL ShowHomeStatus()
    ENDIF

    IF SCAN_EVENT(IN(7)) > 0 THEN
        CALL VerifyAllHome()
    ENDIF

    DELAY(50)
WEND
END

' 编码器回零系统初始化
GLOBAL SUB InitEncoderHome()
    FOR i = 0 TO total_axis - 1
        BASE(i)
        ATYPE = 4                ' 脉冲+正交编码器
        UNITS = 1000             ' 脉冲当量
        SPEED = 200              ' 回零速度
        CREEP = 20               ' 爬行速度
        ACCEL = 1000             ' 加速度
        DECEL = 1000             ' 减速度
        HOMEWAIT = 20            ' 反找等待时间
        
        ' 信号映射
        DATUM_IN = 8 + i         ' 原点开关
        FWD_IN = 11 + i          ' 正限位
        REV_IN = 14 + i          ' 负限位
        
        ' 信号反转（ZMC系列OFF有效）
        INVERT_IN(8 + i, ON)
        INVERT_IN(11 + i, ON)
        INVERT_IN(14 + i, ON)
        
        ' 编码器设置
        ENCODER_RATIO(1, 1)      ' 1:1比例
        
        encoder_home_status(i) = 0    ' 初始化为未回零
    NEXT
    
    PRINT "AB编码器回零系统初始化完成"
    PRINT "ATYPE=4: 脉冲+正交编码器"
    PRINT "支持模式3（标准回零）和模式13（限位反找回零）"
END SUB

' 单轴回零（支持不同模式）
GLOBAL SUB DoSingleHome(axis_num, home_mode)
    IF home_mode = 3 THEN
        PRINT "=== 开始轴", axis_num, "标准回零（模式3）==="
        PRINT "运动过程：SPEED正向→碰到原点→CREEP反向→离开原点→停止"
    ELSEIF home_mode = 13 THEN
        PRINT "=== 开始轴", axis_num, "限位反找回零（模式13）==="
        PRINT "运动过程：SPEED正向→如碰限位则反向→找到原点→CREEP正向→离开原点→停止"
    ENDIF
    
    BASE(axis_num)
    encoder_home_status(axis_num) = 1  ' 设置为回零中
    
    ' 记录回零前状态
    PRINT "回零前状态："
    PRINT "  DPOS：", DPOS(axis_num)
    PRINT "  MPOS：", MPOS(axis_num)
    PRINT "  原点信号：", IN(8 + axis_num)
    PRINT "  正限位信号：", IN(11 + axis_num)
    
    DIM enc_stat
    enc_stat = ENCODER_STATUS(axis_num)
    PRINT "  编码器状态：", HEX(enc_stat)
    
    ' 清除轴错误状态
    DATUM(0) AXIS(axis_num)
    DELAY(10)
    
    ' 执行AB编码器回零
    PRINT "执行DATUM(", home_mode, ")回零..."
    DATUM(home_mode)
    WAIT UNTIL IDLE(axis_num) = -1
    DELAY(10)                    ' 状态稳定延时
    
    ' 检查结果
    IF AXISSTATUS(axis_num) = 0 THEN
        ' 回零成功 - DATUM指令已自动清零DPOS并纠正MPOS
        encoder_home_status(axis_num) = 2  ' 设置为已回零
        PRINT "✓ 轴", axis_num, "回零成功！"
        PRINT "回零后状态："
        PRINT "  DPOS：", DPOS(axis_num), "（已自动清零）"
        PRINT "  MPOS：", MPOS(axis_num), "（已自动纠正）"
    ELSE
        encoder_home_status(axis_num) = 3  ' 设置为回零失败
        PRINT "✗ 轴", axis_num, "回零失败！"
        PRINT "错误状态：", HEX(AXISSTATUS(axis_num))
        CALL AnalyzeHomeError(axis_num)
    ENDIF
    
    PRINT "=== 轴", axis_num, "回零完成 ==="
END SUB

' 多轴顺序回零（支持不同模式）
GLOBAL SUB DoSequentialHome(home_mode)
    IF home_mode = 3 THEN
        PRINT "=== 开始多轴顺序回零（模式3）==="
        PRINT "按轴号顺序依次标准回零..."
    ELSEIF home_mode = 13 THEN
        PRINT "=== 开始多轴顺序回零（模式13）==="
        PRINT "按轴号顺序依次限位反找回零..."
    ENDIF
    
    FOR i = 0 TO total_axis - 1
        PRINT "--- 开始轴", i, "回零 ---"
        CALL DoSingleHome(i, home_mode)
        IF encoder_home_status(i) <> 2 THEN
            PRINT "✗ 多轴顺序回零失败，停止在轴", i
            RETURN
        ENDIF
        DELAY(500)  ' 轴间延时，避免冲击
    NEXT
    
    IF home_mode = 3 THEN
        PRINT "✓ 所有轴标准顺序回零成功！"
    ELSEIF home_mode = 13 THEN
        PRINT "✓ 所有轴限位反找顺序回零成功！"
    ENDIF
END SUB

' 多轴同时回零（支持不同模式）
GLOBAL SUB DoSimultaneousHome(home_mode)
    IF home_mode = 3 THEN
        PRINT "=== 开始多轴同时回零（模式3）==="
        PRINT "所有轴同时启动标准回零..."
    ELSEIF home_mode = 13 THEN
        PRINT "=== 开始多轴同时回零（模式13）==="
        PRINT "所有轴同时启动限位反找回零..."
    ENDIF

    ' 设置所有轴状态为回零中
    FOR i = 0 TO total_axis - 1
        encoder_home_status(i) = 1
    NEXT

    ' 清除所有轴错误状态
    FOR i = 0 TO total_axis - 1
        DATUM(0) AXIS(i)
    NEXT
    DELAY(10)

    ' 同时启动所有轴回零
    FOR i = 0 TO total_axis - 1
        BASE(i)
        PRINT "启动轴", i, "回零（模式", home_mode, "）..."

        ' 显示回零前状态
        PRINT "  回零前 DPOS:", DPOS(i), " MPOS:", MPOS(i)
        PRINT "  原点信号:", IN(8 + i), " 正限位:", IN(11 + i)

        ' 执行回零指令
        DATUM(home_mode)
    NEXT

    PRINT "等待所有轴回零完成..."

    ' 等待所有轴回零完成
    WAIT UNTIL IDLE(0) = -1 AND IDLE(1) = -1 AND IDLE(2) = -1
    DELAY(10)  ' 状态稳定延时

    ' 检查所有轴的回零结果
    DIM all_success
    all_success = 1

    FOR i = 0 TO total_axis - 1
        BASE(i)
        IF AXISSTATUS(i) = 0 THEN
            ' 回零成功 - DATUM指令已自动清零DPOS并纠正MPOS
            encoder_home_status(i) = 2
            PRINT "✓ 轴", i, "同时回零成功"
            PRINT "  DPOS：", DPOS(i), "（已自动清零）"
            PRINT "  MPOS：", MPOS(i), "（已自动纠正）"
        ELSE
            ' 回零失败
            encoder_home_status(i) = 3
            PRINT "✗ 轴", i, "同时回零失败，状态：", HEX(AXISSTATUS(i))
            all_success = 0
        ENDIF
    NEXT

    ' 显示最终结果
    IF all_success = 1 THEN
        IF home_mode = 3 THEN
            PRINT "✓ 所有轴标准同时回零成功！"
        ELSEIF home_mode = 13 THEN
            PRINT "✓ 所有轴限位反找同时回零成功！"
        ENDIF
    ELSE
        IF home_mode = 3 THEN
            PRINT "✗ 部分轴标准同时回零失败！"
        ELSEIF home_mode = 13 THEN
            PRINT "✗ 部分轴限位反找同时回零失败！"
        ENDIF
        PRINT "请检查失败轴的状态"
    ENDIF
END SUB

' 回零状态检查
GLOBAL SUB ShowHomeStatus()
    PRINT "=== 编码器回零状态检查 ==="
    
    FOR i = 0 TO total_axis - 1
        PRINT "轴", i, "详细状态："
        PRINT "  回零状态：", encoder_home_status(i), "(0-未回零,1-回零中,2-已回零,3-失败)"
        PRINT "  ATYPE：", ATYPE(i)
        PRINT "  DPOS位置：", DPOS(i)
        PRINT "  MPOS位置：", MPOS(i)
        PRINT "  轴状态：", HEX(AXISSTATUS(i))
        PRINT "  编码器值：", ENCODER(i)
        
        DIM enc_stat
        enc_stat = ENCODER_STATUS(i)
        PRINT "  编码器状态：", HEX(enc_stat)
        PRINT "  原点信号：", IN(8 + i)
        PRINT "  正限位信号：", IN(11 + i)
        
        ' 运动状态
        IF IDLE(i) = -1 THEN
            PRINT "  运动状态：停止"
        ELSE
            PRINT "  运动状态：运动中"
        ENDIF
        
        PRINT ""
    NEXT
END SUB

' 验证所有轴回零
GLOBAL SUB VerifyAllHome()
    PRINT "=== 验证所有轴回零结果 ==="
    
    FOR i = 0 TO total_axis - 1
        CALL VerifyHome(i)
        PRINT ""
    NEXT
END SUB

' 验证单轴回零
GLOBAL SUB VerifyHome(axis_num)
    PRINT "验证轴", axis_num, "回零结果："
    
    BASE(axis_num)
    
    ' 检查1：轴状态正常
    IF AXISSTATUS(axis_num) = 0 THEN
        PRINT "✓ 轴状态正常"
    ELSE
        PRINT "✗ 轴状态异常：", HEX(AXISSTATUS(axis_num))
    ENDIF
    
    ' 检查2：位置已清零
    IF ABS(DPOS(axis_num)) < 0.1 AND ABS(MPOS(axis_num)) < 0.1 THEN
        PRINT "✓ 位置已清零"
        PRINT "  DPOS：", DPOS(axis_num)
        PRINT "  MPOS：", MPOS(axis_num)
    ELSE
        PRINT "⚠ 位置偏差"
        PRINT "  DPOS：", DPOS(axis_num)
        PRINT "  MPOS：", MPOS(axis_num)
    ENDIF
    
    ' 检查3：轴已停止
    IF IDLE(axis_num) = -1 THEN
        PRINT "✓ 轴已停止"
    ELSE
        PRINT "✗ 轴仍在运动"
    ENDIF
END SUB

' 回零错误分析
GLOBAL SUB AnalyzeHomeError(axis_num)
    DIM status_val
    status_val = AXISSTATUS(axis_num)
    
    PRINT "轴", axis_num, "回零错误分析："
    
    IF status_val AND 1 THEN
        PRINT "  ✗ 轴错误标志置位"
    ENDIF
    
    IF status_val AND 2 THEN
        PRINT "  ✗ 碰到正向硬限位"
    ENDIF
    
    IF status_val AND 4 THEN
        PRINT "  ✗ 碰到负向硬限位"
    ENDIF
    
    IF status_val AND 8 THEN
        PRINT "  ✗ 急停信号有效"
    ENDIF
    
    IF status_val AND 16 THEN
        PRINT "  ✗ 驱动器报警"
    ENDIF
    
    PRINT "故障排除建议："
    PRINT "  1. 检查编码器AB信号连接"
    PRINT "  2. 检查编码器供电（5V/24V）"
    PRINT "  3. 检查原点开关和限位开关连接"
    PRINT "  4. 确认ATYPE=4（正交编码器）"
    PRINT "  5. 检查信号屏蔽和接地"
    PRINT "  6. 模式13需要确保限位开关正常工作"
END SUB
